{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/security/headers.ts"], "sourcesContent": ["/**\n * Security Headers Configuration\n * Implements comprehensive security headers for production deployment\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\n\nexport interface SecurityConfig {\n  contentSecurityPolicy?: boolean;\n  strictTransportSecurity?: boolean;\n  xFrameOptions?: boolean;\n  xContentTypeOptions?: boolean;\n  referrerPolicy?: boolean;\n  permissionsPolicy?: boolean;\n  crossOriginEmbedderPolicy?: boolean;\n  crossOriginOpenerPolicy?: boolean;\n  crossOriginResourcePolicy?: boolean;\n}\n\nconst defaultConfig: SecurityConfig = {\n  contentSecurityPolicy: true,\n  strictTransportSecurity: true,\n  xFrameOptions: true,\n  xContentTypeOptions: true,\n  referrerPolicy: true,\n  permissionsPolicy: true,\n  crossOriginEmbedderPolicy: false, // Can break some integrations\n  crossOriginOpenerPolicy: true,\n  crossOriginResourcePolicy: true,\n};\n\n/**\n * Content Security Policy configuration\n */\nexport function getContentSecurityPolicy(isDevelopment = false): string {\n  const policies = {\n    'default-src': [\"'self'\"],\n    'script-src': [\n      \"'self'\",\n      \"'unsafe-inline'\", // Required for Next.js in development\n      \"'unsafe-eval'\", // Required for Next.js in development\n      'https://js.stripe.com',\n      'https://checkout.stripe.com',\n      'https://www.google-analytics.com',\n      'https://www.googletagmanager.com',\n      'https://connect.facebook.net',\n      'https://www.facebook.com',\n      ...(isDevelopment ? [\"'unsafe-inline'\", \"'unsafe-eval'\"] : []),\n    ],\n    'style-src': [\n      \"'self'\",\n      \"'unsafe-inline'\", // Required for Tailwind CSS\n      'https://fonts.googleapis.com',\n    ],\n    'img-src': [\n      \"'self'\",\n      'data:',\n      'blob:',\n      'https:',\n      'https://images.unsplash.com',\n      'https://via.placeholder.com',\n      'https://www.google-analytics.com',\n      'https://www.facebook.com',\n    ],\n    'font-src': [\n      \"'self'\",\n      'https://fonts.gstatic.com',\n      'data:',\n    ],\n    'connect-src': [\n      \"'self'\",\n      'https://api.stripe.com',\n      'https://checkout.stripe.com',\n      'https://www.google-analytics.com',\n      'https://analytics.google.com',\n      'https://www.facebook.com',\n      'https://connect.facebook.net',\n      'wss:',\n      ...(isDevelopment ? ['ws:', 'wss:'] : []),\n    ],\n    'frame-src': [\n      \"'self'\",\n      'https://js.stripe.com',\n      'https://checkout.stripe.com',\n      'https://www.facebook.com',\n      'https://www.youtube.com',\n    ],\n    'worker-src': [\n      \"'self'\",\n      'blob:',\n    ],\n    'child-src': [\n      \"'self'\",\n      'blob:',\n    ],\n    'object-src': [\"'none'\"],\n    'base-uri': [\"'self'\"],\n    'form-action': [\"'self'\"],\n    'frame-ancestors': [\"'none'\"],\n    'upgrade-insecure-requests': isDevelopment ? [] : [''],\n  };\n\n  return Object.entries(policies)\n    .filter(([_, values]) => values.length > 0)\n    .map(([key, values]) => `${key} ${values.join(' ')}`)\n    .join('; ');\n}\n\n/**\n * Permissions Policy configuration\n */\nexport function getPermissionsPolicy(): string {\n  const policies = {\n    'accelerometer': ['()'],\n    'ambient-light-sensor': ['()'],\n    'autoplay': ['()'],\n    'battery': ['()'],\n    'camera': ['()'],\n    'cross-origin-isolated': ['()'],\n    'display-capture': ['()'],\n    'document-domain': ['()'],\n    'encrypted-media': ['()'],\n    'execution-while-not-rendered': ['()'],\n    'execution-while-out-of-viewport': ['()'],\n    'fullscreen': ['(self)'],\n    'geolocation': ['()'],\n    'gyroscope': ['()'],\n    'keyboard-map': ['()'],\n    'magnetometer': ['()'],\n    'microphone': ['()'],\n    'midi': ['()'],\n    'navigation-override': ['()'],\n    'payment': ['(self)'],\n    'picture-in-picture': ['()'],\n    'publickey-credentials-get': ['()'],\n    'screen-wake-lock': ['()'],\n    'sync-xhr': ['()'],\n    'usb': ['()'],\n    'web-share': ['(self)'],\n    'xr-spatial-tracking': ['()'],\n  };\n\n  return Object.entries(policies)\n    .map(([key, values]) => `${key}=${values.join(' ')}`)\n    .join(', ');\n}\n\n/**\n * Apply security headers to response\n */\nexport function applySecurityHeaders(\n  response: NextResponse,\n  config: SecurityConfig = defaultConfig,\n  isDevelopment = false\n): NextResponse {\n  // Content Security Policy\n  if (config.contentSecurityPolicy) {\n    response.headers.set(\n      'Content-Security-Policy',\n      getContentSecurityPolicy(isDevelopment)\n    );\n  }\n\n  // Strict Transport Security (HTTPS only)\n  if (config.strictTransportSecurity && !isDevelopment) {\n    response.headers.set(\n      'Strict-Transport-Security',\n      'max-age=31536000; includeSubDomains; preload'\n    );\n  }\n\n  // X-Frame-Options\n  if (config.xFrameOptions) {\n    response.headers.set('X-Frame-Options', 'DENY');\n  }\n\n  // X-Content-Type-Options\n  if (config.xContentTypeOptions) {\n    response.headers.set('X-Content-Type-Options', 'nosniff');\n  }\n\n  // Referrer Policy\n  if (config.referrerPolicy) {\n    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');\n  }\n\n  // Permissions Policy\n  if (config.permissionsPolicy) {\n    response.headers.set('Permissions-Policy', getPermissionsPolicy());\n  }\n\n  // Cross-Origin Embedder Policy\n  if (config.crossOriginEmbedderPolicy) {\n    response.headers.set('Cross-Origin-Embedder-Policy', 'require-corp');\n  }\n\n  // Cross-Origin Opener Policy\n  if (config.crossOriginOpenerPolicy) {\n    response.headers.set('Cross-Origin-Opener-Policy', 'same-origin');\n  }\n\n  // Cross-Origin Resource Policy\n  if (config.crossOriginResourcePolicy) {\n    response.headers.set('Cross-Origin-Resource-Policy', 'same-origin');\n  }\n\n  // Additional security headers\n  response.headers.set('X-DNS-Prefetch-Control', 'off');\n  response.headers.set('X-Download-Options', 'noopen');\n  response.headers.set('X-Permitted-Cross-Domain-Policies', 'none');\n  response.headers.set('X-XSS-Protection', '1; mode=block');\n\n  // Remove potentially sensitive headers\n  response.headers.delete('X-Powered-By');\n  response.headers.delete('Server');\n\n  return response;\n}\n\n/**\n * Security headers middleware\n */\nexport function securityHeadersMiddleware(\n  request: NextRequest,\n  config?: SecurityConfig\n): NextResponse {\n  const response = NextResponse.next();\n  const isDevelopment = process.env.NODE_ENV === 'development';\n\n  return applySecurityHeaders(response, config, isDevelopment);\n}\n\n/**\n * Rate limiting headers\n */\nexport function applyRateLimitHeaders(\n  response: NextResponse,\n  limit: number,\n  remaining: number,\n  resetTime: number\n): NextResponse {\n  response.headers.set('X-RateLimit-Limit', limit.toString());\n  response.headers.set('X-RateLimit-Remaining', remaining.toString());\n  response.headers.set('X-RateLimit-Reset', resetTime.toString());\n\n  if (remaining === 0) {\n    response.headers.set('Retry-After', Math.ceil((resetTime - Date.now()) / 1000).toString());\n  }\n\n  return response;\n}\n\n/**\n * CORS headers configuration\n */\nexport function applyCorsHeaders(\n  response: NextResponse,\n  origin?: string,\n  allowedOrigins: string[] = []\n): NextResponse {\n  const isDevelopment = process.env.NODE_ENV === 'development';\n  \n  // Allow all origins in development, specific origins in production\n  if (isDevelopment) {\n    response.headers.set('Access-Control-Allow-Origin', '*');\n  } else if (origin && allowedOrigins.includes(origin)) {\n    response.headers.set('Access-Control-Allow-Origin', origin);\n    response.headers.set('Vary', 'Origin');\n  }\n\n  response.headers.set(\n    'Access-Control-Allow-Methods',\n    'GET, POST, PUT, DELETE, OPTIONS'\n  );\n  \n  response.headers.set(\n    'Access-Control-Allow-Headers',\n    'Content-Type, Authorization, X-Requested-With, X-CSRF-Token'\n  );\n\n  response.headers.set('Access-Control-Max-Age', '86400'); // 24 hours\n\n  return response;\n}\n\n/**\n * Security audit helper\n */\nexport function auditSecurityHeaders(headers: Headers): {\n  score: number;\n  issues: string[];\n  recommendations: string[];\n} {\n  const issues: string[] = [];\n  const recommendations: string[] = [];\n  let score = 100;\n\n  const requiredHeaders = [\n    'Content-Security-Policy',\n    'X-Frame-Options',\n    'X-Content-Type-Options',\n    'Referrer-Policy',\n  ];\n\n  const recommendedHeaders = [\n    'Strict-Transport-Security',\n    'Permissions-Policy',\n    'Cross-Origin-Opener-Policy',\n  ];\n\n  // Check required headers\n  requiredHeaders.forEach(header => {\n    if (!headers.get(header)) {\n      issues.push(`Missing required header: ${header}`);\n      score -= 20;\n    }\n  });\n\n  // Check recommended headers\n  recommendedHeaders.forEach(header => {\n    if (!headers.get(header)) {\n      recommendations.push(`Consider adding header: ${header}`);\n      score -= 5;\n    }\n  });\n\n  // Check for insecure values\n  const csp = headers.get('Content-Security-Policy');\n  if (csp && csp.includes(\"'unsafe-inline'\") && !csp.includes('nonce-')) {\n    issues.push('CSP allows unsafe-inline without nonce');\n    score -= 10;\n  }\n\n  const frameOptions = headers.get('X-Frame-Options');\n  if (frameOptions && frameOptions.toLowerCase() === 'allowall') {\n    issues.push('X-Frame-Options set to ALLOWALL (insecure)');\n    score -= 15;\n  }\n\n  return {\n    score: Math.max(0, score),\n    issues,\n    recommendations,\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAED;AAAA;;AAcA,MAAM,gBAAgC;IACpC,uBAAuB;IACvB,yBAAyB;IACzB,eAAe;IACf,qBAAqB;IACrB,gBAAgB;IAChB,mBAAmB;IACnB,2BAA2B;IAC3B,yBAAyB;IACzB,2BAA2B;AAC7B;AAKO,SAAS,yBAAyB,gBAAgB,KAAK;IAC5D,MAAM,WAAW;QACf,eAAe;YAAC;SAAS;QACzB,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;eACI,gBAAgB;gBAAC;gBAAmB;aAAgB,GAAG,EAAE;SAC9D;QACD,aAAa;YACX;YACA;YACA;SACD;QACD,WAAW;YACT;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;YACV;YACA;YACA;SACD;QACD,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;eACI,gBAAgB;gBAAC;gBAAO;aAAO,GAAG,EAAE;SACzC;QACD,aAAa;YACX;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ;YACA;SACD;QACD,aAAa;YACX;YACA;SACD;QACD,cAAc;YAAC;SAAS;QACxB,YAAY;YAAC;SAAS;QACtB,eAAe;YAAC;SAAS;QACzB,mBAAmB;YAAC;SAAS;QAC7B,6BAA6B,gBAAgB,EAAE,GAAG;YAAC;SAAG;IACxD;IAEA,OAAO,OAAO,OAAO,CAAC,UACnB,MAAM,CAAC,CAAC,CAAC,GAAG,OAAO,GAAK,OAAO,MAAM,GAAG,GACxC,GAAG,CAAC,CAAC,CAAC,KAAK,OAAO,GAAK,GAAG,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,MAAM,EACnD,IAAI,CAAC;AACV;AAKO,SAAS;IACd,MAAM,WAAW;QACf,iBAAiB;YAAC;SAAK;QACvB,wBAAwB;YAAC;SAAK;QAC9B,YAAY;YAAC;SAAK;QAClB,WAAW;YAAC;SAAK;QACjB,UAAU;YAAC;SAAK;QAChB,yBAAyB;YAAC;SAAK;QAC/B,mBAAmB;YAAC;SAAK;QACzB,mBAAmB;YAAC;SAAK;QACzB,mBAAmB;YAAC;SAAK;QACzB,gCAAgC;YAAC;SAAK;QACtC,mCAAmC;YAAC;SAAK;QACzC,cAAc;YAAC;SAAS;QACxB,eAAe;YAAC;SAAK;QACrB,aAAa;YAAC;SAAK;QACnB,gBAAgB;YAAC;SAAK;QACtB,gBAAgB;YAAC;SAAK;QACtB,cAAc;YAAC;SAAK;QACpB,QAAQ;YAAC;SAAK;QACd,uBAAuB;YAAC;SAAK;QAC7B,WAAW;YAAC;SAAS;QACrB,sBAAsB;YAAC;SAAK;QAC5B,6BAA6B;YAAC;SAAK;QACnC,oBAAoB;YAAC;SAAK;QAC1B,YAAY;YAAC;SAAK;QAClB,OAAO;YAAC;SAAK;QACb,aAAa;YAAC;SAAS;QACvB,uBAAuB;YAAC;SAAK;IAC/B;IAEA,OAAO,OAAO,OAAO,CAAC,UACnB,GAAG,CAAC,CAAC,CAAC,KAAK,OAAO,GAAK,GAAG,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,MAAM,EACnD,IAAI,CAAC;AACV;AAKO,SAAS,qBACd,QAAsB,EACtB,SAAyB,aAAa,EACtC,gBAAgB,KAAK;IAErB,0BAA0B;IAC1B,IAAI,OAAO,qBAAqB,EAAE;QAChC,SAAS,OAAO,CAAC,GAAG,CAClB,2BACA,yBAAyB;IAE7B;IAEA,yCAAyC;IACzC,IAAI,OAAO,uBAAuB,IAAI,CAAC,eAAe;QACpD,SAAS,OAAO,CAAC,GAAG,CAClB,6BACA;IAEJ;IAEA,kBAAkB;IAClB,IAAI,OAAO,aAAa,EAAE;QACxB,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IAC1C;IAEA,yBAAyB;IACzB,IAAI,OAAO,mBAAmB,EAAE;QAC9B,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B;IACjD;IAEA,kBAAkB;IAClB,IAAI,OAAO,cAAc,EAAE;QACzB,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IAC1C;IAEA,qBAAqB;IACrB,IAAI,OAAO,iBAAiB,EAAE;QAC5B,SAAS,OAAO,CAAC,GAAG,CAAC,sBAAsB;IAC7C;IAEA,+BAA+B;IAC/B,IAAI,OAAO,yBAAyB,EAAE;QACpC,SAAS,OAAO,CAAC,GAAG,CAAC,gCAAgC;IACvD;IAEA,6BAA6B;IAC7B,IAAI,OAAO,uBAAuB,EAAE;QAClC,SAAS,OAAO,CAAC,GAAG,CAAC,8BAA8B;IACrD;IAEA,+BAA+B;IAC/B,IAAI,OAAO,yBAAyB,EAAE;QACpC,SAAS,OAAO,CAAC,GAAG,CAAC,gCAAgC;IACvD;IAEA,8BAA8B;IAC9B,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B;IAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,sBAAsB;IAC3C,SAAS,OAAO,CAAC,GAAG,CAAC,qCAAqC;IAC1D,SAAS,OAAO,CAAC,GAAG,CAAC,oBAAoB;IAEzC,uCAAuC;IACvC,SAAS,OAAO,CAAC,MAAM,CAAC;IACxB,SAAS,OAAO,CAAC,MAAM,CAAC;IAExB,OAAO;AACT;AAKO,SAAS,0BACd,OAAoB,EACpB,MAAuB;IAEvB,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI;IAClC,MAAM,gBAAgB,oDAAyB;IAE/C,OAAO,qBAAqB,UAAU,QAAQ;AAChD;AAKO,SAAS,sBACd,QAAsB,EACtB,KAAa,EACb,SAAiB,EACjB,SAAiB;IAEjB,SAAS,OAAO,CAAC,GAAG,CAAC,qBAAqB,MAAM,QAAQ;IACxD,SAAS,OAAO,CAAC,GAAG,CAAC,yBAAyB,UAAU,QAAQ;IAChE,SAAS,OAAO,CAAC,GAAG,CAAC,qBAAqB,UAAU,QAAQ;IAE5D,IAAI,cAAc,GAAG;QACnB,SAAS,OAAO,CAAC,GAAG,CAAC,eAAe,KAAK,IAAI,CAAC,CAAC,YAAY,KAAK,GAAG,EAAE,IAAI,MAAM,QAAQ;IACzF;IAEA,OAAO;AACT;AAKO,SAAS,iBACd,QAAsB,EACtB,MAAe,EACf,iBAA2B,EAAE;IAE7B,MAAM,gBAAgB,oDAAyB;IAE/C,mEAAmE;IACnE,wCAAmB;QACjB,SAAS,OAAO,CAAC,GAAG,CAAC,+BAA+B;IACtD,OAAO;;IAGP;IAEA,SAAS,OAAO,CAAC,GAAG,CAClB,gCACA;IAGF,SAAS,OAAO,CAAC,GAAG,CAClB,gCACA;IAGF,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B,UAAU,WAAW;IAEpE,OAAO;AACT;AAKO,SAAS,qBAAqB,OAAgB;IAKnD,MAAM,SAAmB,EAAE;IAC3B,MAAM,kBAA4B,EAAE;IACpC,IAAI,QAAQ;IAEZ,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;KACD;IAED,MAAM,qBAAqB;QACzB;QACA;QACA;KACD;IAED,yBAAyB;IACzB,gBAAgB,OAAO,CAAC,CAAA;QACtB,IAAI,CAAC,QAAQ,GAAG,CAAC,SAAS;YACxB,OAAO,IAAI,CAAC,CAAC,yBAAyB,EAAE,QAAQ;YAChD,SAAS;QACX;IACF;IAEA,4BAA4B;IAC5B,mBAAmB,OAAO,CAAC,CAAA;QACzB,IAAI,CAAC,QAAQ,GAAG,CAAC,SAAS;YACxB,gBAAgB,IAAI,CAAC,CAAC,wBAAwB,EAAE,QAAQ;YACxD,SAAS;QACX;IACF;IAEA,4BAA4B;IAC5B,MAAM,MAAM,QAAQ,GAAG,CAAC;IACxB,IAAI,OAAO,IAAI,QAAQ,CAAC,sBAAsB,CAAC,IAAI,QAAQ,CAAC,WAAW;QACrE,OAAO,IAAI,CAAC;QACZ,SAAS;IACX;IAEA,MAAM,eAAe,QAAQ,GAAG,CAAC;IACjC,IAAI,gBAAgB,aAAa,WAAW,OAAO,YAAY;QAC7D,OAAO,IAAI,CAAC;QACZ,SAAS;IACX;IAEA,OAAO;QACL,OAAO,KAAK,GAAG,CAAC,GAAG;QACnB;QACA;IACF;AACF"}}, {"offset": {"line": 348, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { applySecurityHeaders, applyCorsHeaders, applyRateLimitHeaders } from './lib/security/headers';\n\nconst locales = ['en', 'ar'];\nconst defaultLocale = 'ar';\n\n// Rate limiting store (in production, use Redis or similar)\nconst rateLimitStore = new Map<string, { count: number; resetTime: number }>();\n\ninterface RateLimitConfig {\n  windowMs: number;\n  maxRequests: number;\n}\n\nconst rateLimitConfigs: Record<string, RateLimitConfig> = {\n  api: { windowMs: 15 * 60 * 1000, maxRequests: 100 }, // 100 requests per 15 minutes\n  auth: { windowMs: 15 * 60 * 1000, maxRequests: 5 }, // 5 auth attempts per 15 minutes\n  upload: { windowMs: 60 * 60 * 1000, maxRequests: 10 }, // 10 uploads per hour\n  default: { windowMs: 60 * 1000, maxRequests: 60 }, // 60 requests per minute\n};\n\n/**\n * Get client IP address\n */\nfunction getClientIP(request: NextRequest): string {\n  const forwarded = request.headers.get('x-forwarded-for');\n  const realIP = request.headers.get('x-real-ip');\n\n  if (forwarded) {\n    return forwarded.split(',')[0].trim();\n  }\n\n  return realIP || 'unknown';\n}\n\n/**\n * Rate limiting implementation\n */\nfunction checkRateLimit(\n  clientIP: string,\n  endpoint: string,\n  config: RateLimitConfig\n): { allowed: boolean; remaining: number; resetTime: number } {\n  const key = `${clientIP}:${endpoint}`;\n  const now = Date.now();\n\n  // Clean up expired entries\n  for (const [k, v] of rateLimitStore.entries()) {\n    if (v.resetTime < now) {\n      rateLimitStore.delete(k);\n    }\n  }\n\n  const current = rateLimitStore.get(key);\n\n  if (!current || current.resetTime < now) {\n    rateLimitStore.set(key, {\n      count: 1,\n      resetTime: now + config.windowMs,\n    });\n    return {\n      allowed: true,\n      remaining: config.maxRequests - 1,\n      resetTime: now + config.windowMs,\n    };\n  }\n\n  if (current.count >= config.maxRequests) {\n    return {\n      allowed: false,\n      remaining: 0,\n      resetTime: current.resetTime,\n    };\n  }\n\n  current.count++;\n  rateLimitStore.set(key, current);\n\n  return {\n    allowed: true,\n    remaining: config.maxRequests - current.count,\n    resetTime: current.resetTime,\n  };\n}\n\nfunction getLocale(request: NextRequest) {\n  // Check if there is a cookie with the locale\n  const cookieLocale = request.cookies.get('NEXT_LOCALE')?.value;\n  if (cookieLocale && locales.includes(cookieLocale)) {\n    return cookieLocale;\n  }\n\n  // Check if there is a locale in the pathname\n  const pathname = request.nextUrl.pathname;\n  const pathnameLocale = locales.find(\n    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`\n  );\n  if (pathnameLocale) {\n    return pathnameLocale;\n  }\n\n  // Check the Accept-Language header\n  const acceptLanguage = request.headers.get('accept-language');\n  if (acceptLanguage) {\n    const acceptedLocales = acceptLanguage.split(',').map((locale) => locale.split(';')[0].trim());\n    const matchedLocale = acceptedLocales.find((locale) => locales.includes(locale));\n    if (matchedLocale) {\n      return matchedLocale;\n    }\n  }\n\n  // Default to the default locale\n  return defaultLocale;\n}\n\nexport function middleware(request: NextRequest) {\n  const pathname = request.nextUrl.pathname;\n  const clientIP = getClientIP(request);\n\n  // Skip middleware for static assets and Next.js internals\n  if (\n    pathname.startsWith('/_next/') ||\n    pathname.startsWith('/static/') ||\n    pathname.includes('.') && !pathname.startsWith('/api/')\n  ) {\n    return NextResponse.next();\n  }\n\n  let response = NextResponse.next();\n\n  try {\n    // 1. Rate Limiting for API routes\n    if (pathname.startsWith('/api/')) {\n      const endpoint = pathname.startsWith('/api/auth/') ? 'auth' : 'api';\n      const config = rateLimitConfigs[endpoint];\n      const rateLimit = checkRateLimit(clientIP, endpoint, config);\n\n      if (!rateLimit.allowed) {\n        console.warn(`[SECURITY] Rate limit exceeded for ${clientIP} on ${pathname}`);\n\n        response = NextResponse.json(\n          { error: 'Rate limit exceeded' },\n          { status: 429 }\n        );\n\n        return applyRateLimitHeaders(\n          response,\n          config.maxRequests,\n          rateLimit.remaining,\n          rateLimit.resetTime\n        );\n      }\n\n      response = applyRateLimitHeaders(\n        response,\n        config.maxRequests,\n        rateLimit.remaining,\n        rateLimit.resetTime\n      );\n    }\n\n    // 2. Internationalization Logic\n    const pathnameIsMissingLocale = locales.every(\n      (locale) => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`\n    );\n\n    if (pathnameIsMissingLocale) {\n      const locale = getLocale(request);\n      const url = new URL(`/${locale}${pathname}`, request.url);\n      url.search = request.nextUrl.search;\n      response = NextResponse.redirect(url);\n    }\n\n    // 3. Apply Security Headers\n    response = applySecurityHeaders(response);\n\n    // 4. Apply CORS Headers\n    const origin = request.headers.get('origin');\n    const allowedOrigins = [\n      process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',\n    ];\n    response = applyCorsHeaders(response, origin || undefined, allowedOrigins);\n\n    return response;\n\n  } catch (error) {\n    console.error('Middleware error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\nexport const config = {\n  matcher: [\n    // Skip all internal paths (_next, api, etc.)\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEA,MAAM,UAAU;IAAC;IAAM;CAAK;AAC5B,MAAM,gBAAgB;AAEtB,4DAA4D;AAC5D,MAAM,iBAAiB,IAAI;AAO3B,MAAM,mBAAoD;IACxD,KAAK;QAAE,UAAU,KAAK,KAAK;QAAM,aAAa;IAAI;IAClD,MAAM;QAAE,UAAU,KAAK,KAAK;QAAM,aAAa;IAAE;IACjD,QAAQ;QAAE,UAAU,KAAK,KAAK;QAAM,aAAa;IAAG;IACpD,SAAS;QAAE,UAAU,KAAK;QAAM,aAAa;IAAG;AAClD;AAEA;;CAEC,GACD,SAAS,YAAY,OAAoB;IACvC,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;IACtC,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEnC,IAAI,WAAW;QACb,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;IACrC;IAEA,OAAO,UAAU;AACnB;AAEA;;CAEC,GACD,SAAS,eACP,QAAgB,EAChB,QAAgB,EAChB,MAAuB;IAEvB,MAAM,MAAM,GAAG,SAAS,CAAC,EAAE,UAAU;IACrC,MAAM,MAAM,KAAK,GAAG;IAEpB,2BAA2B;IAC3B,KAAK,MAAM,CAAC,GAAG,EAAE,IAAI,eAAe,OAAO,GAAI;QAC7C,IAAI,EAAE,SAAS,GAAG,KAAK;YACrB,eAAe,MAAM,CAAC;QACxB;IACF;IAEA,MAAM,UAAU,eAAe,GAAG,CAAC;IAEnC,IAAI,CAAC,WAAW,QAAQ,SAAS,GAAG,KAAK;QACvC,eAAe,GAAG,CAAC,KAAK;YACtB,OAAO;YACP,WAAW,MAAM,OAAO,QAAQ;QAClC;QACA,OAAO;YACL,SAAS;YACT,WAAW,OAAO,WAAW,GAAG;YAChC,WAAW,MAAM,OAAO,QAAQ;QAClC;IACF;IAEA,IAAI,QAAQ,KAAK,IAAI,OAAO,WAAW,EAAE;QACvC,OAAO;YACL,SAAS;YACT,WAAW;YACX,WAAW,QAAQ,SAAS;QAC9B;IACF;IAEA,QAAQ,KAAK;IACb,eAAe,GAAG,CAAC,KAAK;IAExB,OAAO;QACL,SAAS;QACT,WAAW,OAAO,WAAW,GAAG,QAAQ,KAAK;QAC7C,WAAW,QAAQ,SAAS;IAC9B;AACF;AAEA,SAAS,UAAU,OAAoB;IACrC,6CAA6C;IAC7C,MAAM,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC,gBAAgB;IACzD,IAAI,gBAAgB,QAAQ,QAAQ,CAAC,eAAe;QAClD,OAAO;IACT;IAEA,6CAA6C;IAC7C,MAAM,WAAW,QAAQ,OAAO,CAAC,QAAQ;IACzC,MAAM,iBAAiB,QAAQ,IAAI,CACjC,CAAC,SAAW,SAAS,UAAU,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,KAAK,aAAa,CAAC,CAAC,EAAE,QAAQ;IAE7E,IAAI,gBAAgB;QAClB,OAAO;IACT;IAEA,mCAAmC;IACnC,MAAM,iBAAiB,QAAQ,OAAO,CAAC,GAAG,CAAC;IAC3C,IAAI,gBAAgB;QAClB,MAAM,kBAAkB,eAAe,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,SAAW,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;QAC3F,MAAM,gBAAgB,gBAAgB,IAAI,CAAC,CAAC,SAAW,QAAQ,QAAQ,CAAC;QACxE,IAAI,eAAe;YACjB,OAAO;QACT;IACF;IAEA,gCAAgC;IAChC,OAAO;AACT;AAEO,SAAS,WAAW,OAAoB;IAC7C,MAAM,WAAW,QAAQ,OAAO,CAAC,QAAQ;IACzC,MAAM,WAAW,YAAY;IAE7B,0DAA0D;IAC1D,IACE,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,eACpB,SAAS,QAAQ,CAAC,QAAQ,CAAC,SAAS,UAAU,CAAC,UAC/C;QACA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,IAAI,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI;IAEhC,IAAI;QACF,kCAAkC;QAClC,IAAI,SAAS,UAAU,CAAC,UAAU;YAChC,MAAM,WAAW,SAAS,UAAU,CAAC,gBAAgB,SAAS;YAC9D,MAAM,SAAS,gBAAgB,CAAC,SAAS;YACzC,MAAM,YAAY,eAAe,UAAU,UAAU;YAErD,IAAI,CAAC,UAAU,OAAO,EAAE;gBACtB,QAAQ,IAAI,CAAC,CAAC,mCAAmC,EAAE,SAAS,IAAI,EAAE,UAAU;gBAE5E,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI,CAC1B;oBAAE,OAAO;gBAAsB,GAC/B;oBAAE,QAAQ;gBAAI;gBAGhB,OAAO,CAAA,GAAA,yIAAA,CAAA,wBAAqB,AAAD,EACzB,UACA,OAAO,WAAW,EAClB,UAAU,SAAS,EACnB,UAAU,SAAS;YAEvB;YAEA,WAAW,CAAA,GAAA,yIAAA,CAAA,wBAAqB,AAAD,EAC7B,UACA,OAAO,WAAW,EAClB,UAAU,SAAS,EACnB,UAAU,SAAS;QAEvB;QAEA,gCAAgC;QAChC,MAAM,0BAA0B,QAAQ,KAAK,CAC3C,CAAC,SAAW,CAAC,SAAS,UAAU,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,KAAK,aAAa,CAAC,CAAC,EAAE,QAAQ;QAG9E,IAAI,yBAAyB;YAC3B,MAAM,SAAS,UAAU;YACzB,MAAM,MAAM,IAAI,IAAI,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,QAAQ,GAAG;YACxD,IAAI,MAAM,GAAG,QAAQ,OAAO,CAAC,MAAM;YACnC,WAAW,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QACnC;QAEA,4BAA4B;QAC5B,WAAW,CAAA,GAAA,yIAAA,CAAA,uBAAoB,AAAD,EAAE;QAEhC,wBAAwB;QACxB,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,MAAM,iBAAiB;YACrB,6DAAmC;SACpC;QACD,WAAW,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,UAAU,WAAW;QAE3D,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,MAAM,SAAS;IACpB,SAAS;QACP,6CAA6C;QAC7C;KACD;AACH"}}]}