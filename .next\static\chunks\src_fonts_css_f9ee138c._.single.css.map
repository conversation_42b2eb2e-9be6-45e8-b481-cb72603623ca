{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/fonts.css"], "sourcesContent": ["/* تحسين عرض الخط العربي */\n[dir=\"rtl\"] h1,\n[dir=\"rtl\"] h2,\n[dir=\"rtl\"] h3,\n[dir=\"rtl\"] h4,\n[dir=\"rtl\"] h5,\n[dir=\"rtl\"] h6 {\n  letter-spacing: -0.025em;\n  line-height: 1.5;\n}\n\n/* تحسين حجم الخط للعربية */\n[dir=\"rtl\"] {\n  font-feature-settings: \"calt\" 1, \"clig\" 1, \"dlig\" 1, \"kern\" 1, \"liga\" 1, \"salt\" 1;\n}\n\n/* تحسين المسافات بين الأحرف العربية */\n[dir=\"rtl\"] p,\n[dir=\"rtl\"] span,\n[dir=\"rtl\"] div,\n[dir=\"rtl\"] button,\n[dir=\"rtl\"] a {\n  letter-spacing: 0;\n  word-spacing: 0.05em;\n  line-height: 1.8;\n}\n\n/* تحسين اتجاه النص في المدخلات */\n[dir=\"rtl\"] input,\n[dir=\"rtl\"] textarea {\n  text-align: right;\n}\n\n/* تحسين هوامش العناصر في وضع RTL */\n[dir=\"rtl\"] .ml-2 {\n  margin-left: 0;\n  margin-right: 0.5rem;\n}\n\n[dir=\"rtl\"] .mr-2 {\n  margin-right: 0;\n  margin-left: 0.5rem;\n}\n\n[dir=\"rtl\"] .ml-4 {\n  margin-left: 0;\n  margin-right: 1rem;\n}\n\n[dir=\"rtl\"] .mr-4 {\n  margin-right: 0;\n  margin-left: 1rem;\n}\n\n/* تحسين أيقونات الأسهم في وضع RTL */\n[dir=\"rtl\"] .rotate-rtl {\n  transform: rotate(180deg);\n}\n"], "names": [], "mappings": "AACA;;;;;AAWA;;;;AAKA;;;;;;AAWA;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAMA"}}]}