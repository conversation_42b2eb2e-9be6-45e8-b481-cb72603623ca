{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_e5387405.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_e5387405-module__6kjfMG__className\",\n  \"variable\": \"inter_e5387405-module__6kjfMG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_e5387405.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22,%22display%22:%22swap%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/tajawal_6b6f4029.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"tajawal_6b6f4029-module__xzAzWG__className\",\n  \"variable\": \"tajawal_6b6f4029-module__xzAzWG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/tajawal_6b6f4029.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Tajawal%22,%22arguments%22:[{%22subsets%22:[%22arabic%22],%22weight%22:[%22200%22,%22300%22,%22400%22,%22500%22,%22700%22,%22800%22,%22900%22],%22variable%22:%22--font-tajawal%22,%22display%22:%22swap%22}],%22variableName%22:%22tajawal%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'<PERSON><PERSON><PERSON>', 'Tajawal Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,uJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,uJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,uJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/layout/RootLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const RootLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call RootLayout() from the server but RootLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/RootLayout.tsx <module evaluation>\",\n    \"RootLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,sEACA", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/layout/RootLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const RootLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call RootLayout() from the server but RootLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/RootLayout.tsx\",\n    \"RootLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,kDACA", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/app/providers.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Providers = registerClientReference(\n    function() { throw new Error(\"Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/providers.tsx <module evaluation>\",\n    \"Providers\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,uDACA", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/app/providers.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Providers = registerClientReference(\n    function() { throw new Error(\"Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/providers.tsx\",\n    \"Providers\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,mCACA", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/app/%5Blocale%5D/layout.tsx"], "sourcesContent": ["import type { Metadata, Viewport } from 'next';\nimport { <PERSON>, <PERSON><PERSON><PERSON> } from 'next/font/google';\nimport { RootLayout } from '../../components/layout/RootLayout';\nimport { Providers } from '../providers';\nimport '../../index.css'; // Keep for Tailwind base, components, utilities, and CSS variables\n\n// Initialize fonts\nconst inter = Inter({\n  subsets: ['latin'],\n  variable: '--font-inter', // For Tailwind CSS variable integration\n  display: 'swap',\n});\n\nconst tajawal = Tajawal({\n  subsets: ['arabic'],\n  weight: ['200', '300', '400', '500', '700', '800', '900'],\n  variable: '--font-tajawal', // For Tailwind CSS variable integration\n  display: 'swap',\n});\n\nexport const metadata: Metadata = {\n  title: 'ARTAL | Your Complete Business Solution',\n  description: 'Full-service commercial platform offering B2C retail, B2B wholesale, production lines, business services, and more.',\n  manifest: '/manifest.json',\n};\n\nexport const viewport: Viewport = {\n  themeColor: '#9C27B0',\n  width: 'device-width',\n  initialScale: 1,\n  maximumScale: 1,\n};\n\n// Initialize theme will be handled in client components\n\nexport default async function LocaleLayout({\n  children,\n  params,\n}: {\n  children: React.ReactNode;\n  params: Promise<{ locale: string }>;\n}) {\n  // استخراج اللغة من المعاملات (await for Next.js 15)\n  const resolvedParams = await params;\n  const locale = resolvedParams.locale || 'ar';\n  const isArabic = locale === 'ar';\n\n  // تحديد فئة الخط بناءً على اللغة\n  const fontUtilityClass = isArabic ? \"font-arabic\" : \"font-sans\";\n\n  // تحديد اتجاه النص\n  const direction = isArabic ? 'rtl' : 'ltr';\n\n  // تحديد اللغة للـ HTML\n  const htmlLang = isArabic ? 'ar' : 'en';\n\n  const themeInitializerScript = `\n    (function() {\n      function getInitialTheme() {\n        try {\n          const storedTheme = localStorage.getItem('ui-theme');\n          if (storedTheme) {\n            return storedTheme;\n          }\n          const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n          if (mediaQuery.matches) {\n            return 'dark';\n          }\n        } catch (e) {\n          // localStorage is not available or other error\n        }\n        return 'light'; // Default theme if no preference or error\n      }\n\n      const theme = getInitialTheme();\n      if (theme === 'dark') {\n        document.documentElement.classList.add('dark');\n      } else {\n        // Ensure dark class is removed if theme is light or system preference is light\n        // and no specific theme is stored.\n        document.documentElement.classList.remove('dark');\n      }\n\n      // Set language direction\n      document.documentElement.setAttribute('dir', '${direction}');\n      document.documentElement.setAttribute('lang', '${htmlLang}');\n    })();\n  `;\n\n  return (\n    // Apply font variables and the determined font utility class to HTML tag\n    <html\n      lang={htmlLang}\n      dir={direction}\n      className={`${inter.variable} ${tajawal.variable} ${fontUtilityClass}`}\n      suppressHydrationWarning\n    >\n      <head>\n        <script dangerouslySetInnerHTML={{ __html: themeInitializerScript }} />\n      </head>\n      {/* Body tag will inherit font from HTML. Styling like background, text color, and antialiasing\n          are applied globally via CSS (e.g., in index.css or Tailwind base styles) */}\n      <body suppressHydrationWarning>\n        <Providers locale={locale}>\n          <RootLayout>\n            {children}\n          </RootLayout>\n        </Providers>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;;;;;;;AAiBO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;AACZ;AAEO,MAAM,WAAqB;IAChC,YAAY;IACZ,OAAO;IACP,cAAc;IACd,cAAc;AAChB;AAIe,eAAe,aAAa,EACzC,QAAQ,EACR,MAAM,EAIP;IACC,oDAAoD;IACpD,MAAM,iBAAiB,MAAM;IAC7B,MAAM,SAAS,eAAe,MAAM,IAAI;IACxC,MAAM,WAAW,WAAW;IAE5B,iCAAiC;IACjC,MAAM,mBAAmB,WAAW,gBAAgB;IAEpD,mBAAmB;IACnB,MAAM,YAAY,WAAW,QAAQ;IAErC,uBAAuB;IACvB,MAAM,WAAW,WAAW,OAAO;IAEnC,MAAM,yBAAyB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;oDA4BkB,EAAE,UAAU;qDACX,EAAE,SAAS;;EAE9D,CAAC;IAED,OACE,yEAAyE;kBACzE,8OAAC;QACC,MAAM;QACN,KAAK;QACL,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,2IAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,kBAAkB;QACtE,wBAAwB;;0BAExB,8OAAC;0BACC,cAAA,8OAAC;oBAAO,yBAAyB;wBAAE,QAAQ;oBAAuB;;;;;;;;;;;0BAIpE,8OAAC;gBAAK,wBAAwB;0BAC5B,cAAA,8OAAC,wHAAA,CAAA,YAAS;oBAAC,QAAQ;8BACjB,cAAA,8OAAC,0IAAA,CAAA,aAAU;kCACR;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}]}