{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata, Viewport } from 'next';\nimport '../index.css';\n\nexport const metadata: Metadata = {\n  title: 'ARTAL | Your Complete Business Solution',\n  description: 'Full-service commercial platform offering B2C retail, B2B wholesale, production lines, business services, and more.',\n};\n\nexport const viewport: Viewport = {\n  themeColor: '#9C27B0',\n  width: 'device-width',\n  initialScale: 1,\n  maximumScale: 1,\n};\n\nexport default function RootAppLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body>\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEO,MAAM,WAAqB;IAChC,YAAY;IACZ,OAAO;IACP,cAAc;IACd,cAAc;AAChB;AAEe,SAAS,cAAc,EACpC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;sBACE;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}