{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/app/%5Blocale%5D/shop/page.tsx"], "sourcesContent": ["import { Metadata } from 'next';\nimport { Suspense, lazy } from 'react';\n\n// Lazy load the ShopPageEnhanced component for better performance\nconst ShopPageEnhanced = lazy(() => import('../../../components/shop/ShopPageEnhanced').then(module => ({ default: module.ShopPageEnhanced })));\n\ninterface ShopPageProps {\n  params: {\n    locale: string;\n  };\n  searchParams: {\n    category?: string;\n    q?: string;\n    featured?: string;\n    new?: string;\n  };\n}\n\nexport async function generateMetadata({ params, searchParams }: ShopPageProps): Promise<Metadata> {\n  const resolvedParams = await params;\n  const resolvedSearchParams = await searchParams;\n  const isArabic = resolvedParams.locale === 'ar';\n\n  let title = isArabic ? 'المتجر | كوميرس برو' : 'Shop | CommercePro';\n  let description = isArabic\n    ? 'تسوق من مجموعة واسعة من المنتجات عالية الجودة مع أفضل الأسعار وخدمة العملاء المتميزة'\n    : 'Shop from a wide range of high-quality products with the best prices and excellent customer service';\n\n  // Customize based on search parameters\n  if (resolvedSearchParams?.category && resolvedSearchParams.category !== 'all') {\n    const categoryName = resolvedSearchParams.category.replace('-', ' ');\n    title = isArabic\n      ? `${categoryName} | المتجر | كوميرس برو`\n      : `${categoryName} | Shop | CommercePro`;\n  }\n\n  if (resolvedSearchParams?.q) {\n    title = isArabic\n      ? `البحث عن \"${resolvedSearchParams.q}\" | المتجر | كوميرس برو`\n      : `Search for \"${resolvedSearchParams.q}\" | Shop | CommercePro`;\n  }\n\n  if (resolvedSearchParams?.featured === 'true') {\n    title = isArabic\n      ? 'المنتجات المميزة | المتجر | كوميرس برو'\n      : 'Featured Products | Shop | CommercePro';\n    description = isArabic\n      ? 'اكتشف مجموعتنا المختارة من المنتجات المميزة والأكثر شعبية'\n      : 'Discover our curated selection of featured and most popular products';\n  }\n\n  if (resolvedSearchParams?.new === 'true') {\n    title = isArabic\n      ? 'واصل حديثاً | المتجر | كوميرس برو'\n      : 'New Arrivals | Shop | CommercePro';\n    description = isArabic\n      ? 'اكتشف أحدث المنتجات والوصول الجديد في متجرنا'\n      : 'Discover the latest products and new arrivals in our store';\n  }\n\n  return {\n    title,\n    description,\n    keywords: isArabic\n      ? 'متجر, تسوق, منتجات, جودة عالية, أسعار مناسبة, خدمة عملاء'\n      : 'shop, shopping, products, high quality, affordable prices, customer service',\n    openGraph: {\n      title,\n      description,\n      type: 'website',\n      locale: resolvedParams.locale,\n    },\n    twitter: {\n      card: 'summary_large_image',\n      title,\n      description,\n    },\n    alternates: {\n      canonical: `/${resolvedParams.locale}/shop`,\n      languages: {\n        'en': '/en/shop',\n        'ar': '/ar/shop',\n      },\n    },\n  };\n}\n\n// Loading fallback component\nconst LoadingFallback = () => (\n  <div className=\"min-h-screen flex items-center justify-center\">\n    <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500\"></div>\n  </div>\n);\n\nexport default async function Shop({ params, searchParams }: ShopPageProps) {\n  const resolvedParams = await params;\n  const resolvedSearchParams = await searchParams;\n\n  return (\n    <Suspense fallback={<LoadingFallback />}>\n      <ShopPageEnhanced\n        locale={resolvedParams.locale as 'en' | 'ar'}\n        initialFilters={{\n          featured: resolvedSearchParams?.featured === 'true',\n          newArrivals: resolvedSearchParams?.new === 'true',\n          category: resolvedSearchParams?.category || 'all',\n          searchQuery: resolvedSearchParams?.q || ''\n        }}\n      />\n    </Suspense>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;;;AAEA,kEAAkE;AAClE,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,IAAM,4IAAoD,IAAI,CAAC,CAAA,SAAU,CAAC;YAAE,SAAS,OAAO,gBAAgB;QAAC,CAAC;AAcrI,eAAe,iBAAiB,EAAE,MAAM,EAAE,YAAY,EAAiB;IAC5E,MAAM,iBAAiB,MAAM;IAC7B,MAAM,uBAAuB,MAAM;IACnC,MAAM,WAAW,eAAe,MAAM,KAAK;IAE3C,IAAI,QAAQ,WAAW,wBAAwB;IAC/C,IAAI,cAAc,WACd,yFACA;IAEJ,uCAAuC;IACvC,IAAI,sBAAsB,YAAY,qBAAqB,QAAQ,KAAK,OAAO;QAC7E,MAAM,eAAe,qBAAqB,QAAQ,CAAC,OAAO,CAAC,KAAK;QAChE,QAAQ,WACJ,GAAG,aAAa,sBAAsB,CAAC,GACvC,GAAG,aAAa,qBAAqB,CAAC;IAC5C;IAEA,IAAI,sBAAsB,GAAG;QAC3B,QAAQ,WACJ,CAAC,UAAU,EAAE,qBAAqB,CAAC,CAAC,uBAAuB,CAAC,GAC5D,CAAC,YAAY,EAAE,qBAAqB,CAAC,CAAC,sBAAsB,CAAC;IACnE;IAEA,IAAI,sBAAsB,aAAa,QAAQ;QAC7C,QAAQ,WACJ,2CACA;QACJ,cAAc,WACV,8DACA;IACN;IAEA,IAAI,sBAAsB,QAAQ,QAAQ;QACxC,QAAQ,WACJ,sCACA;QACJ,cAAc,WACV,iDACA;IACN;IAEA,OAAO;QACL;QACA;QACA,UAAU,WACN,6DACA;QACJ,WAAW;YACT;YACA;YACA,MAAM;YACN,QAAQ,eAAe,MAAM;QAC/B;QACA,SAAS;YACP,MAAM;YACN;YACA;QACF;QACA,YAAY;YACV,WAAW,CAAC,CAAC,EAAE,eAAe,MAAM,CAAC,KAAK,CAAC;YAC3C,WAAW;gBACT,MAAM;gBACN,MAAM;YACR;QACF;IACF;AACF;AAEA,6BAA6B;AAC7B,MAAM,kBAAkB,kBACtB,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;;;;;;;;;;AAIJ,eAAe,KAAK,EAAE,MAAM,EAAE,YAAY,EAAiB;IACxE,MAAM,iBAAiB,MAAM;IAC7B,MAAM,uBAAuB,MAAM;IAEnC,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBAAU,8OAAC;;;;;kBACnB,cAAA,8OAAC;YACC,QAAQ,eAAe,MAAM;YAC7B,gBAAgB;gBACd,UAAU,sBAAsB,aAAa;gBAC7C,aAAa,sBAAsB,QAAQ;gBAC3C,UAAU,sBAAsB,YAAY;gBAC5C,aAAa,sBAAsB,KAAK;YAC1C;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;AAAqC,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;IAI5BI,SAASC;;;;;;;;;;;;AAIX,cAAc,0CAA0C,iBAAA;IAAE,MAAA,kBAAwB;AAAsB,EAAC,IAAA,OAAA;IAAA;IAAA;QAEzG,YAAA;YAAA;YAAA,mCAA4D;gBAC5D,OAAO,KAAA;oBAAMG;oBAAAA,MAAc,IAAIX,mBAAmB;4BAChDY,QAAAA;4BAAAA,GAAY;4BAAA;iCACVC,MAAMZ,UAAUa,QAAQ;sCACxBC,IAAAA,CAAM,CAAA;gCAAA,QAAA;oCAAA,IAAA;oCAAA;iCAAA;;+BACNC,UAAU;;yBACV,2CAA2C;8BAC3CC,IAAAA,CAAAA;oBAAAA;iBAAAA,CAAY;;iBACZC,UAAU;sBACVC,IAAAA,CAAAA,GAAU;gBAAA,CAAE,SAAA;oBAAA,IAAA;oBAAA;iBAAA;kBACd,WAAA;oBAAA,IAAA;oBAAA;iBAAA;;WACAC,UAAU;;SACRC,YAAYnB;UACd,QAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}