{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/ui/HeroButton.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef, ReactNode } from 'react';\nimport Link from 'next/link';\nimport { cn } from '../../lib/utils';\nimport { motion } from 'framer-motion';\n\nexport interface HeroButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'accent' | 'outline';\n  size?: 'sm' | 'md' | 'lg';\n  isLoading?: boolean;\n  href?: string; // استخدام href بدلاً من to لتوافق أفضل مع Next.js\n  children: ReactNode;\n}\n\nconst HeroButton = forwardRef<HTMLButtonElement, HeroButtonProps>(\n  ({ className, variant = 'primary', size = 'lg', isLoading, children, disabled, href, ...props }, ref) => {\n    const baseStyles = 'group inline-flex items-center justify-center font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none rounded-md relative overflow-hidden';\n\n    const variants = {\n      primary: 'bg-gradient-to-r from-primary-500 to-primary-600 text-white hover:from-primary-600 hover:to-primary-700 focus-visible:ring-primary-500 shadow-lg hover:shadow-xl',\n      secondary: 'bg-gradient-to-r from-secondary-500 to-secondary-600 text-white hover:from-secondary-600 hover:to-secondary-700 focus-visible:ring-secondary-500 shadow-lg hover:shadow-xl',\n      accent: 'bg-gradient-to-r from-accent-500 to-accent-600 text-white hover:from-accent-600 hover:to-accent-700 focus-visible:ring-accent-500 shadow-lg hover:shadow-xl',\n      outline: 'border-2 border-white bg-transparent text-white backdrop-blur-sm hover:bg-white/10 focus-visible:ring-white shadow-lg hover:shadow-xl',\n    };\n\n    const sizes = {\n      sm: 'h-9 px-3 text-xs',\n      md: 'h-10 px-4 text-sm',\n      lg: 'h-12 px-8 text-lg',\n    };\n\n    const classNames = cn(\n      baseStyles,\n      variants[variant],\n      sizes[size],\n      isLoading && 'opacity-70',\n      className\n    );\n\n    // تحديد محتوى الزر\n    const buttonContent = (\n      <>\n        {isLoading ? (\n          <span className=\"mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent relative z-10\" />\n        ) : null}\n\n        {typeof children === 'string' ? (\n          <span className=\"relative z-10\">{children}</span>\n        ) : (\n          <span className=\"relative z-10\">{children}</span>\n        )}\n\n        {/* تأثير التحويم للأزرار الملونة - تم إصلاح مشكلة اختفاء النص */}\n        {(variant === 'primary' || variant === 'secondary' || variant === 'accent') && (\n          <span className=\"absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10\" />\n        )}\n      </>\n    );\n\n    // إذا كان هناك href، استخدم Link\n    if (href) {\n      return (\n        <motion.div whileTap={{ scale: 0.98 }}>\n          <Link\n            href={href}\n            className={classNames}\n            {...props}\n          >\n            {buttonContent}\n          </Link>\n        </motion.div>\n      );\n    }\n\n    // استخدم button إذا لم يكن هناك href\n    return (\n      <motion.div whileTap={{ scale: 0.98 }}>\n        <button\n          ref={ref}\n          className={classNames}\n          disabled={disabled || isLoading}\n          {...props}\n        >\n          {buttonContent}\n        </button>\n      </motion.div>\n    );\n  }\n);\n\nHeroButton.displayName = 'HeroButton';\n\nexport { HeroButton };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAUA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAC1B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC/F,MAAM,aAAa;IAEnB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAClB,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX,aAAa,cACb;IAGF,mBAAmB;IACnB,MAAM,8BACJ;;YACG,0BACC,6LAAC;gBAAK,WAAU;;;;;uBACd;YAEH,OAAO,aAAa,yBACnB,6LAAC;gBAAK,WAAU;0BAAiB;;;;;qCAEjC,6LAAC;gBAAK,WAAU;0BAAiB;;;;;;YAIlC,CAAC,YAAY,aAAa,YAAY,eAAe,YAAY,QAAQ,mBACxE,6LAAC;gBAAK,WAAU;;;;;;;;IAKtB,iCAAiC;IACjC,IAAI,MAAM;QACR,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YAAC,UAAU;gBAAE,OAAO;YAAK;sBAClC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gBACH,MAAM;gBACN,WAAW;gBACV,GAAG,KAAK;0BAER;;;;;;;;;;;IAIT;IAEA,qCAAqC;IACrC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QAAC,UAAU;YAAE,OAAO;QAAK;kBAClC,cAAA,6LAAC;YACC,KAAK;YACL,WAAW;YACX,UAAU,YAAY;YACrB,GAAG,KAAK;sBAER;;;;;;;;;;;AAIT;;AAGF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/forms/WholesaleQuoteForm.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { Button } from '../ui/Button';\nimport { Input } from '../ui/Input';\nimport { Card } from '../ui/Card';\nimport { Send, Upload, CheckCircle, X } from 'lucide-react';\nimport { useAuthStore } from '../../stores/authStore';\nimport { useTranslation } from '../../translations';\nimport { useThemeStore } from '../../stores/themeStore';\nimport { cn } from '../../lib/utils';\nimport { Product } from '../../types/index';\n\ninterface WholesaleQuoteFormProps {\n  isOpen: boolean;\n  onClose: () => void;\n  isCustomProduct?: boolean;\n  serviceName?: string;\n  product?: Product;\n  selectedProduct?: Product;\n  initialQuantity?: number;\n}\n\nexport function WholesaleQuoteForm({ isOpen, onClose, isCustomProduct = false, serviceName, product, selectedProduct, initialQuantity }: WholesaleQuoteFormProps) {\n  const [formData, setFormData] = useState({\n    companyName: '',\n    contactName: '',\n    email: '',\n    phone: '',\n    productType: '',\n    specifications: '',\n    targetQuantity: '',\n    targetPrice: '',\n    timeline: '',\n    additionalNotes: '',\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isSubmitted, setIsSubmitted] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const { user } = useAuthStore();\n  const { t } = useTranslation();\n  const { isDarkMode } = useThemeStore();\n\n  // تعبئة بيانات المستخدم تلقائيًا إذا كان مسجل الدخول\n  useEffect(() => {\n    if (user) {\n      setFormData(prev => ({\n        ...prev,\n        contactName: user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : prev.contactName,\n        email: user.email || prev.email,\n      }));\n    }\n  }, [user]);\n\n  // Pre-fill productType if product is provided\n  useEffect(() => {\n    const productToUse = selectedProduct || product;\n    if (productToUse) {\n      setFormData(prev => ({\n        ...prev,\n        productType: productToUse.name || prev.productType,\n        specifications: productToUse.specifications ?\n          Object.entries(productToUse.specifications)\n            .map(([key, value]) => `${key}: ${value}`)\n            .join('\\n') :\n          prev.specifications,\n        targetQuantity: initialQuantity ? initialQuantity.toString() : prev.targetQuantity,\n      }));\n    }\n  }, [product, selectedProduct, initialQuantity]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError(null);\n    setIsSubmitting(true);\n\n    try {\n      // محاكاة إرسال النموذج - لا يتطلب تسجيل الدخول\n      console.log('Quote request submitted:', formData);\n      console.log('Product info:', selectedProduct || product);\n\n      // محاكاة تأخير الشبكة\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      setIsSubmitted(true);\n\n      // إغلاق النموذج بعد فترة قصيرة\n      setTimeout(() => {\n        onClose();\n      }, 2000);\n    } catch (err) {\n      console.error('Error submitting form:', err);\n      setError(t('wholesale.submitError'));\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  // إذا لم يكن المودال مفتوحًا، لا نعرض شيئًا\n  if (!isOpen) {\n    return null;\n  }\n\n  // إذا تم إرسال النموذج بنجاح\n  if (isSubmitted) {\n    return (\n      <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50\">\n        <div className=\"w-full max-w-md\">\n          <Card className={cn(\"relative p-6\", isDarkMode ? \"bg-slate-800\" : \"bg-white\")}>\n            {/* Close Button */}\n            <button\n              onClick={onClose}\n              className={cn(\n                \"absolute top-4 right-4 z-10 p-2 rounded-full transition-colors\",\n                \"hover:bg-slate-100 dark:hover:bg-slate-700\",\n                \"focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\",\n                \"dark:focus:ring-offset-slate-800\"\n              )}\n              aria-label={t('common.close')}\n              type=\"button\"\n            >\n              <X className=\"h-5 w-5 text-slate-500 dark:text-slate-400\" />\n            </button>\n\n            <div className=\"text-center py-8\">\n              <div className={cn(\n                \"w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4\",\n                isDarkMode ? \"bg-green-900/20\" : \"bg-green-100\"\n              )}>\n                <CheckCircle className={cn(\"h-8 w-8\", isDarkMode ? \"text-green-400\" : \"text-green-600\")} />\n              </div>\n              <h3 className={cn(\"text-xl font-semibold mb-2\", isDarkMode ? \"text-white\" : \"text-slate-900\")}>\n                {t('wholesale.requestSubmitted')}\n              </h3>\n              <p className={cn(\"mb-6\", isDarkMode ? \"text-slate-300\" : \"text-slate-600\")}>\n                {t('wholesale.thankYou')}\n              </p>\n              <Button onClick={onClose}>\n                {t('wholesale.close')}\n              </Button>\n            </div>\n          </Card>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50\">\n      <div className=\"w-full max-w-2xl max-h-[90vh] overflow-y-auto\">\n        <Card className={cn(\"relative p-6\", isDarkMode ? \"bg-slate-800\" : \"bg-white\")}>\n          {/* Close Button */}\n          <button\n            onClick={onClose}\n            className={cn(\n              \"absolute top-4 right-4 z-10 p-2 rounded-full transition-colors\",\n              \"hover:bg-slate-100 dark:hover:bg-slate-700\",\n              \"focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\",\n              \"dark:focus:ring-offset-slate-800\"\n            )}\n            aria-label={t('common.close')}\n            type=\"button\"\n          >\n            <X className=\"h-5 w-5 text-slate-500 dark:text-slate-400\" />\n          </button>\n\n          <h3 className={cn(\"text-xl font-semibold mb-4 pr-12\", isDarkMode ? \"text-white\" : \"text-slate-900\")}>\n            {isCustomProduct ? t('wholesale.customProductTitle') : t('wholesale.wholesaleTitle')}\n          </h3>\n\n          {error && (\n            <div className={cn(\n              \"p-3 rounded-md mb-4\",\n              isDarkMode ? \"bg-red-900/20 text-red-300\" : \"bg-red-50 text-red-600\"\n            )}>\n              {error}\n            </div>\n          )}\n\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className={cn(\"block text-sm font-medium mb-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                  {t('wholesale.companyName')}\n                </label>\n                <Input\n                  name=\"companyName\"\n                  value={formData.companyName}\n                  onChange={handleChange}\n                  required\n                />\n              </div>\n\n              <div>\n                <label className={cn(\"block text-sm font-medium mb-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                  {t('wholesale.contactName')}\n                </label>\n                <Input\n                  name=\"contactName\"\n                  value={formData.contactName}\n                  onChange={handleChange}\n                  required\n                />\n              </div>\n\n              <div>\n                <label className={cn(\"block text-sm font-medium mb-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                  {t('wholesale.email')}\n                </label>\n                <Input\n                  type=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  required\n                />\n              </div>\n\n              <div>\n                <label className={cn(\"block text-sm font-medium mb-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                  {t('wholesale.phone')}\n                </label>\n                <Input\n                  type=\"tel\"\n                  name=\"phone\"\n                  value={formData.phone}\n                  onChange={handleChange}\n                  required\n                />\n              </div>\n            </div>\n\n            <div>\n              <label className={cn(\"block text-sm font-medium mb-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                {t('wholesale.productType')}\n              </label>\n              <Input\n                name=\"productType\"\n                value={formData.productType}\n                onChange={handleChange}\n                required\n                placeholder={t('wholesale.productTypePlaceholder')}\n              />\n            </div>\n\n            <div>\n              <label className={cn(\"block text-sm font-medium mb-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                {t('wholesale.specifications')}\n              </label>\n              <textarea\n                name=\"specifications\"\n                value={formData.specifications}\n                onChange={handleChange}\n                required\n                className={cn(\n                  \"w-full min-h-[100px] px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                  isDarkMode ? \"bg-slate-700 border-slate-600 text-white\" : \"bg-white border-slate-300\"\n                )}\n                placeholder={t('wholesale.specificationsPlaceholder')}\n              />\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className={cn(\"block text-sm font-medium mb-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                  {t('wholesale.targetQuantity')}\n                </label>\n                <Input\n                  name=\"targetQuantity\"\n                  value={formData.targetQuantity}\n                  onChange={handleChange}\n                  required\n                  placeholder={t('wholesale.targetQuantityPlaceholder')}\n                />\n              </div>\n\n              <div>\n                <label className={cn(\"block text-sm font-medium mb-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                  {t('wholesale.targetPrice')}\n                </label>\n                <Input\n                  name=\"targetPrice\"\n                  value={formData.targetPrice}\n                  onChange={handleChange}\n                  placeholder={t('wholesale.targetPricePlaceholder')}\n                />\n              </div>\n            </div>\n\n            <div>\n              <label className={cn(\"block text-sm font-medium mb-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                {t('wholesale.timeline')}\n              </label>\n              <Input\n                name=\"timeline\"\n                value={formData.timeline}\n                onChange={handleChange}\n                required\n                placeholder={t('wholesale.timelinePlaceholder')}\n              />\n            </div>\n\n            <div>\n              <label className={cn(\"block text-sm font-medium mb-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                {t('wholesale.additionalNotes')}\n              </label>\n              <textarea\n                name=\"additionalNotes\"\n                value={formData.additionalNotes}\n                onChange={handleChange}\n                className={cn(\n                  \"w-full min-h-[100px] px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                  isDarkMode ? \"bg-slate-700 border-slate-600 text-white\" : \"bg-white border-slate-300\"\n                )}\n                placeholder={t('wholesale.additionalNotesPlaceholder')}\n              />\n            </div>\n\n            {isCustomProduct && (\n              <div className=\"border-t pt-4\">\n                <label className={cn(\"block text-sm font-medium mb-2\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                  {t('wholesale.uploadFiles')}\n                </label>\n                <div className={cn(\n                  \"border-2 border-dashed rounded-lg p-4 text-center\",\n                  isDarkMode ? \"border-slate-600 bg-slate-700/30\" : \"border-slate-300 bg-slate-50\"\n                )}>\n                  <Upload className={cn(\"mx-auto h-8 w-8 mb-2\", isDarkMode ? \"text-slate-400\" : \"text-slate-400\")} />\n                  <p className={cn(\"text-sm\", isDarkMode ? \"text-slate-300\" : \"text-slate-600\")}>\n                    {t('wholesale.dropFilesHere')}\n                  </p>\n                  <input\n                    type=\"file\"\n                    multiple\n                    className=\"hidden\"\n                    accept=\".pdf,.doc,.docx,.jpg,.jpeg,.png\"\n                    id=\"file-upload\"\n                  />\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    className=\"mt-2\"\n                    onClick={() => {\n                      const fileInput = document.getElementById('file-upload') as HTMLInputElement;\n                      if (fileInput) fileInput.click();\n                    }}\n                  >\n                    {t('wholesale.selectFiles')}\n                  </Button>\n                </div>\n              </div>\n            )}\n\n            <div className=\"flex justify-end gap-2\">\n              <Button type=\"button\" variant=\"outline\" onClick={onClose}>\n                {t('wholesale.cancel')}\n              </Button>\n              <Button\n                type=\"submit\"\n                className=\"flex items-center gap-2\"\n                disabled={isSubmitting}\n              >\n                {!isSubmitting && <Send className=\"w-4 h-4\" />}\n                {isSubmitting ? t('wholesale.submitting') : t('wholesale.submitRequest')}\n              </Button>\n            </div>\n          </form>\n        </Card>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAaO,SAAS,mBAAmB,EAAE,MAAM,EAAE,OAAO,EAAE,kBAAkB,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,eAAe,EAAE,eAAe,EAA2B;;IAC9J,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,aAAa;QACb,aAAa;QACb,OAAO;QACP,OAAO;QACP,aAAa;QACb,gBAAgB;QAChB,gBAAgB;QAChB,aAAa;QACb,UAAU;QACV,iBAAiB;IACnB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD;IAEnC,qDAAqD;IACrD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,MAAM;gBACR;oDAAY,CAAA,OAAQ,CAAC;4BACnB,GAAG,IAAI;4BACP,aAAa,KAAK,SAAS,IAAI,KAAK,QAAQ,GAAG,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GAAG,KAAK,WAAW;4BACtG,OAAO,KAAK,KAAK,IAAI,KAAK,KAAK;wBACjC,CAAC;;YACH;QACF;uCAAG;QAAC;KAAK;IAET,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,eAAe,mBAAmB;YACxC,IAAI,cAAc;gBAChB;oDAAY,CAAA,OAAQ,CAAC;4BACnB,GAAG,IAAI;4BACP,aAAa,aAAa,IAAI,IAAI,KAAK,WAAW;4BAClD,gBAAgB,aAAa,cAAc,GACzC,OAAO,OAAO,CAAC,aAAa,cAAc,EACvC,GAAG;gEAAC,CAAC,CAAC,KAAK,MAAM,GAAK,GAAG,IAAI,EAAE,EAAE,OAAO;+DACxC,IAAI,CAAC,QACR,KAAK,cAAc;4BACrB,gBAAgB,kBAAkB,gBAAgB,QAAQ,KAAK,KAAK,cAAc;wBACpF,CAAC;;YACH;QACF;uCAAG;QAAC;QAAS;QAAiB;KAAgB;IAE9C,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QACT,gBAAgB;QAEhB,IAAI;YACF,+CAA+C;YAC/C,QAAQ,GAAG,CAAC,4BAA4B;YACxC,QAAQ,GAAG,CAAC,iBAAiB,mBAAmB;YAEhD,sBAAsB;YACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,eAAe;YAEf,+BAA+B;YAC/B,WAAW;gBACT;YACF,GAAG;QACL,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS,EAAE;QACb,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IACjD;IAEA,4CAA4C;IAC5C,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,6BAA6B;IAC7B,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,aAAa,iBAAiB;;sCAEhE,6LAAC;4BACC,SAAS;4BACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA,8CACA,8EACA;4BAEF,cAAY,EAAE;4BACd,MAAK;sCAEL,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;sCAGf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,wEACA,aAAa,oBAAoB;8CAEjC,cAAA,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,aAAa,mBAAmB;;;;;;;;;;;8CAExE,6LAAC;oCAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,aAAa,eAAe;8CACzE,EAAE;;;;;;8CAEL,6LAAC;oCAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,aAAa,mBAAmB;8CACtD,EAAE;;;;;;8CAEL,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;8CACd,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOjB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,aAAa,iBAAiB;;kCAEhE,6LAAC;wBACC,SAAS;wBACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA,8CACA,8EACA;wBAEF,cAAY,EAAE;wBACd,MAAK;kCAEL,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;kCAGf,6LAAC;wBAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC,aAAa,eAAe;kCAC/E,kBAAkB,EAAE,kCAAkC,EAAE;;;;;;oBAG1D,uBACC,6LAAC;wBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,uBACA,aAAa,+BAA+B;kCAE3C;;;;;;kCAIL,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;0DACpF,EAAE;;;;;;0DAEL,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU;gDACV,QAAQ;;;;;;;;;;;;kDAIZ,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;0DACpF,EAAE;;;;;;0DAEL,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU;gDACV,QAAQ;;;;;;;;;;;;kDAIZ,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;0DACpF,EAAE;;;;;;0DAEL,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,QAAQ;;;;;;;;;;;;kDAIZ,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;0DACpF,EAAE;;;;;;0DAEL,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,QAAQ;;;;;;;;;;;;;;;;;;0CAKd,6LAAC;;kDACC,6LAAC;wCAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;kDACpF,EAAE;;;;;;kDAEL,6LAAC,oIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,OAAO,SAAS,WAAW;wCAC3B,UAAU;wCACV,QAAQ;wCACR,aAAa,EAAE;;;;;;;;;;;;0CAInB,6LAAC;;kDACC,6LAAC;wCAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;kDACpF,EAAE;;;;;;kDAEL,6LAAC;wCACC,MAAK;wCACL,OAAO,SAAS,cAAc;wCAC9B,UAAU;wCACV,QAAQ;wCACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2GACA,aAAa,6CAA6C;wCAE5D,aAAa,EAAE;;;;;;;;;;;;0CAInB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;0DACpF,EAAE;;;;;;0DAEL,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,OAAO,SAAS,cAAc;gDAC9B,UAAU;gDACV,QAAQ;gDACR,aAAa,EAAE;;;;;;;;;;;;kDAInB,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;0DACpF,EAAE;;;;;;0DAEL,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU;gDACV,aAAa,EAAE;;;;;;;;;;;;;;;;;;0CAKrB,6LAAC;;kDACC,6LAAC;wCAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;kDACpF,EAAE;;;;;;kDAEL,6LAAC,oIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,OAAO,SAAS,QAAQ;wCACxB,UAAU;wCACV,QAAQ;wCACR,aAAa,EAAE;;;;;;;;;;;;0CAInB,6LAAC;;kDACC,6LAAC;wCAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;kDACpF,EAAE;;;;;;kDAEL,6LAAC;wCACC,MAAK;wCACL,OAAO,SAAS,eAAe;wCAC/B,UAAU;wCACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2GACA,aAAa,6CAA6C;wCAE5D,aAAa,EAAE;;;;;;;;;;;;4BAIlB,iCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;kDACpF,EAAE;;;;;;kDAEL,6LAAC;wCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,qDACA,aAAa,qCAAqC;;0DAElD,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB,aAAa,mBAAmB;;;;;;0DAC9E,6LAAC;gDAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,aAAa,mBAAmB;0DACzD,EAAE;;;;;;0DAEL,6LAAC;gDACC,MAAK;gDACL,QAAQ;gDACR,WAAU;gDACV,QAAO;gDACP,IAAG;;;;;;0DAEL,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS;oDACP,MAAM,YAAY,SAAS,cAAc,CAAC;oDAC1C,IAAI,WAAW,UAAU,KAAK;gDAChC;0DAEC,EAAE;;;;;;;;;;;;;;;;;;0CAMX,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAQ;wCAAU,SAAS;kDAC9C,EAAE;;;;;;kDAEL,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;wCACV,UAAU;;4CAET,CAAC,8BAAgB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACjC,eAAe,EAAE,0BAA0B,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9D;GAlWgB;;QAiBG,6HAAA,CAAA,eAAY;QACf,+HAAA,CAAA,iBAAc;QACL,8HAAA,CAAA,gBAAa;;;KAnBtB", "debugId": null}}, {"offset": {"line": 811, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/ui/Form.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, ReactNode, FormEvent } from 'react';\nimport { cn } from '../../lib/utils';\n\n// تعريف نوع البيانات للحقول\nexport type FormFieldType = {\n  name: string;\n  value: any;\n  error?: string;\n  touched?: boolean;\n  required?: boolean;\n  validators?: ((value: any, formValues: Record<string, any>) => string | undefined)[];\n};\n\n// تعريف سياق النموذج\ntype FormContextType = {\n  values: Record<string, any>;\n  errors: Record<string, string | undefined>;\n  touched: Record<string, boolean>;\n  setFieldValue: (name: string, value: any) => void;\n  setFieldError: (name: string, error?: string) => void;\n  setFieldTouched: (name: string, touched?: boolean) => void;\n  registerField: (field: FormFieldType) => void;\n  unregisterField: (name: string) => void;\n  validateField: (name: string) => boolean;\n  validateForm: () => boolean;\n  resetForm: () => void;\n  submitForm: () => void;\n  isSubmitting: boolean;\n};\n\n// إنشاء سياق النموذج\nconst FormContext = createContext<FormContextType | undefined>(undefined);\n\n// Hook لاستخدام سياق النموذج\nexport const useForm = () => {\n  const context = useContext(FormContext);\n  if (!context) {\n    throw new Error('useForm must be used within a FormProvider');\n  }\n  return context;\n};\n\n// خصائص مكون النموذج\ninterface FormProps {\n  children: ReactNode;\n  initialValues?: Record<string, any>;\n  onSubmit?: (values: Record<string, any>) => void | Promise<void>;\n  onReset?: () => void;\n  className?: string;\n  validateOnChange?: boolean;\n  validateOnBlur?: boolean;\n  validateOnSubmit?: boolean;\n}\n\n// مكون النموذج\nexport function Form({\n  children,\n  initialValues = {},\n  onSubmit,\n  onReset,\n  className,\n  validateOnChange = true,\n  validateOnBlur = true,\n  validateOnSubmit = true,\n}: FormProps) {\n  const [values, setValues] = useState<Record<string, any>>(initialValues);\n  const [errors, setErrors] = useState<Record<string, string | undefined>>({});\n  const [touched, setTouched] = useState<Record<string, boolean>>({});\n  const [fields, setFields] = useState<Record<string, FormFieldType>>({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // تسجيل حقل جديد\n  const registerField = (field: FormFieldType) => {\n    setFields(prev => ({\n      ...prev,\n      [field.name]: field\n    }));\n\n    // تعيين القيمة الأولية إذا لم تكن موجودة\n    if (values[field.name] === undefined) {\n      setFieldValue(field.name, field.value);\n    }\n  };\n\n  // إلغاء تسجيل حقل\n  const unregisterField = (name: string) => {\n    setFields(prev => {\n      const newFields = { ...prev };\n      delete newFields[name];\n      return newFields;\n    });\n  };\n\n  // تعيين قيمة حقل\n  const setFieldValue = (name: string, value: any) => {\n    setValues(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // التحقق من صحة الحقل عند التغيير إذا كان مطلوبًا\n    if (validateOnChange && touched[name]) {\n      validateField(name);\n    }\n  };\n\n  // تعيين خطأ حقل\n  const setFieldError = (name: string, error?: string) => {\n    setErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n  };\n\n  // تعيين حالة لمس الحقل\n  const setFieldTouched = (name: string, isTouched: boolean = true) => {\n    setTouched(prev => ({\n      ...prev,\n      [name]: isTouched\n    }));\n\n    // التحقق من صحة الحقل عند فقدان التركيز إذا كان مطلوبًا\n    if (validateOnBlur && isTouched) {\n      validateField(name);\n    }\n  };\n\n  // التحقق من صحة حقل\n  const validateField = (name: string): boolean => {\n    const field = fields[name];\n    if (!field) return true;\n\n    const value = values[name];\n    let error: string | undefined;\n\n    // التحقق من الحقل المطلوب\n    if (field.required && (value === undefined || value === null || value === '')) {\n      error = 'هذا الحقل مطلوب';\n    }\n    // تنفيذ المصادقات المخصصة\n    else if (field.validators && field.validators.length > 0) {\n      for (const validator of field.validators) {\n        const validationError = validator(value, values);\n        if (validationError) {\n          error = validationError;\n          break;\n        }\n      }\n    }\n\n    setFieldError(name, error);\n    return !error;\n  };\n\n  // التحقق من صحة النموذج بالكامل\n  const validateForm = (): boolean => {\n    let isValid = true;\n\n    // التحقق من جميع الحقول المسجلة\n    Object.keys(fields).forEach(fieldName => {\n      const fieldIsValid = validateField(fieldName);\n      if (!fieldIsValid) {\n        isValid = false;\n      }\n\n      // تعيين جميع الحقول كملموسة\n      setFieldTouched(fieldName, true);\n    });\n\n    return isValid;\n  };\n\n  // إعادة تعيين النموذج\n  const resetForm = () => {\n    setValues(initialValues);\n    setErrors({});\n    setTouched({});\n\n    if (onReset) {\n      onReset();\n    }\n  };\n\n  // تقديم النموذج\n  const submitForm = async () => {\n    // التحقق من صحة النموذج قبل التقديم إذا كان مطلوبًا\n    if (validateOnSubmit) {\n      const isValid = validateForm();\n      if (!isValid) return;\n    }\n\n    if (onSubmit) {\n      setIsSubmitting(true);\n      try {\n        await onSubmit(values);\n      } finally {\n        setIsSubmitting(false);\n      }\n    }\n  };\n\n  // معالج تقديم النموذج\n  const handleSubmit = (e: FormEvent) => {\n    e.preventDefault();\n    submitForm();\n  };\n\n  // قيمة سياق النموذج\n  const formContextValue: FormContextType = {\n    values,\n    errors,\n    touched,\n    setFieldValue,\n    setFieldError,\n    setFieldTouched,\n    registerField,\n    unregisterField,\n    validateField,\n    validateForm,\n    resetForm,\n    submitForm,\n    isSubmitting\n  };\n\n  return (\n    <FormContext.Provider value={formContextValue}>\n      <form onSubmit={handleSubmit} className={cn('space-y-4', className)}>\n        {children}\n      </form>\n    </FormContext.Provider>\n  );\n}\n\n// مكون حقل النموذج\ninterface FormFieldProps {\n  name: string;\n  children: ReactNode | ((props: {\n    value: any;\n    error?: string;\n    touched?: boolean;\n    onChange: (value: any) => void;\n    onBlur: () => void;\n  }) => ReactNode);\n  initialValue?: any;\n  required?: boolean;\n  validators?: ((value: any, formValues: Record<string, any>) => string | undefined)[];\n}\n\nexport function FormField({\n  name,\n  children,\n  initialValue = '',\n  required = false,\n  validators = [],\n}: FormFieldProps) {\n  const {\n    values,\n    errors,\n    touched,\n    setFieldValue,\n    setFieldTouched,\n    registerField,\n    unregisterField\n  } = useForm();\n\n  // تسجيل الحقل عند التحميل وإلغاء التسجيل عند التفريغ\n  React.useEffect(() => {\n    registerField({\n      name,\n      value: initialValue,\n      required,\n      validators\n    });\n\n    return () => {\n      unregisterField(name);\n    };\n  }, [name, initialValue, required, validators.length]);\n\n  // معالجات الأحداث\n  const handleChange = (value: any) => {\n    setFieldValue(name, value);\n  };\n\n  const handleBlur = () => {\n    setFieldTouched(name, true);\n  };\n\n  // تقديم الأطفال\n  if (typeof children === 'function') {\n    return children({\n      value: values[name],\n      error: errors[name],\n      touched: touched[name],\n      onChange: handleChange,\n      onBlur: handleBlur\n    });\n  }\n\n  return children;\n}\n\n// مكون زر تقديم النموذج\ninterface FormSubmitProps {\n  children: ReactNode;\n  className?: string;\n  disabled?: boolean;\n}\n\nexport function FormSubmit({ children, className, disabled }: FormSubmitProps) {\n  const { isSubmitting } = useForm();\n\n  // بدلاً من استخدام زر، نستخدم div لتجنب مشكلة تداخل الأزرار\n  return (\n    <div className={className}>\n      {React.cloneElement(children as React.ReactElement, {\n        type: \"submit\",\n        disabled: disabled || isSubmitting\n      })}\n    </div>\n  );\n}\n\n// مكون زر إعادة تعيين النموذج\ninterface FormResetProps {\n  children: ReactNode;\n  className?: string;\n  disabled?: boolean;\n}\n\nexport function FormReset({ children, className, disabled }: FormResetProps) {\n  const { resetForm, isSubmitting } = useForm();\n\n  // بدلاً من استخدام زر، نستخدم div لتجنب مشكلة تداخل الأزرار\n  return (\n    <div className={className}>\n      {React.cloneElement(children as React.ReactElement, {\n        type: \"button\",\n        disabled: disabled || isSubmitting,\n        onClick: resetForm\n      })}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;;;AAHA;;;AAgCA,qBAAqB;AACrB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAGxD,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAqBN,SAAS,KAAK,EACnB,QAAQ,EACR,gBAAgB,CAAC,CAAC,EAClB,QAAQ,EACR,OAAO,EACP,SAAS,EACT,mBAAmB,IAAI,EACvB,iBAAiB,IAAI,EACrB,mBAAmB,IAAI,EACb;;IACV,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAC1D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsC,CAAC;IAC1E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACjE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiC,CAAC;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,iBAAiB;IACjB,MAAM,gBAAgB,CAAC;QACrB,UAAU,CAAA,OAAQ,CAAC;gBACjB,GAAG,IAAI;gBACP,CAAC,MAAM,IAAI,CAAC,EAAE;YAChB,CAAC;QAED,yCAAyC;QACzC,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,KAAK,WAAW;YACpC,cAAc,MAAM,IAAI,EAAE,MAAM,KAAK;QACvC;IACF;IAEA,kBAAkB;IAClB,MAAM,kBAAkB,CAAC;QACvB,UAAU,CAAA;YACR,MAAM,YAAY;gBAAE,GAAG,IAAI;YAAC;YAC5B,OAAO,SAAS,CAAC,KAAK;YACtB,OAAO;QACT;IACF;IAEA,iBAAiB;IACjB,MAAM,gBAAgB,CAAC,MAAc;QACnC,UAAU,CAAA,OAAQ,CAAC;gBACjB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QAED,kDAAkD;QAClD,IAAI,oBAAoB,OAAO,CAAC,KAAK,EAAE;YACrC,cAAc;QAChB;IACF;IAEA,gBAAgB;IAChB,MAAM,gBAAgB,CAAC,MAAc;QACnC,UAAU,CAAA,OAAQ,CAAC;gBACjB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,uBAAuB;IACvB,MAAM,kBAAkB,CAAC,MAAc,YAAqB,IAAI;QAC9D,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QAED,wDAAwD;QACxD,IAAI,kBAAkB,WAAW;YAC/B,cAAc;QAChB;IACF;IAEA,oBAAoB;IACpB,MAAM,gBAAgB,CAAC;QACrB,MAAM,QAAQ,MAAM,CAAC,KAAK;QAC1B,IAAI,CAAC,OAAO,OAAO;QAEnB,MAAM,QAAQ,MAAM,CAAC,KAAK;QAC1B,IAAI;QAEJ,0BAA0B;QAC1B,IAAI,MAAM,QAAQ,IAAI,CAAC,UAAU,aAAa,UAAU,QAAQ,UAAU,EAAE,GAAG;YAC7E,QAAQ;QACV,OAEK,IAAI,MAAM,UAAU,IAAI,MAAM,UAAU,CAAC,MAAM,GAAG,GAAG;YACxD,KAAK,MAAM,aAAa,MAAM,UAAU,CAAE;gBACxC,MAAM,kBAAkB,UAAU,OAAO;gBACzC,IAAI,iBAAiB;oBACnB,QAAQ;oBACR;gBACF;YACF;QACF;QAEA,cAAc,MAAM;QACpB,OAAO,CAAC;IACV;IAEA,gCAAgC;IAChC,MAAM,eAAe;QACnB,IAAI,UAAU;QAEd,gCAAgC;QAChC,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;YAC1B,MAAM,eAAe,cAAc;YACnC,IAAI,CAAC,cAAc;gBACjB,UAAU;YACZ;YAEA,4BAA4B;YAC5B,gBAAgB,WAAW;QAC7B;QAEA,OAAO;IACT;IAEA,sBAAsB;IACtB,MAAM,YAAY;QAChB,UAAU;QACV,UAAU,CAAC;QACX,WAAW,CAAC;QAEZ,IAAI,SAAS;YACX;QACF;IACF;IAEA,gBAAgB;IAChB,MAAM,aAAa;QACjB,oDAAoD;QACpD,IAAI,kBAAkB;YACpB,MAAM,UAAU;YAChB,IAAI,CAAC,SAAS;QAChB;QAEA,IAAI,UAAU;YACZ,gBAAgB;YAChB,IAAI;gBACF,MAAM,SAAS;YACjB,SAAU;gBACR,gBAAgB;YAClB;QACF;IACF;IAEA,sBAAsB;IACtB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB;IACF;IAEA,oBAAoB;IACpB,MAAM,mBAAoC;QACxC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC3B,cAAA,6LAAC;YAAK,UAAU;YAAc,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBACtD;;;;;;;;;;;AAIT;IAhLgB;KAAA;AAiMT,SAAS,UAAU,EACxB,IAAI,EACJ,QAAQ,EACR,eAAe,EAAE,EACjB,WAAW,KAAK,EAChB,aAAa,EAAE,EACA;;IACf,MAAM,EACJ,MAAM,EACN,MAAM,EACN,OAAO,EACP,aAAa,EACb,eAAe,EACf,aAAa,EACb,eAAe,EAChB,GAAG;IAEJ,qDAAqD;IACrD,6JAAA,CAAA,UAAK,CAAC,SAAS;+BAAC;YACd,cAAc;gBACZ;gBACA,OAAO;gBACP;gBACA;YACF;YAEA;uCAAO;oBACL,gBAAgB;gBAClB;;QACF;8BAAG;QAAC;QAAM;QAAc;QAAU,WAAW,MAAM;KAAC;IAEpD,kBAAkB;IAClB,MAAM,eAAe,CAAC;QACpB,cAAc,MAAM;IACtB;IAEA,MAAM,aAAa;QACjB,gBAAgB,MAAM;IACxB;IAEA,gBAAgB;IAChB,IAAI,OAAO,aAAa,YAAY;QAClC,OAAO,SAAS;YACd,OAAO,MAAM,CAAC,KAAK;YACnB,OAAO,MAAM,CAAC,KAAK;YACnB,SAAS,OAAO,CAAC,KAAK;YACtB,UAAU;YACV,QAAQ;QACV;IACF;IAEA,OAAO;AACT;IApDgB;;QAeV;;;MAfU;AA6DT,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAmB;;IAC3E,MAAM,EAAE,YAAY,EAAE,GAAG;IAEzB,4DAA4D;IAC5D,qBACE,6LAAC;QAAI,WAAW;kBACb,cAAA,6JAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAgC;YAClD,MAAM;YACN,UAAU,YAAY;QACxB;;;;;;AAGN;IAZgB;;QACW;;;MADX;AAqBT,SAAS,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAkB;;IACzE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG;IAEpC,4DAA4D;IAC5D,qBACE,6LAAC;QAAI,WAAW;kBACb,cAAA,6JAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAgC;YAClD,MAAM;YACN,UAAU,YAAY;YACtB,SAAS;QACX;;;;;;AAGN;IAbgB;;QACsB;;;MADtB", "debugId": null}}, {"offset": {"line": 1102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/ui/FormInput.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport { Input, InputProps } from './Input';\nimport { FormField } from './Form';\nimport { cn } from '../../lib/utils';\n\nexport interface FormInputProps extends Omit<InputProps, 'onChange' | 'onBlur' | 'value' | 'error'> {\n  name: string;\n  label?: string;\n  initialValue?: string;\n  required?: boolean;\n  helperText?: string;\n  leftIcon?: ReactNode;\n  rightIcon?: ReactNode;\n  validators?: ((value: any, formValues: Record<string, any>) => string | undefined)[];\n}\n\nexport function FormInput({\n  name,\n  label,\n  initialValue = '',\n  required = false,\n  helperText,\n  leftIcon,\n  rightIcon,\n  validators = [],\n  ...props\n}: FormInputProps) {\n  return (\n    <FormField\n      name={name}\n      initialValue={initialValue}\n      required={required}\n      validators={validators}\n    >\n      {({ value, error, onChange, onBlur }) => (\n        <Input\n          id={name}\n          name={name}\n          value={value || ''}\n          onChange={(e) => onChange(e.target.value)}\n          onBlur={onBlur}\n          error={error}\n          label={label}\n          helperText={!error ? helperText : undefined}\n          leftIcon={leftIcon}\n          rightIcon={rightIcon}\n          required={required}\n          {...props}\n        />\n      )}\n    </FormField>\n  );\n}\n\n// مصادقات شائعة الاستخدام\nexport const validators = {\n  // التحقق من البريد الإلكتروني\n  email: (value: string) => {\n    if (!value) return undefined;\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(value) ? undefined : 'يرجى إدخال بريد إلكتروني صالح';\n  },\n\n  // التحقق من الحد الأدنى للطول\n  minLength: (min: number) => (value: string) => {\n    if (!value) return undefined;\n    return value.length >= min ? undefined : `يجب أن يكون الطول ${min} أحرف على الأقل`;\n  },\n\n  // التحقق من الحد الأقصى للطول\n  maxLength: (max: number) => (value: string) => {\n    if (!value) return undefined;\n    return value.length <= max ? undefined : `يجب أن لا يتجاوز الطول ${max} أحرف`;\n  },\n\n  // التحقق من النطاق\n  range: (min: number, max: number) => (value: number) => {\n    if (value === undefined || value === null) return undefined;\n    return value >= min && value <= max ? undefined : `يجب أن تكون القيمة بين ${min} و ${max}`;\n  },\n\n  // التحقق من تطابق الحقول\n  matches: (matchField: string, message?: string) => (value: any, formValues: Record<string, any>) => {\n    return value === formValues[matchField] ? undefined : message || 'الحقول غير متطابقة';\n  },\n\n  // التحقق من الرقم\n  number: (value: any) => {\n    if (value === undefined || value === null || value === '') return undefined;\n    return !isNaN(Number(value)) ? undefined : 'يرجى إدخال رقم صالح';\n  },\n\n  // التحقق من الرقم الموجب\n  positiveNumber: (value: any) => {\n    if (value === undefined || value === null || value === '') return undefined;\n    const num = Number(value);\n    return !isNaN(num) && num > 0 ? undefined : 'يرجى إدخال رقم موجب';\n  },\n\n  // التحقق من رقم الهاتف\n  phone: (value: string) => {\n    if (!value) return undefined;\n    const phoneRegex = /^\\+?[0-9]{8,15}$/;\n    return phoneRegex.test(value) ? undefined : 'يرجى إدخال رقم هاتف صالح';\n  },\n\n  // التحقق من كلمة المرور القوية\n  strongPassword: (value: string) => {\n    if (!value) return undefined;\n\n    const hasUpperCase = /[A-Z]/.test(value);\n    const hasLowerCase = /[a-z]/.test(value);\n    const hasNumbers = /\\d/.test(value);\n    const hasSpecialChar = /[!@#$%^&*(),.?\":{}|<>]/.test(value);\n\n    if (value.length < 8) {\n      return 'يجب أن تكون كلمة المرور 8 أحرف على الأقل';\n    }\n\n    if (!(hasUpperCase && hasLowerCase && hasNumbers)) {\n      return 'يجب أن تحتوي كلمة المرور على أحرف كبيرة وصغيرة وأرقام';\n    }\n\n    if (!hasSpecialChar) {\n      return 'يجب أن تحتوي كلمة المرور على حرف خاص واحد على الأقل';\n    }\n\n    return undefined;\n  },\n\n  // التحقق من URL\n  url: (value: string) => {\n    if (!value) return undefined;\n    try {\n      new URL(value);\n      return undefined;\n    } catch {\n      return 'يرجى إدخال رابط صالح';\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAkBO,SAAS,UAAU,EACxB,IAAI,EACJ,KAAK,EACL,eAAe,EAAE,EACjB,WAAW,KAAK,EAChB,UAAU,EACV,QAAQ,EACR,SAAS,EACT,aAAa,EAAE,EACf,GAAG,OACY;IACf,qBACE,6LAAC,mIAAA,CAAA,YAAS;QACR,MAAM;QACN,cAAc;QACd,UAAU;QACV,YAAY;kBAEX,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,iBAClC,6LAAC,oIAAA,CAAA,QAAK;gBACJ,IAAI;gBACJ,MAAM;gBACN,OAAO,SAAS;gBAChB,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gBACxC,QAAQ;gBACR,OAAO;gBACP,OAAO;gBACP,YAAY,CAAC,QAAQ,aAAa;gBAClC,UAAU;gBACV,WAAW;gBACX,UAAU;gBACT,GAAG,KAAK;;;;;;;;;;;AAKnB;KApCgB;AAuCT,MAAM,aAAa;IACxB,8BAA8B;IAC9B,OAAO,CAAC;QACN,IAAI,CAAC,OAAO,OAAO;QACnB,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC,SAAS,YAAY;IAC9C;IAEA,8BAA8B;IAC9B,WAAW,CAAC,MAAgB,CAAC;YAC3B,IAAI,CAAC,OAAO,OAAO;YACnB,OAAO,MAAM,MAAM,IAAI,MAAM,YAAY,CAAC,kBAAkB,EAAE,IAAI,eAAe,CAAC;QACpF;IAEA,8BAA8B;IAC9B,WAAW,CAAC,MAAgB,CAAC;YAC3B,IAAI,CAAC,OAAO,OAAO;YACnB,OAAO,MAAM,MAAM,IAAI,MAAM,YAAY,CAAC,uBAAuB,EAAE,IAAI,KAAK,CAAC;QAC/E;IAEA,mBAAmB;IACnB,OAAO,CAAC,KAAa,MAAgB,CAAC;YACpC,IAAI,UAAU,aAAa,UAAU,MAAM,OAAO;YAClD,OAAO,SAAS,OAAO,SAAS,MAAM,YAAY,CAAC,uBAAuB,EAAE,IAAI,GAAG,EAAE,KAAK;QAC5F;IAEA,yBAAyB;IACzB,SAAS,CAAC,YAAoB,UAAqB,CAAC,OAAY;YAC9D,OAAO,UAAU,UAAU,CAAC,WAAW,GAAG,YAAY,WAAW;QACnE;IAEA,kBAAkB;IAClB,QAAQ,CAAC;QACP,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI,OAAO;QAClE,OAAO,CAAC,MAAM,OAAO,UAAU,YAAY;IAC7C;IAEA,yBAAyB;IACzB,gBAAgB,CAAC;QACf,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI,OAAO;QAClE,MAAM,MAAM,OAAO;QACnB,OAAO,CAAC,MAAM,QAAQ,MAAM,IAAI,YAAY;IAC9C;IAEA,uBAAuB;IACvB,OAAO,CAAC;QACN,IAAI,CAAC,OAAO,OAAO;QACnB,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC,SAAS,YAAY;IAC9C;IAEA,+BAA+B;IAC/B,gBAAgB,CAAC;QACf,IAAI,CAAC,OAAO,OAAO;QAEnB,MAAM,eAAe,QAAQ,IAAI,CAAC;QAClC,MAAM,eAAe,QAAQ,IAAI,CAAC;QAClC,MAAM,aAAa,KAAK,IAAI,CAAC;QAC7B,MAAM,iBAAiB,yBAAyB,IAAI,CAAC;QAErD,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,OAAO;QACT;QAEA,IAAI,CAAC,CAAC,gBAAgB,gBAAgB,UAAU,GAAG;YACjD,OAAO;QACT;QAEA,IAAI,CAAC,gBAAgB;YACnB,OAAO;QACT;QAEA,OAAO;IACT;IAEA,gBAAgB;IAChB,KAAK,CAAC;QACJ,IAAI,CAAC,OAAO,OAAO;QACnB,IAAI;YACF,IAAI,IAAI;YACR,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 1227, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/ui/animations/HoverAnimation.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode, useState, useEffect } from 'react';\nimport { motion, Variants } from 'framer-motion';\nimport { cn } from '../../../lib/utils';\n\ninterface HoverAnimationProps {\n  children: ReactNode;\n  className?: string;\n  animation?: 'scale' | 'lift' | 'glow' | 'rotate' | 'pulse' | 'bounce' | 'tilt' | 'shine' | 'border' | 'shadow' | 'fade';\n  scale?: number;\n  duration?: number;\n  disabled?: boolean;\n  as?: React.ElementType;\n  onClick?: () => void;\n}\n\nexport function HoverAnimation({\n  children,\n  className,\n  animation = 'scale',\n  scale = 1.05,\n  duration = 0.3,\n  disabled = false,\n  as = 'div',\n  onClick,\n  ...props\n}: HoverAnimationProps & React.HTMLAttributes<HTMLElement>) {\n  const [isMounted, setIsMounted] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n\n  useEffect(() => {\n    setIsMounted(true);\n    setIsMobile(window.innerWidth < 768);\n\n    const handleResize = () => {\n      setIsMobile(window.innerWidth < 768);\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // تعطيل الرسوم المتحركة على الأجهزة المحمولة إذا كان disabled = true\n  const isAnimationDisabled = disabled || (isMobile && typeof document !== 'undefined' && document.documentElement.classList.contains('mobile-device'));\n\n  // تحسين الأداء عن طريق استخدام will-change للعناصر التي تحتاج إلى تحسين أداء الرسوم المتحركة\n  useEffect(() => {\n    if (isMounted && !isAnimationDisabled) {\n      const needsHardwareAcceleration = ['lift', 'rotate', 'tilt', 'pulse', 'bounce'].includes(animation);\n      if (needsHardwareAcceleration) {\n        const element = document.querySelector(`.animation-${animation}`);\n        if (element) {\n          element.classList.add('will-change-transform');\n        }\n      }\n    }\n  }, [isMounted, isAnimationDisabled, animation]);\n\n  // تحديد متغيرات الرسوم المتحركة بناءً على النوع\n  const getVariants = (): Variants => {\n    const baseTransition = {\n      type: 'tween',\n      duration,\n    };\n\n    switch (animation) {\n      case 'scale':\n        return {\n          initial: {},\n          hover: {\n            scale,\n            transition: baseTransition\n          },\n          tap: {\n            scale: scale * 0.95,\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      case 'lift':\n        return {\n          initial: {},\n          hover: {\n            y: -8,\n            transition: baseTransition\n          },\n          tap: {\n            y: -4,\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      case 'glow':\n        return {\n          initial: {},\n          hover: {\n            boxShadow: '0 0 15px rgba(var(--color-primary-500), 0.5)',\n            transition: baseTransition\n          },\n          tap: {\n            boxShadow: '0 0 8px rgba(var(--color-primary-500), 0.3)',\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      case 'rotate':\n        return {\n          initial: {},\n          hover: {\n            rotate: 5,\n            transition: baseTransition\n          },\n          tap: {\n            rotate: 2,\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      case 'pulse':\n        return {\n          initial: {},\n          hover: {\n            scale: [1, scale, 1, scale, 1],\n            transition: {\n              duration: duration * 2,\n              repeat: Infinity,\n              repeatType: 'loop'\n            }\n          },\n          tap: {\n            scale: scale * 0.95,\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      case 'bounce':\n        return {\n          initial: {},\n          hover: {\n            y: [0, -10, 0],\n            transition: {\n              duration: duration * 1.5,\n              repeat: Infinity,\n              repeatType: 'loop'\n            }\n          },\n          tap: {\n            y: -5,\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      case 'tilt':\n        return {\n          initial: {},\n          hover: {\n            rotateX: -10,\n            rotateY: 10,\n            transition: baseTransition\n          },\n          tap: {\n            rotateX: -5,\n            rotateY: 5,\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      case 'shine':\n        return {\n          initial: {},\n          hover: {},\n          tap: {\n            scale: 0.98,\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      case 'border':\n        return {\n          initial: { borderColor: 'rgba(var(--color-primary-500), 0)' },\n          hover: {\n            borderColor: 'rgba(var(--color-primary-500), 1)',\n            borderWidth: '2px',\n            transition: baseTransition\n          },\n          tap: {\n            borderColor: 'rgba(var(--color-primary-600), 1)',\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      case 'shadow':\n        return {\n          initial: { boxShadow: '0 0 0 rgba(0, 0, 0, 0)' },\n          hover: {\n            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)',\n            y: -2,\n            transition: baseTransition\n          },\n          tap: {\n            boxShadow: '0 5px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',\n            y: -1,\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      case 'fade':\n        return {\n          initial: { opacity: 1 },\n          hover: {\n            opacity: 0.8,\n            transition: baseTransition\n          },\n          tap: {\n            opacity: 0.9,\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      default:\n        return {\n          initial: {},\n          hover: {},\n          tap: {}\n        };\n    }\n  };\n\n  const variants = getVariants();\n  const Component = motion[as as keyof typeof motion] || motion.div;\n\n  // إذا كانت الرسوم المتحركة معطلة، قم بإرجاع المحتوى بدون رسوم متحركة\n  if (isAnimationDisabled || !isMounted) {\n    const ElementType = as;\n    return (\n      <ElementType className={className} onClick={onClick} {...props}>\n        {children}\n      </ElementType>\n    );\n  }\n\n  return (\n    <Component\n      className={cn(\n        className,\n        `animation-${animation}`,\n        animation === 'shine' && 'group overflow-hidden relative',\n        animation === 'border' && 'border border-transparent',\n        animation === 'shadow' && 'transition-shadow'\n      )}\n      initial=\"initial\"\n      whileHover=\"hover\"\n      whileTap=\"tap\"\n      variants={variants}\n      onClick={onClick}\n      {...props}\n    >\n      {children}\n      {animation === 'shine' && (\n        <motion.div\n          className=\"absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent group-hover:translate-x-full\"\n          transition={{ duration: duration * 2, ease: 'linear' }}\n        />\n      )}\n      {animation === 'glow' && (\n        <motion.div\n          className=\"absolute inset-0 rounded-inherit opacity-0 group-hover:opacity-100\"\n          initial={{ opacity: 0, boxShadow: '0 0 0 rgba(var(--color-primary-500), 0)' }}\n          whileHover={{\n            opacity: 1,\n            boxShadow: '0 0 15px rgba(var(--color-primary-500), 0.5)',\n            transition: { duration }\n          }}\n        />\n      )}\n    </Component>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAiBO,SAAS,eAAe,EAC7B,QAAQ,EACR,SAAS,EACT,YAAY,OAAO,EACnB,QAAQ,IAAI,EACZ,WAAW,GAAG,EACd,WAAW,KAAK,EAChB,KAAK,KAAK,EACV,OAAO,EACP,GAAG,OACqD;;IACxD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,aAAa;YACb,YAAY,OAAO,UAAU,GAAG;YAEhC,MAAM;yDAAe;oBACnB,YAAY,OAAO,UAAU,GAAG;gBAClC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;4CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;mCAAG,EAAE;IAEL,qEAAqE;IACrE,MAAM,sBAAsB,YAAa,YAAY,OAAO,aAAa,eAAe,SAAS,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC;IAEpI,6FAA6F;IAC7F,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,aAAa,CAAC,qBAAqB;gBACrC,MAAM,4BAA4B;oBAAC;oBAAQ;oBAAU;oBAAQ;oBAAS;iBAAS,CAAC,QAAQ,CAAC;gBACzF,IAAI,2BAA2B;oBAC7B,MAAM,UAAU,SAAS,aAAa,CAAC,CAAC,WAAW,EAAE,WAAW;oBAChE,IAAI,SAAS;wBACX,QAAQ,SAAS,CAAC,GAAG,CAAC;oBACxB;gBACF;YACF;QACF;mCAAG;QAAC;QAAW;QAAqB;KAAU;IAE9C,gDAAgD;IAChD,MAAM,cAAc;QAClB,MAAM,iBAAiB;YACrB,MAAM;YACN;QACF;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,SAAS,CAAC;oBACV,OAAO;wBACL;wBACA,YAAY;oBACd;oBACA,KAAK;wBACH,OAAO,QAAQ;wBACf,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS,CAAC;oBACV,OAAO;wBACL,GAAG,CAAC;wBACJ,YAAY;oBACd;oBACA,KAAK;wBACH,GAAG,CAAC;wBACJ,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS,CAAC;oBACV,OAAO;wBACL,WAAW;wBACX,YAAY;oBACd;oBACA,KAAK;wBACH,WAAW;wBACX,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS,CAAC;oBACV,OAAO;wBACL,QAAQ;wBACR,YAAY;oBACd;oBACA,KAAK;wBACH,QAAQ;wBACR,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS,CAAC;oBACV,OAAO;wBACL,OAAO;4BAAC;4BAAG;4BAAO;4BAAG;4BAAO;yBAAE;wBAC9B,YAAY;4BACV,UAAU,WAAW;4BACrB,QAAQ;4BACR,YAAY;wBACd;oBACF;oBACA,KAAK;wBACH,OAAO,QAAQ;wBACf,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS,CAAC;oBACV,OAAO;wBACL,GAAG;4BAAC;4BAAG,CAAC;4BAAI;yBAAE;wBACd,YAAY;4BACV,UAAU,WAAW;4BACrB,QAAQ;4BACR,YAAY;wBACd;oBACF;oBACA,KAAK;wBACH,GAAG,CAAC;wBACJ,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS,CAAC;oBACV,OAAO;wBACL,SAAS,CAAC;wBACV,SAAS;wBACT,YAAY;oBACd;oBACA,KAAK;wBACH,SAAS,CAAC;wBACV,SAAS;wBACT,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS,CAAC;oBACV,OAAO,CAAC;oBACR,KAAK;wBACH,OAAO;wBACP,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS;wBAAE,aAAa;oBAAoC;oBAC5D,OAAO;wBACL,aAAa;wBACb,aAAa;wBACb,YAAY;oBACd;oBACA,KAAK;wBACH,aAAa;wBACb,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS;wBAAE,WAAW;oBAAyB;oBAC/C,OAAO;wBACL,WAAW;wBACX,GAAG,CAAC;wBACJ,YAAY;oBACd;oBACA,KAAK;wBACH,WAAW;wBACX,GAAG,CAAC;wBACJ,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS;wBAAE,SAAS;oBAAE;oBACtB,OAAO;wBACL,SAAS;wBACT,YAAY;oBACd;oBACA,KAAK;wBACH,SAAS;wBACT,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF;gBACE,OAAO;oBACL,SAAS,CAAC;oBACV,OAAO,CAAC;oBACR,KAAK,CAAC;gBACR;QACJ;IACF;IAEA,MAAM,WAAW;IACjB,MAAM,YAAY,6LAAA,CAAA,SAAM,CAAC,GAA0B,IAAI,6LAAA,CAAA,SAAM,CAAC,GAAG;IAEjE,qEAAqE;IACrE,IAAI,uBAAuB,CAAC,WAAW;QACrC,MAAM,cAAc;QACpB,qBACE,6LAAC;YAAY,WAAW;YAAW,SAAS;YAAU,GAAG,KAAK;sBAC3D;;;;;;IAGP;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,WACA,CAAC,UAAU,EAAE,WAAW,EACxB,cAAc,WAAW,kCACzB,cAAc,YAAY,6BAC1B,cAAc,YAAY;QAE5B,SAAQ;QACR,YAAW;QACX,UAAS;QACT,UAAU;QACV,SAAS;QACR,GAAG,KAAK;;YAER;YACA,cAAc,yBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,YAAY;oBAAE,UAAU,WAAW;oBAAG,MAAM;gBAAS;;;;;;YAGxD,cAAc,wBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,WAAW;gBAA0C;gBAC5E,YAAY;oBACV,SAAS;oBACT,WAAW;oBACX,YAAY;wBAAE;oBAAS;gBACzB;;;;;;;;;;;;AAKV;GA1PgB;KAAA", "debugId": null}}, {"offset": {"line": 1566, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/forms/NewsletterForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Mail, Send, Check, Bell, X } from 'lucide-react';\nimport { Form, FormSubmit } from '../ui/Form';\nimport { FormInput, validators } from '../ui/FormInput';\nimport { Button } from '../ui/Button';\nimport { useTranslation } from '../../translations';\nimport { cn } from '../../lib/utils';\nimport { HoverAnimation } from '../ui/animations/HoverAnimation';\n\ninterface NewsletterFormProps {\n  className?: string;\n  variant?: 'default' | 'inline' | 'card' | 'popup';\n  title?: string;\n  description?: string;\n  onSuccess?: () => void;\n  onClose?: () => void;\n}\n\nexport function NewsletterForm({\n  className,\n  variant = 'default',\n  title,\n  description,\n  onSuccess,\n  onClose\n}: NewsletterFormProps) {\n  const { t, currentLanguage } = useTranslation();\n  const [isSubmitted, setIsSubmitted] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n\n  const defaultTitle = currentLanguage === 'ar'\n    ? 'اشترك في نشرتنا الإخبارية'\n    : 'Subscribe to Our Newsletter';\n\n  const defaultDescription = currentLanguage === 'ar'\n    ? 'احصل على آخر العروض والمنتجات الجديدة مباشرة إلى بريدك الإلكتروني.'\n    : 'Get the latest offers and new products directly to your inbox.';\n\n  const handleSubmit = async (values: Record<string, any>) => {\n    setIsLoading(true);\n    \n    try {\n      // محاكاة إرسال النموذج إلى API\n      console.log('Newsletter subscription:', values);\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      \n      setIsSubmitted(true);\n      if (onSuccess) {\n        onSuccess();\n      }\n    } catch (error) {\n      console.error('Error submitting form:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const renderContent = () => {\n    if (isSubmitted) {\n      return (\n        <div className=\"text-center py-4\">\n          <div className=\"w-12 h-12 rounded-full bg-success-100 dark:bg-success-900 flex items-center justify-center mx-auto mb-4\">\n            <Check className=\"w-6 h-6 text-success-600 dark:text-success-400\" />\n          </div>\n          <h3 className=\"text-xl font-bold text-slate-900 dark:text-white mb-2\">\n            {currentLanguage === 'ar' ? 'تم الاشتراك بنجاح!' : 'Successfully Subscribed!'}\n          </h3>\n          <p className=\"text-slate-600 dark:text-slate-300 mb-4\">\n            {currentLanguage === 'ar' \n              ? 'شكراً لاشتراكك في نشرتنا الإخبارية.' \n              : 'Thank you for subscribing to our newsletter.'}\n          </p>\n          {variant === 'popup' && onClose && (\n            <Button \n              variant=\"outline\" \n              onClick={onClose}\n              size=\"sm\"\n            >\n              {currentLanguage === 'ar' ? 'إغلاق' : 'Close'}\n            </Button>\n          )}\n        </div>\n      );\n    }\n\n    return (\n      <>\n        {(variant === 'default' || variant === 'card' || variant === 'popup') && (\n          <div className={cn(\n            \"mb-4\",\n            variant === 'popup' ? \"text-center\" : \"\"\n          )}>\n            <h3 className=\"text-xl font-bold text-slate-900 dark:text-white mb-2\">\n              {title || defaultTitle}\n            </h3>\n            <p className=\"text-slate-600 dark:text-slate-300\">\n              {description || defaultDescription}\n            </p>\n          </div>\n        )}\n        \n        <Form\n          initialValues={{ email: '' }}\n          onSubmit={handleSubmit}\n          className=\"space-y-4\"\n        >\n          {variant === 'inline' ? (\n            <div className=\"flex flex-col sm:flex-row gap-3\">\n              <FormInput\n                name=\"email\"\n                placeholder={currentLanguage === 'ar' ? 'بريدك الإلكتروني' : 'Your email'}\n                type=\"email\"\n                required\n                leftIcon={<Mail />}\n                validators={[validators.email]}\n                className=\"flex-1\"\n              />\n              \n              <FormSubmit>\n                <Button\n                  type=\"submit\"\n                  className=\"whitespace-nowrap\"\n                  isLoading={isLoading}\n                >\n                  {currentLanguage === 'ar' ? 'اشترك الآن' : 'Subscribe Now'}\n                </Button>\n              </FormSubmit>\n            </div>\n          ) : (\n            <>\n              <FormInput\n                name=\"email\"\n                label={variant !== 'popup' ? (currentLanguage === 'ar' ? 'البريد الإلكتروني' : 'Email') : undefined}\n                placeholder={currentLanguage === 'ar' ? 'بريدك الإلكتروني' : 'Your email'}\n                type=\"email\"\n                required\n                leftIcon={<Mail />}\n                validators={[validators.email]}\n              />\n              \n              <FormSubmit>\n                <Button\n                  type=\"submit\"\n                  className={variant === 'popup' ? \"w-full\" : \"\"}\n                  isLoading={isLoading}\n                >\n                  <Send className={`h-4 w-4 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}`} />\n                  {currentLanguage === 'ar' ? 'اشترك الآن' : 'Subscribe Now'}\n                </Button>\n              </FormSubmit>\n            </>\n          )}\n        </Form>\n      </>\n    );\n  };\n\n  if (variant === 'popup') {\n    return (\n      <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4\">\n        <HoverAnimation animation=\"fade\">\n          <div className=\"relative max-w-md w-full bg-white dark:bg-slate-800 rounded-lg shadow-xl p-6\">\n            {onClose && (\n              <button\n                onClick={onClose}\n                className=\"absolute top-3 right-3 p-1 rounded-full hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors\"\n                aria-label={currentLanguage === 'ar' ? 'إغلاق' : 'Close'}\n              >\n                <X className=\"h-5 w-5 text-slate-500\" />\n              </button>\n            )}\n            \n            <div className=\"flex items-center justify-center w-12 h-12 rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 mx-auto mb-4\">\n              <Bell className=\"h-6 w-6\" />\n            </div>\n            \n            {renderContent()}\n          </div>\n        </HoverAnimation>\n      </div>\n    );\n  }\n\n  return (\n    <div className={cn(\n      variant === 'card' && \"bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6\",\n      className\n    )}>\n      {renderContent()}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAoBO,SAAS,eAAe,EAC7B,SAAS,EACT,UAAU,SAAS,EACnB,KAAK,EACL,WAAW,EACX,SAAS,EACT,OAAO,EACa;;IACpB,MAAM,EAAE,CAAC,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAC5C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe,oBAAoB,OACrC,8BACA;IAEJ,MAAM,qBAAqB,oBAAoB,OAC3C,uEACA;IAEJ,MAAM,eAAe,OAAO;QAC1B,aAAa;QAEb,IAAI;YACF,+BAA+B;YAC/B,QAAQ,GAAG,CAAC,4BAA4B;YACxC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,eAAe;YACf,IAAI,WAAW;gBACb;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,aAAa;YACf,qBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;kCAEnB,6LAAC;wBAAG,WAAU;kCACX,oBAAoB,OAAO,uBAAuB;;;;;;kCAErD,6LAAC;wBAAE,WAAU;kCACV,oBAAoB,OACjB,wCACA;;;;;;oBAEL,YAAY,WAAW,yBACtB,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS;wBACT,MAAK;kCAEJ,oBAAoB,OAAO,UAAU;;;;;;;;;;;;QAKhD;QAEA,qBACE;;gBACG,CAAC,YAAY,aAAa,YAAY,UAAU,YAAY,OAAO,mBAClE,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,QACA,YAAY,UAAU,gBAAgB;;sCAEtC,6LAAC;4BAAG,WAAU;sCACX,SAAS;;;;;;sCAEZ,6LAAC;4BAAE,WAAU;sCACV,eAAe;;;;;;;;;;;;8BAKtB,6LAAC,mIAAA,CAAA,OAAI;oBACH,eAAe;wBAAE,OAAO;oBAAG;oBAC3B,UAAU;oBACV,WAAU;8BAET,YAAY,yBACX,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,wIAAA,CAAA,YAAS;gCACR,MAAK;gCACL,aAAa,oBAAoB,OAAO,qBAAqB;gCAC7D,MAAK;gCACL,QAAQ;gCACR,wBAAU,6LAAC,qMAAA,CAAA,OAAI;;;;;gCACf,YAAY;oCAAC,wIAAA,CAAA,aAAU,CAAC,KAAK;iCAAC;gCAC9B,WAAU;;;;;;0CAGZ,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,WAAW;8CAEV,oBAAoB,OAAO,eAAe;;;;;;;;;;;;;;;;6CAKjD;;0CACE,6LAAC,wIAAA,CAAA,YAAS;gCACR,MAAK;gCACL,OAAO,YAAY,UAAW,oBAAoB,OAAO,sBAAsB,UAAW;gCAC1F,aAAa,oBAAoB,OAAO,qBAAqB;gCAC7D,MAAK;gCACL,QAAQ;gCACR,wBAAU,6LAAC,qMAAA,CAAA,OAAI;;;;;gCACf,YAAY;oCAAC,wIAAA,CAAA,aAAU,CAAC,KAAK;iCAAC;;;;;;0CAGhC,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAW,YAAY,UAAU,WAAW;oCAC5C,WAAW;;sDAEX,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAW,CAAC,QAAQ,EAAE,oBAAoB,OAAO,SAAS,QAAQ;;;;;;wCACvE,oBAAoB,OAAO,eAAe;;;;;;;;;;;;;;;;;;;;;IAQ3D;IAEA,IAAI,YAAY,SAAS;QACvB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,2JAAA,CAAA,iBAAc;gBAAC,WAAU;0BACxB,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,yBACC,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,cAAY,oBAAoB,OAAO,UAAU;sCAEjD,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;wBAGjB;;;;;;;;;;;;;;;;;IAKX;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,YAAY,UAAU,uDACtB;kBAEC;;;;;;AAGP;GA7KgB;;QAQiB,+HAAA,CAAA,iBAAc;;;KAR/B", "debugId": null}}, {"offset": {"line": 1882, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/ui/Badge.tsx"], "sourcesContent": ["import { cn } from '../../lib/utils';\n\ninterface BadgeProps {\n  children: React.ReactNode;\n  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'destructive' | 'outline';\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n  onClick?: () => void;\n}\n\nexport function Badge({\n  children,\n  variant = 'default',\n  size = 'md',\n  className,\n  onClick,\n}: BadgeProps) {\n  const baseStyles = \"inline-flex items-center justify-center font-medium transition-colors\";\n  \n  const variantStyles = {\n    default: \"bg-slate-100 text-slate-800 dark:bg-slate-700 dark:text-slate-100 hover:bg-slate-200 dark:hover:bg-slate-600\",\n    primary: \"bg-primary-100 text-primary-800 dark:bg-primary-900/30 dark:text-primary-300 hover:bg-primary-200 dark:hover:bg-primary-900/50\",\n    secondary: \"bg-secondary-100 text-secondary-800 dark:bg-secondary-900/30 dark:text-secondary-300 hover:bg-secondary-200 dark:hover:bg-secondary-900/50\",\n    success: \"bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-300 hover:bg-success-200 dark:hover:bg-success-900/50\",\n    warning: \"bg-warning-100 text-warning-800 dark:bg-warning-900/30 dark:text-warning-300 hover:bg-warning-200 dark:hover:bg-warning-900/50\",\n    destructive: \"bg-destructive-100 text-destructive-800 dark:bg-destructive-900/30 dark:text-destructive-300 hover:bg-destructive-200 dark:hover:bg-destructive-900/50\",\n    outline: \"border border-slate-200 dark:border-slate-700 text-slate-800 dark:text-slate-100 hover:bg-slate-100 dark:hover:bg-slate-800\",\n  };\n  \n  const sizeStyles = {\n    sm: \"text-xs px-2 py-0.5 rounded-full\",\n    md: \"text-sm px-2.5 py-0.5 rounded-full\",\n    lg: \"text-base px-3 py-1 rounded-full\",\n  };\n  \n  return (\n    <span\n      className={cn(\n        baseStyles,\n        variantStyles[variant],\n        sizeStyles[size],\n        onClick && \"cursor-pointer\",\n        className\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </span>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAUO,SAAS,MAAM,EACpB,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,SAAS,EACT,OAAO,EACI;IACX,MAAM,aAAa;IAEnB,MAAM,gBAAgB;QACpB,SAAS;QACT,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,aAAa;QACb,SAAS;IACX;IAEA,MAAM,aAAa;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,YACA,aAAa,CAAC,QAAQ,EACtB,UAAU,CAAC,KAAK,EAChB,WAAW,kBACX;QAEF,SAAS;kBAER;;;;;;AAGP;KAvCgB", "debugId": null}}, {"offset": {"line": 1927, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/ui/Dialog.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\nimport { X } from 'lucide-react';\nimport { cn } from '../../lib/utils';\n\nconst Dialog = DialogPrimitive.Root;\nconst DialogTrigger = DialogPrimitive.Trigger;\nconst DialogPortal = DialogPrimitive.Portal;\nconst DialogClose = DialogPrimitive.Close;\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/50 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n));\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-slate-200 bg-white p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] dark:border-slate-800 dark:bg-slate-900 rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </DialogPrimitive.Content>\n  </DialogPortal>\n));\nDialogContent.displayName = DialogPrimitive.Content.displayName;\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n);\nDialogHeader.displayName = \"DialogHeader\";\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n);\nDialogFooter.displayName = \"DialogFooter\";\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight text-slate-900 dark:text-white\",\n      className\n    )}\n    {...props}\n  />\n));\nDialogTitle.displayName = DialogPrimitive.Title.displayName;\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-slate-500 dark:text-slate-400\", className)}\n    {...props}\n  />\n));\nDialogDescription.displayName = DialogPrimitive.Description.displayName;\n\nexport {\n  Dialog,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n  DialogClose,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AACnC,MAAM,gBAAgB,qKAAA,CAAA,UAAuB;AAC7C,MAAM,eAAe,qKAAA,CAAA,SAAsB;AAC3C,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2KACA;QAED,GAAG,KAAK;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gjBACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,qKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2048, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/shop/QuickView.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { X, ShoppingCart, Heart, Star, ArrowRight, ChevronRight, ChevronLeft, Check, Info, Share2 } from 'lucide-react';\nimport Link from 'next/link';\nimport { Button } from '../ui/Button';\nimport { Card } from '../ui/Card';\nimport { formatCurrency, cn } from '../../lib/utils';\nimport { useCartStore } from '../../stores/cartStore';\nimport { useWishlistStore } from '../../stores/wishlistStore';\nimport { useAuthStore } from '../../stores/authStore';\nimport { AuthModal } from '../auth/AuthModal';\nimport { Product } from '../../types/index';\nimport { useTranslation } from '../../translations';\nimport { EnhancedImage } from '../ui/EnhancedImage';\nimport { Badge } from '../ui/Badge';\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose } from '../ui/Dialog';\n\ninterface QuickViewProps {\n  product: Product | null;\n  onClose: () => void;\n  onAddToCart?: (product: Product) => void;\n  onToggleWishlist?: (product: Product) => void;\n}\n\nexport function QuickView({ product, onClose, onAddToCart, onToggleWishlist: onToggleWishlistProp }: QuickViewProps) {\n  const isOpen = !!product; // إذا كان هناك منتج، فإن النافذة مفتوحة\n  const { t, currentLanguage } = useTranslation();\n\n  // Ensure we always have a valid language\n  const safeLanguage = currentLanguage || 'en';\n\n\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  const [showAuthModal, setShowAuthModal] = useState(false);\n  const [isAdding, setIsAdding] = useState(false);\n  const [quantity, setQuantity] = useState(1);\n  const [selectedColor, setSelectedColor] = useState<string | null>(null);\n  const [selectedSize, setSelectedSize] = useState<string | null>(null);\n  const [imageError, setImageError] = useState(false);\n\n  const cartStore = useCartStore();\n  const wishlistStore = useWishlistStore();\n  const { user } = useAuthStore();\n\n  // إعادة تعيين الحالة عند تغيير المنتج\n  useEffect(() => {\n    if (isOpen) {\n      setCurrentImageIndex(0);\n      setQuantity(1);\n      setSelectedColor(null);\n      setSelectedSize(null);\n      setImageError(false);\n      setIsAdding(false);\n    }\n  }, [isOpen, product?.id]);\n\n  // Handle ESC key press and click outside to close modal\n  useEffect(() => {\n    const handleEscKey = (event: KeyboardEvent) => {\n      if (event.key === 'Escape' && isOpen) {\n        console.log('ESC key pressed - closing modal'); // Debug log\n        event.preventDefault();\n        onClose();\n      }\n    };\n\n    const handleClickOutside = (event: MouseEvent) => {\n      const target = event.target as HTMLElement;\n      if (target.closest('[data-dialog-content]')) {\n        return; // Don't close if clicking inside dialog content\n      }\n      if (target.closest('[data-dialog-overlay]') && isOpen) {\n        console.log('Clicked outside - closing modal'); // Debug log\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      console.log('Modal opened - adding event listeners'); // Debug log\n      document.addEventListener('keydown', handleEscKey);\n      document.addEventListener('mousedown', handleClickOutside);\n      // Prevent body scroll when modal is open\n      document.body.style.overflow = 'hidden';\n    }\n\n    return () => {\n      console.log('Cleaning up event listeners'); // Debug log\n      document.removeEventListener('keydown', handleEscKey);\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n\n  // إذا لم يكن هناك منتج، لا تعرض شيئًا\n  if (!product) return null;\n\n  // التحقق من وجود صور للمنتج\n  const hasImages = product.images && product.images.length > 0;\n\n  // الصورة الحالية أو الصورة الاحتياطية\n  const currentImage = imageError || !hasImages\n    ? `/images/product-placeholder-light.svg`\n    : product.images[currentImageIndex];\n\n  // زيادة الكمية\n  const incrementQuantity = () => {\n    if (quantity < (product.stock || 10)) {\n      setQuantity(quantity + 1);\n    }\n  };\n\n  // إنقاص الكمية\n  const decrementQuantity = () => {\n    if (quantity > 1) {\n      setQuantity(quantity - 1);\n    }\n  };\n\n  // إضافة المنتج إلى سلة التسوق\n  const handleAddToCart = () => {\n    if (!product) return;\n\n    // التحقق من توفر المنتج في المخزون\n    if (!product.stock || product.stock <= 0) {\n      console.warn(`المنتج ${product.name} غير متوفر في المخزون`);\n      return;\n    }\n\n    // يمكن للمستخدم إضافة المنتج إلى السلة حتى لو لم يكن مسجل دخول\n    setIsAdding(true);\n\n    // استخدام الدالة المخصصة إذا تم تمريرها، وإلا استخدام السلوك الافتراضي\n    if (onAddToCart) {\n      onAddToCart(product);\n\n      // إظهار تأثير الإضافة وإغلاق النافذة\n      setTimeout(() => {\n        setIsAdding(false);\n        // إغلاق النافذة المنبثقة بعد الإضافة\n        setTimeout(() => {\n          onClose();\n          setQuantity(1); // إعادة تعيين الكمية\n        }, 500);\n      }, 1000);\n    } else {\n      // تجهيز بيانات المنتج للإضافة إلى السلة\n      const cartItem = {\n        ...product,\n        quantity: quantity,\n        color: selectedColor,\n        size: selectedSize,\n      };\n\n      // إضافة المنتج إلى السلة\n      cartStore.addItem(cartItem, quantity);\n\n      // إظهار تأثير الإضافة وإغلاق النافذة\n      setTimeout(() => {\n        setIsAdding(false);\n        // إغلاق النافذة المنبثقة بعد الإضافة\n        setTimeout(() => {\n          onClose();\n          setQuantity(1); // إعادة تعيين الكمية\n        }, 500);\n      }, 1000);\n    }\n\n    // يمكن إضافة تحليلات هنا لتتبع سلوك المستخدم\n    // trackEvent('add_to_cart_from_quickview', { product_id: product.id, product_name: product.name, quantity });\n  };\n\n  // إضافة المنتج إلى المفضلة أو إزالته منها\n  const toggleWishlist = () => {\n    if (!product) return;\n\n    // التحقق من تسجيل الدخول قبل إضافة المنتج إلى المفضلة\n    if (!user) {\n      setShowAuthModal(true);\n      return;\n    }\n\n    // استخدام الدالة المخصصة إذا تم تمريرها، وإلا استخدام السلوك الافتراضي\n    if (onToggleWishlistProp) {\n      onToggleWishlistProp(product);\n    } else {\n      const wishlist = wishlistStore;\n\n      // التحقق مما إذا كان المنتج موجوداً بالفعل في المفضلة\n      if (wishlist.isInWishlist(product.id)) {\n        // إزالة المنتج من المفضلة\n        wishlist.removeItem(product.id);\n        console.log(`تمت إزالة ${product.name} من المفضلة`);\n      } else {\n        // إضافة المنتج إلى المفضلة\n        wishlist.addItem(product);\n        console.log(`تمت إضافة ${product.name} إلى المفضلة`);\n      }\n    }\n\n    // يمكن إضافة تحليلات هنا لتتبع سلوك المستخدم\n    // trackEvent('toggle_wishlist_from_quickview', { product_id: product.id, product_name: product.name });\n  };\n\n  // التنقل بين صور المنتج\n  const navigateImages = (direction: 'next' | 'prev') => {\n    if (!hasImages) return;\n\n    if (direction === 'next') {\n      setCurrentImageIndex((prev) => (prev + 1) % product.images.length);\n    } else {\n      setCurrentImageIndex((prev) => (prev - 1 + product.images.length) % product.images.length);\n    }\n  };\n\n  // تحديد ما إذا كان المنتج في المخزون\n  const isInStock = product.stock && product.stock > 0;\n\n  // الألوان المتاحة (يمكن تحديثها لاحقًا لتكون ديناميكية)\n  const availableColors = product.colors || ['#000000', '#FFFFFF', '#0066CC', '#FF4500'];\n\n  // الأحجام المتاحة (يمكن تحديثها لاحقًا لتكون ديناميكية)\n  const availableSizes = product.sizes || ['S', 'M', 'L', 'XL'];\n\n  return (\n    <>\n      <Dialog\n        open={isOpen}\n        onOpenChange={(open) => {\n          console.log('Dialog onOpenChange:', open); // Debug log\n          if (!open) {\n            onClose();\n          }\n        }}\n      >\n        <DialogContent\n          className=\"sm:max-w-3xl max-h-[90vh] overflow-y-auto\"\n          data-dialog-content\n        >\n          {/* Close Button - Multiple approaches for maximum compatibility */}\n          <div className=\"absolute right-4 top-4 z-[60]\">\n            {/* Primary close button using DialogClose */}\n            <DialogClose asChild>\n              <button\n                className=\"w-8 h-8 rounded-full bg-white/90 dark:bg-slate-800/90 shadow-lg border border-slate-200 dark:border-slate-700 flex items-center justify-center hover:bg-white dark:hover:bg-slate-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\"\n                aria-label=\"Close modal\"\n                type=\"button\"\n                onClick={(e) => {\n                  console.log('DialogClose button clicked'); // Debug log\n                  onClose();\n                }}\n              >\n                <X className=\"h-4 w-4 text-slate-600 dark:text-slate-300\" />\n              </button>\n            </DialogClose>\n\n            {/* Fallback close button */}\n            <button\n              className=\"absolute inset-0 w-8 h-8 rounded-full bg-transparent flex items-center justify-center z-10\"\n              aria-label=\"Close modal (fallback)\"\n              type=\"button\"\n              onClick={(e) => {\n                e.preventDefault();\n                e.stopPropagation();\n                console.log('Fallback close button clicked'); // Debug log\n                onClose();\n              }}\n              onMouseDown={(e) => {\n                e.preventDefault();\n                e.stopPropagation();\n              }}\n            >\n              <span className=\"sr-only\">Close</span>\n            </button>\n          </div>\n\n          <DialogHeader className=\"pr-12\">\n            <DialogTitle className=\"text-xl font-bold text-slate-900 dark:text-white\">\n              {safeLanguage === 'ar' ? (product.name_ar || product.name) : product.name}\n            </DialogTitle>\n          </DialogHeader>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mt-4\">\n            {/* Product Images */}\n            <div className=\"relative\">\n              <div className=\"relative aspect-square rounded-lg overflow-hidden bg-slate-100 dark:bg-slate-800\">\n                <EnhancedImage\n                  src={currentImage}\n                  alt={product.name}\n                  fill={true}\n                  objectFit=\"contain\"\n                  progressive={true}\n                  placeholder=\"shimmer\"\n                  className=\"object-center\"\n                  onError={() => setImageError(true)}\n                />\n\n                {/* Image navigation buttons */}\n                {hasImages && product.images.length > 1 && (\n                  <>\n                    <button\n                      onClick={() => navigateImages('prev')}\n                      className=\"absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 dark:bg-slate-800/80 rounded-full p-1.5 shadow-md hover:bg-white dark:hover:bg-slate-700 transition-colors\"\n                      aria-label=\"Previous image\"\n                    >\n                      <ChevronLeft className=\"h-5 w-5 text-slate-700 dark:text-slate-200\" />\n                    </button>\n                    <button\n                      onClick={() => navigateImages('next')}\n                      className=\"absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 dark:bg-slate-800/80 rounded-full p-1.5 shadow-md hover:bg-white dark:hover:bg-slate-700 transition-colors\"\n                      aria-label=\"Next image\"\n                    >\n                      <ChevronRight className=\"h-5 w-5 text-slate-700 dark:text-slate-200\" />\n                    </button>\n                  </>\n                )}\n\n                {/* Product badges */}\n                <div className=\"absolute top-2 left-2 flex flex-col gap-1\">\n                  {product.isNew && (\n                    <Badge variant=\"primary\" className=\"text-xs\">\n                      {safeLanguage === 'ar' ? 'جديد' : 'New'}\n                    </Badge>\n                  )}\n                  {product.discount > 0 && (\n                    <Badge variant=\"destructive\" className=\"text-xs\">\n                      {safeLanguage === 'ar' ? `${product.discount}% خصم` : `${product.discount}% OFF`}\n                    </Badge>\n                  )}\n                </div>\n              </div>\n\n              {/* Thumbnail images */}\n              {hasImages && product.images.length > 1 && (\n                <div className=\"flex mt-4 gap-2 overflow-x-auto pb-2\">\n                  {product.images.map((image, index) => (\n                    <button\n                      key={index}\n                      onClick={() => setCurrentImageIndex(index)}\n                      className={cn(\n                        \"relative w-16 h-16 rounded-md overflow-hidden border-2 transition-all\",\n                        index === currentImageIndex\n                          ? \"border-primary-500 shadow-md\"\n                          : \"border-transparent hover:border-slate-300 dark:hover:border-slate-600\"\n                      )}\n                    >\n                      <EnhancedImage\n                        src={image}\n                        alt={`${product.name} - Image ${index + 1}`}\n                        fill={true}\n                        objectFit=\"cover\"\n                      />\n                    </button>\n                  ))}\n                </div>\n              )}\n            </div>\n\n            {/* Product Details */}\n            <div className=\"flex flex-col\">\n              {/* Category & Rating */}\n              <div className=\"flex items-center justify-between mb-4\">\n                {product.category && (\n                  <span className=\"text-xs px-2 py-0.5 bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300 rounded-full\">\n                    {product.category}\n                  </span>\n                )}\n\n                <div className=\"flex items-center\">\n                  <div className=\"flex\">\n                    {[1, 2, 3, 4, 5].map((star) => (\n                      <Star\n                        key={star}\n                        className={cn(\n                          \"h-4 w-4\",\n                          star <= (product.rating || 0)\n                            ? \"text-yellow-400 fill-yellow-400\"\n                            : \"text-slate-300 dark:text-slate-600\"\n                        )}\n                      />\n                    ))}\n                  </div>\n                  <span className=\"ml-2 text-sm text-slate-600 dark:text-slate-400\">\n                    {product.rating?.toFixed(1) || '0.0'} ({product.reviewCount || 0} {safeLanguage === 'ar' ? 'تقييم' : 'reviews'})\n                  </span>\n                </div>\n              </div>\n\n              {/* Price */}\n              <div className=\"flex items-baseline mb-4\">\n                <span className=\"text-2xl font-bold text-slate-900 dark:text-white\">\n                  {formatCurrency(product.price)}\n                </span>\n                {product.compareAtPrice && product.compareAtPrice > product.price && (\n                  <span className=\"ml-2 text-sm text-slate-500 line-through\">\n                    {formatCurrency(product.compareAtPrice)}\n                  </span>\n                )}\n              </div>\n\n              {/* Description */}\n              <p className=\"text-slate-600 dark:text-slate-300 mb-6 text-sm\">\n                {safeLanguage === 'ar'\n                  ? (product.description_ar || product.description)\n                  : product.description}\n              </p>\n\n              {/* Color selection */}\n              {availableColors.length > 0 && (\n                <div className=\"mb-4\">\n                  <h3 className=\"text-sm font-medium text-slate-900 dark:text-white mb-2\">\n                    {safeLanguage === 'ar' ? 'اللون' : 'Color'}\n                  </h3>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {availableColors.map((color) => (\n                      <button\n                        key={color}\n                        onClick={() => setSelectedColor(color)}\n                        className={cn(\n                          \"w-8 h-8 rounded-full border-2 transition-all flex items-center justify-center\",\n                          selectedColor === color\n                            ? \"border-primary-500 shadow-sm\"\n                            : \"border-slate-200 dark:border-slate-700\"\n                        )}\n                        style={{ backgroundColor: color }}\n                        aria-label={`Select color: ${color}`}\n                      >\n                        {selectedColor === color && (\n                          <Check className=\"h-4 w-4 text-white drop-shadow-md\" />\n                        )}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Size selection */}\n              {availableSizes.length > 0 && (\n                <div className=\"mb-6\">\n                  <h3 className=\"text-sm font-medium text-slate-900 dark:text-white mb-2\">\n                    {safeLanguage === 'ar' ? 'الحجم' : 'Size'}\n                  </h3>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {availableSizes.map((size) => (\n                      <button\n                        key={size}\n                        onClick={() => setSelectedSize(size)}\n                        className={cn(\n                          \"px-3 py-1 rounded-md text-sm font-medium transition-all\",\n                          selectedSize === size\n                            ? \"bg-primary-500 text-white shadow-sm\"\n                            : \"bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 hover:bg-slate-200 dark:hover:bg-slate-700\"\n                        )}\n                      >\n                        {size}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Quantity and Add to Cart */}\n              <div className=\"flex items-center gap-4 mb-6\">\n                <div className=\"flex items-center border border-slate-300 dark:border-slate-600 rounded-md\">\n                  <button\n                    onClick={decrementQuantity}\n                    disabled={quantity <= 1}\n                    className=\"px-3 py-2 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 disabled:opacity-50\"\n                  >\n                    -\n                  </button>\n                  <span className=\"px-3 py-2 text-slate-900 dark:text-white min-w-[40px] text-center\">\n                    {quantity}\n                  </span>\n                  <button\n                    onClick={incrementQuantity}\n                    disabled={quantity >= (product.stock || 10)}\n                    className=\"px-3 py-2 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 disabled:opacity-50\"\n                  >\n                    +\n                  </button>\n                </div>\n\n                <Button\n                  variant=\"primary\"\n                  className=\"flex-1 flex items-center justify-center gap-2\"\n                  onClick={handleAddToCart}\n                  disabled={!isInStock || isAdding}\n                >\n                  {isAdding ? (\n                    <span>{safeLanguage === 'ar' ? 'تمت الإضافة!' : 'Added to Cart!'}</span>\n                  ) : (\n                    <>\n                      <ShoppingCart className=\"h-4 w-4\" />\n                      <span>{isInStock ? t('products.addToCart') : t('products.outOfStock')}</span>\n                    </>\n                  )}\n                </Button>\n              </div>\n\n              {/* Stock status */}\n              <div className=\"flex items-center gap-2 mb-4\">\n                <div className={cn(\n                  \"w-3 h-3 rounded-full\",\n                  isInStock ? \"bg-green-500\" : \"bg-red-500\"\n                )} />\n                <span className=\"text-sm text-slate-600 dark:text-slate-400\">\n                  {isInStock\n                    ? `${safeLanguage === 'ar' ? 'متوفر في المخزون' : 'In Stock'} (${product.stock})`\n                    : safeLanguage === 'ar' ? 'غير متوفر' : 'Out of Stock'\n                  }\n                </span>\n              </div>\n\n              {/* Action buttons */}\n              <div className=\"flex gap-2 mt-auto\">\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  className={cn(\n                    \"flex items-center gap-1\",\n                    user && useWishlistStore.getState().isInWishlist(product.id) && \"text-red-500\"\n                  )}\n                  onClick={toggleWishlist}\n                >\n                  <Heart className={cn(\n                    \"h-4 w-4\",\n                    user && useWishlistStore.getState().isInWishlist(product.id) && \"fill-current\"\n                  )} />\n                  <span>\n                    {user && useWishlistStore.getState().isInWishlist(product.id)\n                      ? safeLanguage === 'ar' ? 'تمت الإضافة للمفضلة' : 'Added to Wishlist'\n                      : safeLanguage === 'ar' ? 'أضف للمفضلة' : 'Add to Wishlist'\n                    }\n                  </span>\n                </Button>\n\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  className=\"flex items-center gap-1\"\n                >\n                  <Share2 className=\"h-4 w-4\" />\n                  <span>{safeLanguage === 'ar' ? 'مشاركة' : 'Share'}</span>\n                </Button>\n              </div>\n\n              {/* View full details link */}\n              <Link\n                href={`/${safeLanguage}/shop/product/${product.slug}`}\n                className=\"mt-4 text-primary-600 dark:text-primary-400 text-sm font-medium hover:underline flex items-center\"\n                onClick={onClose}\n              >\n                <Info className=\"h-4 w-4 mr-1\" />\n                {safeLanguage === 'ar' ? 'عرض التفاصيل الكاملة' : 'View Full Details'}\n              </Link>\n            </div>\n          </div>\n        </DialogContent>\n      </Dialog>\n\n      {showAuthModal && (\n        <AuthModal onClose={() => setShowAuthModal(false)} />\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;AAhBA;;;;;;;;;;;;;;AAyBO,SAAS,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,kBAAkB,oBAAoB,EAAkB;;IACjH,MAAM,SAAS,CAAC,CAAC,SAAS,wCAAwC;IAClE,MAAM,EAAE,CAAC,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAE5C,yCAAyC;IACzC,MAAM,eAAe,mBAAmB;IAGxC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,YAAY,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAC7B,MAAM,gBAAgB,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD;IACrC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAE5B,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,QAAQ;gBACV,qBAAqB;gBACrB,YAAY;gBACZ,iBAAiB;gBACjB,gBAAgB;gBAChB,cAAc;gBACd,YAAY;YACd;QACF;8BAAG;QAAC;QAAQ,SAAS;KAAG;IAExB,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;oDAAe,CAAC;oBACpB,IAAI,MAAM,GAAG,KAAK,YAAY,QAAQ;wBACpC,QAAQ,GAAG,CAAC,oCAAoC,YAAY;wBAC5D,MAAM,cAAc;wBACpB;oBACF;gBACF;;YAEA,MAAM;0DAAqB,CAAC;oBAC1B,MAAM,SAAS,MAAM,MAAM;oBAC3B,IAAI,OAAO,OAAO,CAAC,0BAA0B;wBAC3C,QAAQ,gDAAgD;oBAC1D;oBACA,IAAI,OAAO,OAAO,CAAC,4BAA4B,QAAQ;wBACrD,QAAQ,GAAG,CAAC,oCAAoC,YAAY;wBAC5D;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,QAAQ,GAAG,CAAC,0CAA0C,YAAY;gBAClE,SAAS,gBAAgB,CAAC,WAAW;gBACrC,SAAS,gBAAgB,CAAC,aAAa;gBACvC,yCAAyC;gBACzC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;uCAAO;oBACL,QAAQ,GAAG,CAAC,gCAAgC,YAAY;oBACxD,SAAS,mBAAmB,CAAC,WAAW;oBACxC,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;8BAAG;QAAC;QAAQ;KAAQ;IAEpB,sCAAsC;IACtC,IAAI,CAAC,SAAS,OAAO;IAErB,4BAA4B;IAC5B,MAAM,YAAY,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG;IAE5D,sCAAsC;IACtC,MAAM,eAAe,cAAc,CAAC,YAChC,CAAC,qCAAqC,CAAC,GACvC,QAAQ,MAAM,CAAC,kBAAkB;IAErC,eAAe;IACf,MAAM,oBAAoB;QACxB,IAAI,WAAW,CAAC,QAAQ,KAAK,IAAI,EAAE,GAAG;YACpC,YAAY,WAAW;QACzB;IACF;IAEA,eAAe;IACf,MAAM,oBAAoB;QACxB,IAAI,WAAW,GAAG;YAChB,YAAY,WAAW;QACzB;IACF;IAEA,8BAA8B;IAC9B,MAAM,kBAAkB;QACtB,IAAI,CAAC,SAAS;QAEd,mCAAmC;QACnC,IAAI,CAAC,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,GAAG;YACxC,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,IAAI,CAAC,qBAAqB,CAAC;YAC1D;QACF;QAEA,+DAA+D;QAC/D,YAAY;QAEZ,uEAAuE;QACvE,IAAI,aAAa;YACf,YAAY;YAEZ,qCAAqC;YACrC,WAAW;gBACT,YAAY;gBACZ,qCAAqC;gBACrC,WAAW;oBACT;oBACA,YAAY,IAAI,qBAAqB;gBACvC,GAAG;YACL,GAAG;QACL,OAAO;YACL,wCAAwC;YACxC,MAAM,WAAW;gBACf,GAAG,OAAO;gBACV,UAAU;gBACV,OAAO;gBACP,MAAM;YACR;YAEA,yBAAyB;YACzB,UAAU,OAAO,CAAC,UAAU;YAE5B,qCAAqC;YACrC,WAAW;gBACT,YAAY;gBACZ,qCAAqC;gBACrC,WAAW;oBACT;oBACA,YAAY,IAAI,qBAAqB;gBACvC,GAAG;YACL,GAAG;QACL;IAEA,6CAA6C;IAC7C,8GAA8G;IAChH;IAEA,0CAA0C;IAC1C,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS;QAEd,sDAAsD;QACtD,IAAI,CAAC,MAAM;YACT,iBAAiB;YACjB;QACF;QAEA,uEAAuE;QACvE,IAAI,sBAAsB;YACxB,qBAAqB;QACvB,OAAO;YACL,MAAM,WAAW;YAEjB,sDAAsD;YACtD,IAAI,SAAS,YAAY,CAAC,QAAQ,EAAE,GAAG;gBACrC,0BAA0B;gBAC1B,SAAS,UAAU,CAAC,QAAQ,EAAE;gBAC9B,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,IAAI,CAAC,WAAW,CAAC;YACpD,OAAO;gBACL,2BAA2B;gBAC3B,SAAS,OAAO,CAAC;gBACjB,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,IAAI,CAAC,YAAY,CAAC;YACrD;QACF;IAEA,6CAA6C;IAC7C,wGAAwG;IAC1G;IAEA,wBAAwB;IACxB,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,WAAW;QAEhB,IAAI,cAAc,QAAQ;YACxB,qBAAqB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,QAAQ,MAAM,CAAC,MAAM;QACnE,OAAO;YACL,qBAAqB,CAAC,OAAS,CAAC,OAAO,IAAI,QAAQ,MAAM,CAAC,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM;QAC3F;IACF;IAEA,qCAAqC;IACrC,MAAM,YAAY,QAAQ,KAAK,IAAI,QAAQ,KAAK,GAAG;IAEnD,wDAAwD;IACxD,MAAM,kBAAkB,QAAQ,MAAM,IAAI;QAAC;QAAW;QAAW;QAAW;KAAU;IAEtF,wDAAwD;IACxD,MAAM,iBAAiB,QAAQ,KAAK,IAAI;QAAC;QAAK;QAAK;QAAK;KAAK;IAE7D,qBACE;;0BACE,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc,CAAC;oBACb,QAAQ,GAAG,CAAC,wBAAwB,OAAO,YAAY;oBACvD,IAAI,CAAC,MAAM;wBACT;oBACF;gBACF;0BAEA,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBACZ,WAAU;oBACV,qBAAmB;;sCAGnB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,qIAAA,CAAA,cAAW;oCAAC,OAAO;8CAClB,cAAA,6LAAC;wCACC,WAAU;wCACV,cAAW;wCACX,MAAK;wCACL,SAAS,CAAC;4CACR,QAAQ,GAAG,CAAC,+BAA+B,YAAY;4CACvD;wCACF;kDAEA,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAKjB,6LAAC;oCACC,WAAU;oCACV,cAAW;oCACX,MAAK;oCACL,SAAS,CAAC;wCACR,EAAE,cAAc;wCAChB,EAAE,eAAe;wCACjB,QAAQ,GAAG,CAAC,kCAAkC,YAAY;wCAC1D;oCACF;oCACA,aAAa,CAAC;wCACZ,EAAE,cAAc;wCAChB,EAAE,eAAe;oCACnB;8CAEA,cAAA,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;sCAI9B,6LAAC,qIAAA,CAAA,eAAY;4BAAC,WAAU;sCACtB,cAAA,6LAAC,qIAAA,CAAA,cAAW;gCAAC,WAAU;0CACpB,iBAAiB,OAAQ,QAAQ,OAAO,IAAI,QAAQ,IAAI,GAAI,QAAQ,IAAI;;;;;;;;;;;sCAI7E,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,4IAAA,CAAA,gBAAa;oDACZ,KAAK;oDACL,KAAK,QAAQ,IAAI;oDACjB,MAAM;oDACN,WAAU;oDACV,aAAa;oDACb,aAAY;oDACZ,WAAU;oDACV,SAAS,IAAM,cAAc;;;;;;gDAI9B,aAAa,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACpC;;sEACE,6LAAC;4DACC,SAAS,IAAM,eAAe;4DAC9B,WAAU;4DACV,cAAW;sEAEX,cAAA,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;sEAEzB,6LAAC;4DACC,SAAS,IAAM,eAAe;4DAC9B,WAAU;4DACV,cAAW;sEAEX,cAAA,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;;8DAM9B,6LAAC;oDAAI,WAAU;;wDACZ,QAAQ,KAAK,kBACZ,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAChC,iBAAiB,OAAO,SAAS;;;;;;wDAGrC,QAAQ,QAAQ,GAAG,mBAClB,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAc,WAAU;sEACpC,iBAAiB,OAAO,GAAG,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,QAAQ,QAAQ,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;wCAOvF,aAAa,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACpC,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC;oDAEC,SAAS,IAAM,qBAAqB;oDACpC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yEACA,UAAU,oBACN,iCACA;8DAGN,cAAA,6LAAC,4IAAA,CAAA,gBAAa;wDACZ,KAAK;wDACL,KAAK,GAAG,QAAQ,IAAI,CAAC,SAAS,EAAE,QAAQ,GAAG;wDAC3C,MAAM;wDACN,WAAU;;;;;;mDAbP;;;;;;;;;;;;;;;;8CAsBf,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;gDACZ,QAAQ,QAAQ,kBACf,6LAAC;oDAAK,WAAU;8DACb,QAAQ,QAAQ;;;;;;8DAIrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ;gEAAC;gEAAG;gEAAG;gEAAG;gEAAG;6DAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,6LAAC,qMAAA,CAAA,OAAI;oEAEH,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,WACA,QAAQ,CAAC,QAAQ,MAAM,IAAI,CAAC,IACxB,oCACA;mEALD;;;;;;;;;;sEAUX,6LAAC;4DAAK,WAAU;;gEACb,QAAQ,MAAM,EAAE,QAAQ,MAAM;gEAAM;gEAAG,QAAQ,WAAW,IAAI;gEAAE;gEAAE,iBAAiB,OAAO,UAAU;gEAAU;;;;;;;;;;;;;;;;;;;sDAMrH,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,KAAK;;;;;;gDAE9B,QAAQ,cAAc,IAAI,QAAQ,cAAc,GAAG,QAAQ,KAAK,kBAC/D,6LAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,cAAc;;;;;;;;;;;;sDAM5C,6LAAC;4CAAE,WAAU;sDACV,iBAAiB,OACb,QAAQ,cAAc,IAAI,QAAQ,WAAW,GAC9C,QAAQ,WAAW;;;;;;wCAIxB,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,iBAAiB,OAAO,UAAU;;;;;;8DAErC,6LAAC;oDAAI,WAAU;8DACZ,gBAAgB,GAAG,CAAC,CAAC,sBACpB,6LAAC;4DAEC,SAAS,IAAM,iBAAiB;4DAChC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iFACA,kBAAkB,QACd,iCACA;4DAEN,OAAO;gEAAE,iBAAiB;4DAAM;4DAChC,cAAY,CAAC,cAAc,EAAE,OAAO;sEAEnC,kBAAkB,uBACjB,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;2DAZd;;;;;;;;;;;;;;;;wCAqBd,eAAe,MAAM,GAAG,mBACvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,iBAAiB,OAAO,UAAU;;;;;;8DAErC,6LAAC;oDAAI,WAAU;8DACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC;4DAEC,SAAS,IAAM,gBAAgB;4DAC/B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA,iBAAiB,OACb,wCACA;sEAGL;2DATI;;;;;;;;;;;;;;;;sDAiBf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,SAAS;4DACT,UAAU,YAAY;4DACtB,WAAU;sEACX;;;;;;sEAGD,6LAAC;4DAAK,WAAU;sEACb;;;;;;sEAEH,6LAAC;4DACC,SAAS;4DACT,UAAU,YAAY,CAAC,QAAQ,KAAK,IAAI,EAAE;4DAC1C,WAAU;sEACX;;;;;;;;;;;;8DAKH,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS;oDACT,UAAU,CAAC,aAAa;8DAEvB,yBACC,6LAAC;kEAAM,iBAAiB,OAAO,iBAAiB;;;;;6EAEhD;;0EACE,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;0EACxB,6LAAC;0EAAM,YAAY,EAAE,wBAAwB,EAAE;;;;;;;;;;;;;;;;;;;sDAOvD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,wBACA,YAAY,iBAAiB;;;;;;8DAE/B,6LAAC;oDAAK,WAAU;8DACb,YACG,GAAG,iBAAiB,OAAO,qBAAqB,WAAW,EAAE,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAC,GAC/E,iBAAiB,OAAO,cAAc;;;;;;;;;;;;sDAM9C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2BACA,QAAQ,iIAAA,CAAA,mBAAgB,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,EAAE,KAAK;oDAElE,SAAS;;sEAET,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACjB,WACA,QAAQ,iIAAA,CAAA,mBAAgB,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,EAAE,KAAK;;;;;;sEAElE,6LAAC;sEACE,QAAQ,iIAAA,CAAA,mBAAgB,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,EAAE,IACxD,iBAAiB,OAAO,wBAAwB,sBAChD,iBAAiB,OAAO,gBAAgB;;;;;;;;;;;;8DAKhD,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;;sEAEV,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;sEAAM,iBAAiB,OAAO,WAAW;;;;;;;;;;;;;;;;;;sDAK9C,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,CAAC,EAAE,aAAa,cAAc,EAAE,QAAQ,IAAI,EAAE;4CACrD,WAAU;4CACV,SAAS;;8DAET,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,iBAAiB,OAAO,yBAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO3D,+BACC,6LAAC,0IAAA,CAAA,YAAS;gBAAC,SAAS,IAAM,iBAAiB;;;;;;;;AAInD;GA7hBgB;;QAEiB,+HAAA,CAAA,iBAAc;QAc3B,6HAAA,CAAA,eAAY;QACR,iIAAA,CAAA,mBAAgB;QACrB,6HAAA,CAAA,eAAY;;;KAlBf", "debugId": null}}, {"offset": {"line": 2904, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/marketing/ExitIntentPopup.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { X, Mail, Gift, ShoppingCart, Tag, ArrowRight, Check } from 'lucide-react';\nimport { Button } from '../ui/Button';\nimport { Input } from '../ui/Input';\nimport { EnhancedImage } from '../ui/EnhancedImage';\nimport { useLanguageStore } from '../../stores/languageStore';\nimport { useTranslation } from '../../translations';\nimport { useTheme } from 'next-themes';\nimport { cn, formatCurrency } from '../../lib/utils';\nimport { Product } from '../../types/index';\nimport { useCartStore } from '../../stores/cartStore';\nimport { Badge } from '../ui/Badge';\nimport { HoverAnimation } from '../ui/animations/HoverAnimation';\n\ninterface ExitIntentPopupProps {\n  delay?: number;\n  sessionKey?: string;\n  cookieDays?: number;\n  onClose: () => void;\n  product: Product;\n}\n\nexport function ExitIntentPopup({\n  delay = 5000,\n  sessionKey = 'exit_popup_shown',\n  cookieDays = 7,\n  onClose,\n  product\n}: ExitIntentPopupProps) {\n  const [isVisible, setIsVisible] = useState(false);\n  const [email, setEmail] = useState('');\n  const [isSubmitted, setIsSubmitted] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const popupRef = useRef<HTMLDivElement>(null);\n\n  const { language } = useLanguageStore();\n  const { t, currentLanguage } = useTranslation();\n  const { resolvedTheme } = useTheme();\n  const cartStore = useCartStore();\n\n  useEffect(() => {\n    const hasSeenPopup =\n      sessionStorage.getItem(sessionKey) === 'true' ||\n      document.cookie.includes(`${sessionKey}=true`);\n\n    if (hasSeenPopup) {\n      onClose();\n      return;\n    }\n\n    const timer = setTimeout(() => {\n      document.addEventListener('mouseout', handleMouseLeave);\n    }, delay);\n\n    return () => {\n      clearTimeout(timer);\n      document.removeEventListener('mouseout', handleMouseLeave);\n    };\n  }, [delay, sessionKey, onClose]);\n\n  const handleMouseLeave = (e: MouseEvent) => {\n    if (e.clientY <= 0 && !isVisible) {\n      setIsVisible(true);\n      document.removeEventListener('mouseout', handleMouseLeave);\n      sessionStorage.setItem(sessionKey, 'true');\n      if (cookieDays > 0) {\n        const expiryDate = new Date();\n        expiryDate.setDate(expiryDate.getDate() + cookieDays);\n        document.cookie = `${sessionKey}=true; expires=${expiryDate.toUTCString()}; path=/`;\n      }\n    }\n  };\n\n  const internalHandleClose = () => {\n    setIsVisible(false);\n    onClose();\n  };\n\n  useEffect(() => {\n    const handleClickOutside = (e: MouseEvent) => {\n      if (popupRef.current && !popupRef.current.contains(e.target as Node)) {\n        internalHandleClose();\n      }\n    };\n\n    if (isVisible) {\n      document.addEventListener('mousedown', handleClickOutside);\n    }\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [isVisible, internalHandleClose]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!email) {\n      setError(language === 'ar' ? 'يرجى إدخال بريدك الإلكتروني' : 'Please enter your email');\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      // هنا يمكن إضافة رمز لإرسال البريد الإلكتروني إلى API\n      // await api.subscribeToNewsletter(email);\n\n      // محاكاة تأخير الشبكة\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      setIsSubmitted(true);\n    } catch (err) {\n      setError(language === 'ar' ? 'حدث خطأ. يرجى المحاولة مرة أخرى.' : 'An error occurred. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (!isVisible) {\n    return null;\n  }\n\n  // إضافة المنتج إلى سلة التسوق\n  const handleAddToCart = () => {\n    cartStore.addItem({\n      id: product.id,\n      name: product.name,\n      name_ar: product.name_ar,\n      price: product.price,\n      image: product.images && product.images.length > 0 ? product.images[0] : '/images/product-placeholder-light.svg',\n      quantity: 1,\n    });\n\n    // إغلاق النافذة المنبثقة بعد الإضافة\n    setTimeout(() => {\n      internalHandleClose();\n    }, 500);\n  };\n\n  // حساب نسبة الخصم إذا كان هناك سعر مقارنة\n  const discountPercentage = product.compareAtPrice && product.compareAtPrice > product.price\n    ? Math.round((1 - product.price / product.compareAtPrice) * 100)\n    : product.discount || 0;\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm p-4\">\n      <HoverAnimation animation=\"fade\">\n        <div\n          ref={popupRef}\n          className={cn(\n            \"relative max-w-lg w-full rounded-xl shadow-2xl overflow-hidden\",\n            resolvedTheme === 'dark' ? \"bg-slate-900\" : \"bg-white\",\n            \"border border-slate-200 dark:border-slate-700\"\n          )}\n        >\n          {/* زر الإغلاق */}\n          <button\n            onClick={internalHandleClose}\n            className=\"absolute top-4 right-4 z-10 p-1.5 rounded-full bg-white/80 dark:bg-slate-800/80 hover:bg-white dark:hover:bg-slate-700 transition-colors shadow-sm\"\n            aria-label={currentLanguage === 'ar' ? 'إغلاق' : 'Close'}\n          >\n            <X size={18} className=\"text-slate-700 dark:text-slate-200\" />\n          </button>\n\n          {!isSubmitted ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2\">\n              {/* صورة المنتج */}\n              <div className=\"relative aspect-square bg-slate-100 dark:bg-slate-800\">\n                {product.images && product.images[0] && (\n                  <EnhancedImage\n                    src={product.images[0]}\n                    alt={product.name}\n                    fill\n                    objectFit=\"contain\"\n                    progressive={true}\n                    placeholder=\"shimmer\"\n                    containerClassName=\"w-full h-full\"\n                  />\n                )}\n\n                {/* شارات المنتج */}\n                <div className=\"absolute top-4 left-4 flex flex-col gap-1.5\">\n                  {discountPercentage > 0 && (\n                    <Badge variant=\"destructive\" size=\"md\">\n                      {currentLanguage === 'ar' ? `${discountPercentage}% خصم` : `${discountPercentage}% OFF`}\n                    </Badge>\n                  )}\n                  {product.isNew && (\n                    <Badge variant=\"primary\" size=\"md\">\n                      {currentLanguage === 'ar' ? 'جديد' : 'New'}\n                    </Badge>\n                  )}\n                </div>\n              </div>\n\n              {/* تفاصيل المنتج ونموذج الاشتراك */}\n              <div className=\"p-6 flex flex-col\">\n                <div className=\"mb-4\">\n                  <h2 className=\"text-xl font-bold mb-1 text-slate-900 dark:text-white\">\n                    {currentLanguage === 'ar' ? 'عرض خاص لك!' : 'Special Offer For You!'}\n                  </h2>\n\n                  <div className=\"mb-3\">\n                    <h3 className=\"text-lg font-medium text-slate-800 dark:text-slate-200\">\n                      {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}\n                    </h3>\n\n                    <div className=\"flex items-baseline mt-2\">\n                      <span className=\"text-xl font-bold text-primary-600 dark:text-primary-400\">\n                        {formatCurrency(product.price)}\n                      </span>\n                      {product.compareAtPrice && product.compareAtPrice > product.price && (\n                        <span className=\"ml-2 text-sm text-slate-500 line-through\">\n                          {formatCurrency(product.compareAtPrice)}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n\n                  <p className=\"text-slate-600 dark:text-slate-300 text-sm mb-4\">\n                    {currentLanguage === 'ar'\n                      ? 'لا تفوت هذا العرض الرائع! اشترك في نشرتنا الإخبارية للحصول على خصم إضافي.'\n                      : 'Don\\'t miss this amazing offer! Subscribe to our newsletter for an additional discount.'}\n                  </p>\n\n                  {/* أزرار الإجراءات */}\n                  <div className=\"flex gap-2 mb-6\">\n                    <Button\n                      variant=\"primary\"\n                      className=\"flex-1 flex items-center justify-center gap-1\"\n                      onClick={handleAddToCart}\n                    >\n                      <ShoppingCart className=\"h-4 w-4\" />\n                      <span>{currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}</span>\n                    </Button>\n\n                    <Button\n                      variant=\"outline\"\n                      className=\"flex items-center justify-center\"\n                      onClick={internalHandleClose}\n                    >\n                      <ArrowRight className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                </div>\n\n                {/* نموذج الاشتراك */}\n                <div className=\"mt-auto\">\n                  <div className=\"border-t border-slate-200 dark:border-slate-700 pt-4 mb-4\">\n                    <p className=\"text-sm font-medium text-slate-700 dark:text-slate-300 mb-3\">\n                      {currentLanguage === 'ar'\n                        ? 'اشترك للحصول على خصم إضافي 10%'\n                        : 'Subscribe for an additional 10% discount'}\n                    </p>\n\n                    <form onSubmit={handleSubmit}>\n                      <div className=\"mb-3\">\n                        <div className=\"relative\">\n                          <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400\" size={18} />\n                          <Input\n                            type=\"email\"\n                            placeholder={currentLanguage === 'ar' ? 'بريدك الإلكتروني' : 'Your email'}\n                            value={email}\n                            onChange={(e) => setEmail(e.target.value)}\n                            className={cn(\n                              \"pl-10 py-2 text-sm\",\n                              error ? \"border-error-500 focus:ring-error-500\" : \"\"\n                            )}\n                          />\n                        </div>\n                        {error && <p className=\"mt-1 text-xs text-error-500\">{error}</p>}\n                      </div>\n\n                      <Button\n                        type=\"submit\"\n                        variant=\"secondary\"\n                        className=\"w-full text-sm\"\n                        size=\"sm\"\n                        isLoading={isLoading}\n                      >\n                        {currentLanguage === 'ar' ? 'احصل على الخصم' : 'Get Discount'}\n                      </Button>\n                    </form>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"p-8 text-center\">\n              <div className=\"inline-flex items-center justify-center w-16 h-16 rounded-full bg-success-100 dark:bg-success-900 text-success-600 dark:text-success-300 mb-4\">\n                <Check className=\"h-8 w-8\" />\n              </div>\n              <h2 className=\"text-2xl font-bold mb-2 text-slate-900 dark:text-white\">\n                {currentLanguage === 'ar' ? 'شكراً لك!' : 'Thank You!'}\n              </h2>\n              <p className=\"text-slate-600 dark:text-slate-300 mb-2\">\n                {currentLanguage === 'ar'\n                  ? 'تم تسجيل بريدك الإلكتروني بنجاح.'\n                  : 'Your email has been registered successfully.'}\n              </p>\n              <p className=\"text-primary-600 dark:text-primary-400 font-medium mb-6\">\n                {currentLanguage === 'ar'\n                  ? 'رمز الخصم الخاص بك: WELCOME10'\n                  : 'Your discount code: WELCOME10'}\n              </p>\n\n              <div className=\"flex gap-3 justify-center\">\n                <Button\n                  variant=\"primary\"\n                  onClick={handleAddToCart}\n                >\n                  <ShoppingCart className=\"h-4 w-4 mr-2\" />\n                  {currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}\n                </Button>\n                <Button\n                  variant=\"outline\"\n                  onClick={internalHandleClose}\n                >\n                  {currentLanguage === 'ar' ? 'إغلاق' : 'Close'}\n                </Button>\n              </div>\n            </div>\n          )}\n        </div>\n      </HoverAnimation>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;;;AAdA;;;;;;;;;;;;;AAwBO,SAAS,gBAAgB,EAC9B,QAAQ,IAAI,EACZ,aAAa,kBAAkB,EAC/B,aAAa,CAAC,EACd,OAAO,EACP,OAAO,EACc;;IACrB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAExC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD;IACpC,MAAM,EAAE,CAAC,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAC5C,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,YAAY,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAE7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM,eACJ,eAAe,OAAO,CAAC,gBAAgB,UACvC,SAAS,MAAM,CAAC,QAAQ,CAAC,GAAG,WAAW,KAAK,CAAC;YAE/C,IAAI,cAAc;gBAChB;gBACA;YACF;YAEA,MAAM,QAAQ;mDAAW;oBACvB,SAAS,gBAAgB,CAAC,YAAY;gBACxC;kDAAG;YAEH;6CAAO;oBACL,aAAa;oBACb,SAAS,mBAAmB,CAAC,YAAY;gBAC3C;;QACF;oCAAG;QAAC;QAAO;QAAY;KAAQ;IAE/B,MAAM,mBAAmB,CAAC;QACxB,IAAI,EAAE,OAAO,IAAI,KAAK,CAAC,WAAW;YAChC,aAAa;YACb,SAAS,mBAAmB,CAAC,YAAY;YACzC,eAAe,OAAO,CAAC,YAAY;YACnC,IAAI,aAAa,GAAG;gBAClB,MAAM,aAAa,IAAI;gBACvB,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;gBAC1C,SAAS,MAAM,GAAG,GAAG,WAAW,eAAe,EAAE,WAAW,WAAW,GAAG,QAAQ,CAAC;YACrF;QACF;IACF;IAEA,MAAM,sBAAsB;QAC1B,aAAa;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;gEAAqB,CAAC;oBAC1B,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAW;wBACpE;oBACF;gBACF;;YAEA,IAAI,WAAW;gBACb,SAAS,gBAAgB,CAAC,aAAa;YACzC;YAEA;6CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;oCAAG;QAAC;QAAW;KAAoB;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,OAAO;YACV,SAAS,aAAa,OAAO,gCAAgC;YAC7D;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,sDAAsD;YACtD,0CAA0C;YAE1C,sBAAsB;YACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS,aAAa,OAAO,qCAAqC;QACpE,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,8BAA8B;IAC9B,MAAM,kBAAkB;QACtB,UAAU,OAAO,CAAC;YAChB,IAAI,QAAQ,EAAE;YACd,MAAM,QAAQ,IAAI;YAClB,SAAS,QAAQ,OAAO;YACxB,OAAO,QAAQ,KAAK;YACpB,OAAO,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,IAAI,QAAQ,MAAM,CAAC,EAAE,GAAG;YACzE,UAAU;QACZ;QAEA,qCAAqC;QACrC,WAAW;YACT;QACF,GAAG;IACL;IAEA,0CAA0C;IAC1C,MAAM,qBAAqB,QAAQ,cAAc,IAAI,QAAQ,cAAc,GAAG,QAAQ,KAAK,GACvF,KAAK,KAAK,CAAC,CAAC,IAAI,QAAQ,KAAK,GAAG,QAAQ,cAAc,IAAI,OAC1D,QAAQ,QAAQ,IAAI;IAExB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,2JAAA,CAAA,iBAAc;YAAC,WAAU;sBACxB,cAAA,6LAAC;gBACC,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA,kBAAkB,SAAS,iBAAiB,YAC5C;;kCAIF,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAY,oBAAoB,OAAO,UAAU;kCAEjD,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,MAAM;4BAAI,WAAU;;;;;;;;;;;oBAGxB,CAAC,4BACA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;oCACZ,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,EAAE,kBAClC,6LAAC,4IAAA,CAAA,gBAAa;wCACZ,KAAK,QAAQ,MAAM,CAAC,EAAE;wCACtB,KAAK,QAAQ,IAAI;wCACjB,IAAI;wCACJ,WAAU;wCACV,aAAa;wCACb,aAAY;wCACZ,oBAAmB;;;;;;kDAKvB,6LAAC;wCAAI,WAAU;;4CACZ,qBAAqB,mBACpB,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAc,MAAK;0DAC/B,oBAAoB,OAAO,GAAG,mBAAmB,KAAK,CAAC,GAAG,GAAG,mBAAmB,KAAK,CAAC;;;;;;4CAG1F,QAAQ,KAAK,kBACZ,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,MAAK;0DAC3B,oBAAoB,OAAO,SAAS;;;;;;;;;;;;;;;;;;0CAO7C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,oBAAoB,OAAO,gBAAgB;;;;;;0DAG9C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,oBAAoB,OAAQ,QAAQ,OAAO,IAAI,QAAQ,IAAI,GAAI,QAAQ,IAAI;;;;;;kEAG9E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,KAAK;;;;;;4DAE9B,QAAQ,cAAc,IAAI,QAAQ,cAAc,GAAG,QAAQ,KAAK,kBAC/D,6LAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,cAAc;;;;;;;;;;;;;;;;;;0DAM9C,6LAAC;gDAAE,WAAU;0DACV,oBAAoB,OACjB,8EACA;;;;;;0DAIN,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;;0EAET,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;0EACxB,6LAAC;0EAAM,oBAAoB,OAAO,cAAc;;;;;;;;;;;;kEAGlD,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS;kEAET,cAAA,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAM5B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV,oBAAoB,OACjB,mCACA;;;;;;8DAGN,6LAAC;oDAAK,UAAU;;sEACd,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;4EAAoE,MAAM;;;;;;sFAC1F,6LAAC,oIAAA,CAAA,QAAK;4EACJ,MAAK;4EACL,aAAa,oBAAoB,OAAO,qBAAqB;4EAC7D,OAAO;4EACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4EACxC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sBACA,QAAQ,0CAA0C;;;;;;;;;;;;gEAIvD,uBAAS,6LAAC;oEAAE,WAAU;8EAA+B;;;;;;;;;;;;sEAGxD,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,WAAU;4DACV,MAAK;4DACL,WAAW;sEAEV,oBAAoB,OAAO,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6CAQ3D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,6LAAC;gCAAG,WAAU;0CACX,oBAAoB,OAAO,cAAc;;;;;;0CAE5C,6LAAC;gCAAE,WAAU;0CACV,oBAAoB,OACjB,qCACA;;;;;;0CAEN,6LAAC;gCAAE,WAAU;0CACV,oBAAoB,OACjB,kCACA;;;;;;0CAGN,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;;0DAET,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CACvB,oBAAoB,OAAO,cAAc;;;;;;;kDAE5C,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;kDAER,oBAAoB,OAAO,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxD;GApTgB;;QAcO,iIAAA,CAAA,mBAAgB;QACN,+HAAA,CAAA,iBAAc;QACnB,mJAAA,CAAA,WAAQ;QAChB,6HAAA,CAAA,eAAY;;;KAjBhB", "debugId": null}}, {"offset": {"line": 3471, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/data/machinery.ts"], "sourcesContent": ["export interface MachineryItem {\n  id: string;\n  name: string;\n  name_ar?: string;\n  description: string;\n  description_ar?: string;\n  category: string;\n  category_ar?: string;\n  slug: string;\n  images: string[];\n  price?: number;\n  priceRange?: {\n    min: number;\n    max: number;\n  };\n  pricing: 'contact-for-quote' | 'price-on-request' | 'custom-quote';\n  specifications: {\n    [key: string]: string;\n  };\n  specifications_ar?: {\n    [key: string]: string;\n  };\n  features: string[];\n  features_ar?: string[];\n  applications: string[];\n  applications_ar?: string[];\n  tags: string[];\n  availability: 'in-stock' | 'on-order' | 'custom-order';\n  leadTime?: string;\n  leadTime_ar?: string;\n  warranty?: string;\n  warranty_ar?: string;\n  origin?: string;\n  origin_ar?: string;\n  certification?: string[];\n  certification_ar?: string[];\n  createdAt: string;\n  updatedAt: string;\n  featured?: boolean;\n  isNew?: boolean;\n}\n\nexport const machineryCategories = [\n  {\n    id: 'production-lines',\n    name: 'Production Lines',\n    name_ar: 'خطوط الإنتاج',\n    description: 'Complete automated production line systems',\n    description_ar: 'أنظمة خطوط الإنتاج الآلية المتكاملة',\n    icon: 'Factory',\n    slug: 'production-lines'\n  },\n  {\n    id: 'packaging-machines',\n    name: 'Packaging Machines',\n    name_ar: 'آلات التعبئة والتغليف',\n    description: 'Advanced packaging and wrapping equipment',\n    description_ar: 'معدات التعبئة والتغليف المتطورة',\n    icon: 'Package',\n    slug: 'packaging-machines'\n  },\n  {\n    id: 'processing-equipment',\n    name: 'Processing Equipment',\n    name_ar: 'معدات المعالجة',\n    description: 'Industrial processing and manufacturing equipment',\n    description_ar: 'معدات المعالجة والتصنيع الصناعية',\n    icon: 'Cog',\n    slug: 'processing-equipment'\n  },\n  {\n    id: 'quality-control',\n    name: 'Quality Control Systems',\n    name_ar: 'أنظمة مراقبة الجودة',\n    description: 'Automated quality inspection and testing systems',\n    description_ar: 'أنظمة الفحص واختبار الجودة الآلية',\n    icon: 'CheckCircle',\n    slug: 'quality-control'\n  },\n  {\n    id: 'raw-materials',\n    name: 'Raw Materials',\n    name_ar: 'المواد الخام',\n    description: 'High-quality industrial raw materials and supplies',\n    description_ar: 'مواد خام ومستلزمات صناعية عالية الجودة',\n    icon: 'Layers',\n    slug: 'raw-materials'\n  },\n  {\n    id: 'automation-systems',\n    name: 'Automation Systems',\n    name_ar: 'أنظمة الأتمتة',\n    description: 'Industrial automation and control systems',\n    description_ar: 'أنظمة الأتمتة والتحكم الصناعية',\n    icon: 'Cpu',\n    slug: 'automation-systems'\n  }\n];\n\nexport const machineryData: MachineryItem[] = [\n  {\n    id: 'auto-packaging-line-001',\n    name: 'Automated Food Packaging Line',\n    name_ar: 'خط التعبئة الآلي للأغذية',\n    description: 'Complete automated packaging solution for food products with high-speed filling, sealing, and labeling capabilities.',\n    description_ar: 'حل التعبئة الآلي المتكامل للمنتجات الغذائية مع قدرات الملء والإغلاق والوسم عالية السرعة.',\n    category: 'production-lines',\n    category_ar: 'خطوط الإنتاج',\n    slug: 'automated-food-packaging-line',\n    images: [\n      '/images/machinery/packaging-line-1.svg',\n      '/images/machinery/packaging-line-1.svg',\n      '/images/machinery/packaging-line-1.svg'\n    ],\n    priceRange: {\n      min: 250000,\n      max: 500000\n    },\n    pricing: 'contact-for-quote' as const,\n    specifications: {\n      'Production Capacity': '1000-5000 units/hour',\n      'Power Consumption': '15-25 kW',\n      'Dimensions': '12m x 3m x 2.5m',\n      'Weight': '8500 kg',\n      'Material': 'Stainless Steel 316L',\n      'Control System': 'PLC with HMI touchscreen'\n    },\n    specifications_ar: {\n      'الطاقة الإنتاجية': '1000-5000 وحدة/ساعة',\n      'استهلاك الطاقة': '15-25 كيلو واط',\n      'الأبعاد': '12م × 3م × 2.5م',\n      'الوزن': '8500 كجم',\n      'المادة': 'ستانلس ستيل 316L',\n      'نظام التحكم': 'PLC مع شاشة لمس HMI'\n    },\n    features: [\n      'Fully automated operation',\n      'High-speed processing',\n      'Quality control integration',\n      'Easy maintenance access',\n      'Modular design',\n      'Energy efficient'\n    ],\n    features_ar: [\n      'تشغيل آلي بالكامل',\n      'معالجة عالية السرعة',\n      'تكامل مراقبة الجودة',\n      'سهولة الوصول للصيانة',\n      'تصميم معياري',\n      'كفاءة في استهلاك الطاقة'\n    ],\n    applications: [\n      'Food and beverage industry',\n      'Pharmaceutical packaging',\n      'Cosmetics industry',\n      'Chemical products'\n    ],\n    applications_ar: [\n      'صناعة الأغذية والمشروبات',\n      'تعبئة الأدوية',\n      'صناعة مستحضرات التجميل',\n      'المنتجات الكيميائية'\n    ],\n    tags: ['packaging', 'automation', 'food-industry', 'high-speed'],\n    availability: 'on-order',\n    leadTime: '8-12 weeks',\n    leadTime_ar: '8-12 أسبوع',\n    warranty: '2 years full warranty + 5 years parts',\n    warranty_ar: 'ضمان شامل لمدة سنتين + 5 سنوات قطع غيار',\n    origin: 'Germany',\n    origin_ar: 'ألمانيا',\n    certification: ['CE', 'ISO 9001', 'FDA Approved'],\n    certification_ar: ['CE', 'ISO 9001', 'معتمد من FDA'],\n    createdAt: '2024-01-15T00:00:00Z',\n    updatedAt: '2024-06-01T00:00:00Z',\n    featured: true,\n    isNew: false\n  },\n  {\n    id: 'cnc-machining-center-002',\n    name: 'CNC Machining Center',\n    name_ar: 'مركز التشغيل CNC',\n    description: 'High-precision CNC machining center for complex metal parts manufacturing with advanced automation features.',\n    description_ar: 'مركز تشغيل CNC عالي الدقة لتصنيع القطع المعدنية المعقدة مع ميزات الأتمتة المتقدمة.',\n    category: 'processing-equipment',\n    category_ar: 'معدات المعالجة',\n    slug: 'cnc-machining-center',\n    images: [\n      '/images/machinery/cnc-machine-1.svg',\n      '/images/machinery/cnc-machine-1.svg',\n      '/images/machinery/cnc-machine-1.svg'\n    ],\n    priceRange: {\n      min: 180000,\n      max: 350000\n    },\n    pricing: 'contact-for-quote' as const,\n    specifications: {\n      'Working Area': '800mm x 600mm x 500mm',\n      'Spindle Speed': '12000 RPM',\n      'Tool Capacity': '24 tools',\n      'Positioning Accuracy': '±0.005mm',\n      'Power Consumption': '20 kW',\n      'Weight': '4500 kg'\n    },\n    specifications_ar: {\n      'منطقة العمل': '800مم × 600مم × 500مم',\n      'سرعة المغزل': '12000 دورة/دقيقة',\n      'سعة الأدوات': '24 أداة',\n      'دقة التموضع': '±0.005مم',\n      'استهلاك الطاقة': '20 كيلو واط',\n      'الوزن': '4500 كجم'\n    },\n    features: [\n      'High-speed machining',\n      'Automatic tool changer',\n      'Advanced CNC control',\n      'Precision positioning',\n      'Coolant system',\n      'Safety enclosure'\n    ],\n    features_ar: [\n      'تشغيل عالي السرعة',\n      'مغير أدوات أوتوماتيكي',\n      'تحكم CNC متقدم',\n      'تموضع دقيق',\n      'نظام تبريد',\n      'حاوية أمان'\n    ],\n    applications: [\n      'Aerospace industry',\n      'Automotive parts',\n      'Medical devices',\n      'Precision tooling'\n    ],\n    applications_ar: [\n      'صناعة الطيران',\n      'قطع السيارات',\n      'الأجهزة الطبية',\n      'الأدوات الدقيقة'\n    ],\n    tags: ['cnc', 'machining', 'precision', 'automation'],\n    availability: 'in-stock',\n    leadTime: '4-6 weeks',\n    leadTime_ar: '4-6 أسابيع',\n    warranty: '3 years full warranty',\n    warranty_ar: 'ضمان شامل لمدة 3 سنوات',\n    origin: 'Japan',\n    origin_ar: 'اليابان',\n    certification: ['CE', 'ISO 9001', 'JIS'],\n    certification_ar: ['CE', 'ISO 9001', 'JIS'],\n    createdAt: '2024-02-01T00:00:00Z',\n    updatedAt: '2024-06-15T00:00:00Z',\n    featured: true,\n    isNew: true\n  },\n  {\n    id: 'quality-inspection-system-003',\n    name: 'Automated Quality Inspection System',\n    name_ar: 'نظام فحص الجودة الآلي',\n    description: 'Advanced vision-based quality control system for automated inspection and defect detection.',\n    description_ar: 'نظام مراقبة جودة متقدم يعتمد على الرؤية للفحص الآلي وكشف العيوب.',\n    category: 'quality-control',\n    category_ar: 'أنظمة مراقبة الجودة',\n    slug: 'automated-quality-inspection-system',\n    images: [\n      '/images/machinery/inspection-system-1.jpg',\n      '/images/machinery/inspection-system-2.jpg'\n    ],\n    priceRange: {\n      min: 120000,\n      max: 200000\n    },\n    pricing: 'contact-for-quote' as const,\n    specifications: {\n      'Inspection Speed': '200 parts/minute',\n      'Resolution': '0.1mm',\n      'Detection Accuracy': '99.8%',\n      'Camera Type': 'High-resolution CCD',\n      'Processing Unit': 'Industrial PC',\n      'Interface': 'Ethernet, USB, Serial'\n    },\n    specifications_ar: {\n      'سرعة الفحص': '200 قطعة/دقيقة',\n      'الدقة': '0.1مم',\n      'دقة الكشف': '99.8%',\n      'نوع الكاميرا': 'CCD عالية الدقة',\n      'وحدة المعالجة': 'حاسوب صناعي',\n      'الواجهة': 'إيثرنت، USB، تسلسلي'\n    },\n    features: [\n      'Machine vision technology',\n      'Real-time processing',\n      'Defect classification',\n      'Statistical reporting',\n      'Remote monitoring',\n      'Easy integration'\n    ],\n    features_ar: [\n      'تقنية الرؤية الآلية',\n      'معالجة فورية',\n      'تصنيف العيوب',\n      'تقارير إحصائية',\n      'مراقبة عن بعد',\n      'سهولة التكامل'\n    ],\n    applications: [\n      'Electronics manufacturing',\n      'Automotive assembly',\n      'Pharmaceutical production',\n      'Food processing'\n    ],\n    applications_ar: [\n      'تصنيع الإلكترونيات',\n      'تجميع السيارات',\n      'إنتاج الأدوية',\n      'معالجة الأغذية'\n    ],\n    tags: ['quality-control', 'vision-system', 'automation', 'inspection'],\n    availability: 'on-order',\n    leadTime: '6-8 weeks',\n    leadTime_ar: '6-8 أسابيع',\n    warranty: '2 years warranty + software updates',\n    warranty_ar: 'ضمان لمدة سنتين + تحديثات البرمجيات',\n    origin: 'Germany',\n    origin_ar: 'ألمانيا',\n    certification: ['CE', 'ISO 9001', 'RoHS'],\n    certification_ar: ['CE', 'ISO 9001', 'RoHS'],\n    createdAt: '2024-03-01T00:00:00Z',\n    updatedAt: '2024-06-20T00:00:00Z',\n    featured: false,\n    isNew: true\n  },\n  {\n    id: 'packaging-automation-004',\n    name: 'Packaging Automation System',\n    name_ar: 'نظام أتمتة التعبئة والتغليف',\n    description: 'Advanced packaging automation system with robotic arms and intelligent sorting capabilities for various product types.',\n    description_ar: 'نظام أتمتة التعبئة والتغليف المتقدم مع أذرع روبوتية وقدرات فرز ذكية لأنواع مختلفة من المنتجات.',\n    category: 'packaging-machines',\n    category_ar: 'آلات التعبئة والتغليف',\n    slug: 'packaging-automation',\n    images: [\n      'https://images.pexels.com/photos/4484078/pexels-photo-4484078.jpeg',\n      'https://images.pexels.com/photos/2874793/pexels-photo-2874793.jpeg'\n    ],\n    priceRange: {\n      min: 320000,\n      max: 650000\n    },\n    pricing: 'contact-for-quote' as const,\n    specifications: {\n      'Packaging Speed': '2000-8000 units/hour',\n      'Robot Reach': '1.5m radius',\n      'Payload Capacity': '25kg',\n      'Power Consumption': '18-30 kW',\n      'Dimensions': '15m x 4m x 3m',\n      'Control System': 'Advanced PLC with AI integration'\n    },\n    specifications_ar: {\n      'سرعة التعبئة': '2000-8000 وحدة/ساعة',\n      'مدى الروبوت': 'نصف قطر 1.5م',\n      'سعة الحمولة': '25 كجم',\n      'استهلاك الطاقة': '18-30 كيلو واط',\n      'الأبعاد': '15م × 4م × 3م',\n      'نظام التحكم': 'PLC متقدم مع تكامل الذكاء الاصطناعي'\n    },\n    features: [\n      'Robotic automation',\n      'AI-powered sorting',\n      'Multi-product handling',\n      'Real-time monitoring',\n      'Predictive maintenance',\n      'Energy optimization'\n    ],\n    features_ar: [\n      'أتمتة روبوتية',\n      'فرز مدعوم بالذكاء الاصطناعي',\n      'التعامل مع منتجات متعددة',\n      'مراقبة فورية',\n      'صيانة تنبؤية',\n      'تحسين الطاقة'\n    ],\n    applications: [\n      'Food & beverage packaging',\n      'Pharmaceutical packaging',\n      'Consumer goods',\n      'E-commerce fulfillment'\n    ],\n    applications_ar: [\n      'تعبئة الأغذية والمشروبات',\n      'تعبئة الأدوية',\n      'السلع الاستهلاكية',\n      'تنفيذ التجارة الإلكترونية'\n    ],\n    tags: ['packaging', 'robotics', 'automation', 'ai'],\n    availability: 'on-order',\n    leadTime: '12-16 weeks',\n    leadTime_ar: '12-16 أسبوع',\n    warranty: '3 years comprehensive warranty',\n    warranty_ar: 'ضمان شامل لمدة 3 سنوات',\n    origin: 'Germany',\n    origin_ar: 'ألمانيا',\n    certification: ['CE', 'ISO 9001', 'ATEX'],\n    certification_ar: ['CE', 'ISO 9001', 'ATEX'],\n    createdAt: '2024-04-01T00:00:00Z',\n    updatedAt: '2024-06-25T00:00:00Z',\n    featured: true,\n    isNew: true\n  },\n  {\n    id: 'industrial-mixer-005',\n    name: 'Industrial Mixing System',\n    name_ar: 'نظام الخلط الصناعي',\n    description: 'High-capacity industrial mixing system for chemical, pharmaceutical, and food processing applications.',\n    description_ar: 'نظام خلط صناعي عالي السعة للتطبيقات الكيميائية والصيدلانية ومعالجة الأغذية.',\n    category: 'processing-equipment',\n    category_ar: 'معدات المعالجة',\n    slug: 'industrial-mixer',\n    images: [\n      'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg',\n      'https://images.pexels.com/photos/2582937/pexels-photo-2582937.jpeg'\n    ],\n    priceRange: {\n      min: 85000,\n      max: 220000\n    },\n    pricing: 'contact-for-quote' as const,\n    specifications: {\n      'Mixing Capacity': '500-5000 liters',\n      'Motor Power': '15-75 kW',\n      'Mixing Speed': '10-200 RPM',\n      'Material': 'Stainless Steel 316L',\n      'Temperature Range': '-20°C to +200°C',\n      'Pressure Rating': '10 bar'\n    },\n    specifications_ar: {\n      'سعة الخلط': '500-5000 لتر',\n      'قوة المحرك': '15-75 كيلو واط',\n      'سرعة الخلط': '10-200 دورة/دقيقة',\n      'المادة': 'ستانلس ستيل 316L',\n      'نطاق درجة الحرارة': '-20°م إلى +200°م',\n      'تصنيف الضغط': '10 بار'\n    },\n    features: [\n      'Variable speed control',\n      'Temperature monitoring',\n      'Pressure relief system',\n      'CIP cleaning system',\n      'Explosion-proof design',\n      'Remote operation'\n    ],\n    features_ar: [\n      'تحكم في السرعة المتغيرة',\n      'مراقبة درجة الحرارة',\n      'نظام تخفيف الضغط',\n      'نظام تنظيف CIP',\n      'تصميم مقاوم للانفجار',\n      'تشغيل عن بعد'\n    ],\n    applications: [\n      'Chemical processing',\n      'Pharmaceutical manufacturing',\n      'Food production',\n      'Cosmetics industry'\n    ],\n    applications_ar: [\n      'المعالجة الكيميائية',\n      'التصنيع الصيدلاني',\n      'إنتاج الأغذية',\n      'صناعة مستحضرات التجميل'\n    ],\n    tags: ['mixing', 'processing', 'chemical', 'pharmaceutical'],\n    availability: 'in-stock',\n    leadTime: '6-10 weeks',\n    leadTime_ar: '6-10 أسابيع',\n    warranty: '2 years parts and labor',\n    warranty_ar: 'ضمان قطع غيار وعمالة لمدة سنتين',\n    origin: 'Italy',\n    origin_ar: 'إيطاليا',\n    certification: ['CE', 'FDA', 'ASME'],\n    certification_ar: ['CE', 'FDA', 'ASME'],\n    createdAt: '2024-05-01T00:00:00Z',\n    updatedAt: '2024-06-30T00:00:00Z',\n    featured: false,\n    isNew: false\n  },\n  {\n    id: 'automation-controller-006',\n    name: 'Industrial Automation Controller',\n    name_ar: 'وحدة تحكم الأتمتة الصناعية',\n    description: 'Advanced PLC-based automation controller with IoT connectivity and real-time data analytics capabilities.',\n    description_ar: 'وحدة تحكم أتمتة متقدمة تعتمد على PLC مع اتصال إنترنت الأشياء وقدرات تحليل البيانات الفورية.',\n    category: 'automation-systems',\n    category_ar: 'أنظمة الأتمتة',\n    slug: 'automation-controller',\n    images: [\n      'https://images.pexels.com/photos/163100/circuit-circuit-board-resistor-computer-163100.jpeg',\n      'https://images.pexels.com/photos/2582937/pexels-photo-2582937.jpeg'\n    ],\n    priceRange: {\n      min: 25000,\n      max: 85000\n    },\n    pricing: 'contact-for-quote' as const,\n    specifications: {\n      'Processing Speed': '1 GHz dual-core',\n      'Memory': '2GB RAM, 32GB Storage',\n      'I/O Capacity': '512 digital, 128 analog',\n      'Communication': 'Ethernet, WiFi, 4G/5G',\n      'Operating Temperature': '-40°C to +70°C',\n      'Power Supply': '24VDC, 5A'\n    },\n    specifications_ar: {\n      'سرعة المعالجة': '1 جيجاهرتز ثنائي النواة',\n      'الذاكرة': '2 جيجابايت رام، 32 جيجابايت تخزين',\n      'سعة الإدخال/الإخراج': '512 رقمي، 128 تناظري',\n      'الاتصال': 'إيثرنت، واي فاي، 4G/5G',\n      'درجة حرارة التشغيل': '-40°م إلى +70°م',\n      'مصدر الطاقة': '24 فولت تيار مستمر، 5 أمبير'\n    },\n    features: [\n      'IoT connectivity',\n      'Real-time analytics',\n      'Cloud integration',\n      'Predictive maintenance',\n      'Remote monitoring',\n      'Cybersecurity features'\n    ],\n    features_ar: [\n      'اتصال إنترنت الأشياء',\n      'تحليلات فورية',\n      'تكامل سحابي',\n      'صيانة تنبؤية',\n      'مراقبة عن بعد',\n      'ميزات الأمن السيبراني'\n    ],\n    applications: [\n      'Manufacturing automation',\n      'Process control',\n      'Building automation',\n      'Energy management'\n    ],\n    applications_ar: [\n      'أتمتة التصنيع',\n      'التحكم في العمليات',\n      'أتمتة المباني',\n      'إدارة الطاقة'\n    ],\n    tags: ['automation', 'plc', 'iot', 'control-system'],\n    availability: 'in-stock',\n    leadTime: '2-4 weeks',\n    leadTime_ar: '2-4 أسابيع',\n    warranty: '5 years manufacturer warranty',\n    warranty_ar: 'ضمان الشركة المصنعة لمدة 5 سنوات',\n    origin: 'USA',\n    origin_ar: 'الولايات المتحدة',\n    certification: ['UL', 'CE', 'FCC'],\n    certification_ar: ['UL', 'CE', 'FCC'],\n    createdAt: '2024-06-01T00:00:00Z',\n    updatedAt: '2024-07-01T00:00:00Z',\n    featured: true,\n    isNew: true\n  }\n];\n"], "names": [], "mappings": ";;;;AA0CO,MAAM,sBAAsB;IACjC;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,MAAM;IACR;CACD;AAEM,MAAM,gBAAiC;IAC5C;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,UAAU;QACV,aAAa;QACb,MAAM;QACN,QAAQ;YACN;YACA;YACA;SACD;QACD,YAAY;YACV,KAAK;YACL,KAAK;QACP;QACA,SAAS;QACT,gBAAgB;YACd,uBAAuB;YACvB,qBAAqB;YACrB,cAAc;YACd,UAAU;YACV,YAAY;YACZ,kBAAkB;QACpB;QACA,mBAAmB;YACjB,oBAAoB;YACpB,kBAAkB;YAClB,WAAW;YACX,SAAS;YACT,UAAU;YACV,eAAe;QACjB;QACA,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,iBAAiB;YACf;YACA;YACA;YACA;SACD;QACD,MAAM;YAAC;YAAa;YAAc;YAAiB;SAAa;QAChE,cAAc;QACd,UAAU;QACV,aAAa;QACb,UAAU;QACV,aAAa;QACb,QAAQ;QACR,WAAW;QACX,eAAe;YAAC;YAAM;YAAY;SAAe;QACjD,kBAAkB;YAAC;YAAM;YAAY;SAAe;QACpD,WAAW;QACX,WAAW;QACX,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,UAAU;QACV,aAAa;QACb,MAAM;QACN,QAAQ;YACN;YACA;YACA;SACD;QACD,YAAY;YACV,KAAK;YACL,KAAK;QACP;QACA,SAAS;QACT,gBAAgB;YACd,gBAAgB;YAChB,iBAAiB;YACjB,iBAAiB;YACjB,wBAAwB;YACxB,qBAAqB;YACrB,UAAU;QACZ;QACA,mBAAmB;YACjB,eAAe;YACf,eAAe;YACf,eAAe;YACf,eAAe;YACf,kBAAkB;YAClB,SAAS;QACX;QACA,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,iBAAiB;YACf;YACA;YACA;YACA;SACD;QACD,MAAM;YAAC;YAAO;YAAa;YAAa;SAAa;QACrD,cAAc;QACd,UAAU;QACV,aAAa;QACb,UAAU;QACV,aAAa;QACb,QAAQ;QACR,WAAW;QACX,eAAe;YAAC;YAAM;YAAY;SAAM;QACxC,kBAAkB;YAAC;YAAM;YAAY;SAAM;QAC3C,WAAW;QACX,WAAW;QACX,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,UAAU;QACV,aAAa;QACb,MAAM;QACN,QAAQ;YACN;YACA;SACD;QACD,YAAY;YACV,KAAK;YACL,KAAK;QACP;QACA,SAAS;QACT,gBAAgB;YACd,oBAAoB;YACpB,cAAc;YACd,sBAAsB;YACtB,eAAe;YACf,mBAAmB;YACnB,aAAa;QACf;QACA,mBAAmB;YACjB,cAAc;YACd,SAAS;YACT,aAAa;YACb,gBAAgB;YAChB,iBAAiB;YACjB,WAAW;QACb;QACA,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,iBAAiB;YACf;YACA;YACA;YACA;SACD;QACD,MAAM;YAAC;YAAmB;YAAiB;YAAc;SAAa;QACtE,cAAc;QACd,UAAU;QACV,aAAa;QACb,UAAU;QACV,aAAa;QACb,QAAQ;QACR,WAAW;QACX,eAAe;YAAC;YAAM;YAAY;SAAO;QACzC,kBAAkB;YAAC;YAAM;YAAY;SAAO;QAC5C,WAAW;QACX,WAAW;QACX,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,UAAU;QACV,aAAa;QACb,MAAM;QACN,QAAQ;YACN;YACA;SACD;QACD,YAAY;YACV,KAAK;YACL,KAAK;QACP;QACA,SAAS;QACT,gBAAgB;YACd,mBAAmB;YACnB,eAAe;YACf,oBAAoB;YACpB,qBAAqB;YACrB,cAAc;YACd,kBAAkB;QACpB;QACA,mBAAmB;YACjB,gBAAgB;YAChB,eAAe;YACf,eAAe;YACf,kBAAkB;YAClB,WAAW;YACX,eAAe;QACjB;QACA,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,iBAAiB;YACf;YACA;YACA;YACA;SACD;QACD,MAAM;YAAC;YAAa;YAAY;YAAc;SAAK;QACnD,cAAc;QACd,UAAU;QACV,aAAa;QACb,UAAU;QACV,aAAa;QACb,QAAQ;QACR,WAAW;QACX,eAAe;YAAC;YAAM;YAAY;SAAO;QACzC,kBAAkB;YAAC;YAAM;YAAY;SAAO;QAC5C,WAAW;QACX,WAAW;QACX,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,UAAU;QACV,aAAa;QACb,MAAM;QACN,QAAQ;YACN;YACA;SACD;QACD,YAAY;YACV,KAAK;YACL,KAAK;QACP;QACA,SAAS;QACT,gBAAgB;YACd,mBAAmB;YACnB,eAAe;YACf,gBAAgB;YAChB,YAAY;YACZ,qBAAqB;YACrB,mBAAmB;QACrB;QACA,mBAAmB;YACjB,aAAa;YACb,cAAc;YACd,cAAc;YACd,UAAU;YACV,qBAAqB;YACrB,eAAe;QACjB;QACA,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,iBAAiB;YACf;YACA;YACA;YACA;SACD;QACD,MAAM;YAAC;YAAU;YAAc;YAAY;SAAiB;QAC5D,cAAc;QACd,UAAU;QACV,aAAa;QACb,UAAU;QACV,aAAa;QACb,QAAQ;QACR,WAAW;QACX,eAAe;YAAC;YAAM;YAAO;SAAO;QACpC,kBAAkB;YAAC;YAAM;YAAO;SAAO;QACvC,WAAW;QACX,WAAW;QACX,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,UAAU;QACV,aAAa;QACb,MAAM;QACN,QAAQ;YACN;YACA;SACD;QACD,YAAY;YACV,KAAK;YACL,KAAK;QACP;QACA,SAAS;QACT,gBAAgB;YACd,oBAAoB;YACpB,UAAU;YACV,gBAAgB;YAChB,iBAAiB;YACjB,yBAAyB;YACzB,gBAAgB;QAClB;QACA,mBAAmB;YACjB,iBAAiB;YACjB,WAAW;YACX,uBAAuB;YACvB,WAAW;YACX,sBAAsB;YACtB,eAAe;QACjB;QACA,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,iBAAiB;YACf;YACA;YACA;YACA;SACD;QACD,MAAM;YAAC;YAAc;YAAO;YAAO;SAAiB;QACpD,cAAc;QACd,UAAU;QACV,aAAa;QACb,UAAU;QACV,aAAa;QACb,QAAQ;QACR,WAAW;QACX,eAAe;YAAC;YAAM;YAAM;SAAM;QAClC,kBAAkB;YAAC;YAAM;YAAM;SAAM;QACrC,WAAW;QACX,WAAW;QACX,UAAU;QACV,OAAO;IACT;CACD", "debugId": null}}, {"offset": {"line": 4084, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/pages/HomePage.tsx"], "sourcesContent": ["'use client';\n\nimport { Suspense, lazy } from 'react';\nimport Link from 'next/link';\nimport { useState, useEffect } from 'react';\nimport { ArrowRight, ShoppingCart, Users, Package, Truck, FileText, Tag, BarChart as ChartBar, Globe, Clock, Shield, CheckCircle, ArrowUpRight, Heart, Star, Eye, Settings, Gauge, PenTool as Tool, Calendar, FileCheck, Search, Building2, Play, MapPin, X, Send, Box, Factory, Bell } from 'lucide-react';\nimport { Button } from '../components/ui/Button';\nimport { HeroButton } from '../components/ui/HeroButton';\nimport { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card';\nimport { LazyImage } from '../components/ui/LazyImage';\nimport { EnhancedImage } from '../components/ui/EnhancedImage';\nimport { useCartStore } from '../stores/cartStore';\nimport { useWishlistStore } from '../stores/wishlistStore';\nimport { useAuthStore } from '../stores/authStore';\nimport { useLanguageStore } from '../stores/languageStore';\nimport { useTranslation } from '../translations';\nimport { useAuthModalStore } from '../stores/authModalStore';\nimport { WholesaleQuoteForm } from '../components/forms/WholesaleQuoteForm';\nimport { NewsletterForm } from '../components/forms/NewsletterForm';\nimport { QuickView } from '../components/shop/QuickView';\nimport { ExitIntentPopup } from '../components/marketing/ExitIntentPopup';\nimport { useABTesting } from '../components/marketing/ABTestingProvider';\nimport { SmoothTransition } from '../components/ui/animations/SmoothTransition';\nimport { ScrollAnimation, ScrollStagger, HoverAnimation } from '../components/ui/animations';\nimport { motion } from 'framer-motion';\nimport { clearanceItems } from '../data/clearanceItems';\nimport { productionLines } from '../data/productionLines';\nimport { machineryData } from '../data/machinery';\nimport { services } from '../data/services';\nimport { blogPosts } from '../data/blogPosts';\nimport { formatCurrency } from '../lib/utils';\nimport { getDbInstance } from '../lib/sqlite';\nimport { Product, ClearanceItem, Service } from '../types/index';\nimport { WishlistButton } from '../components/shop/WishlistButton';\nimport { QuickViewButton } from '../components/shop/QuickViewButton';\nimport { AddToCartButton } from '../components/shop/AddToCartButton';\nimport { ProductCard } from '../components/product/ProductCard';\nimport { ProductGrid } from '../components/product/ProductGrid';\n\n// صور القسم الرئيسي حسب اللغة - تم تحديثها لتكون أكثر تعبيراً عن المحتوى النصي\nconst heroImagesData = {\n  en: [\n    {\n      url: 'https://images.unsplash.com/photo-1661956602116-aa6865609028?ixlib=rb-4.0.3&ixid=M3wxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1664&q=80',\n      alt: 'Business solutions showcase',\n      title: 'Business Solutions',\n      subtitle: 'Comprehensive solutions for your business needs'\n    },\n    {\n      url: 'https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',\n      alt: 'Professional services',\n      title: 'Professional Services',\n      subtitle: 'Expert services to support your business growth'\n    },\n    {\n      url: 'https://images.unsplash.com/photo-1494412574643-ff11b0a5c1c3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',\n      alt: 'Quality products',\n      title: 'Quality Products',\n      subtitle: 'Premium products with quality guarantee'\n    }\n  ],\n  ar: [\n    {\n      url: 'https://images.unsplash.com/photo-1661956602116-aa6865609028?ixlib=rb-4.0.3&ixid=M3wxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1664&q=80',\n      alt: 'عرض حلول الأعمال',\n      title: 'حلول الأعمال',\n      subtitle: 'حلول شاملة لاحتياجات عملك'\n    },\n    {\n      url: 'https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',\n      alt: 'خدمات احترافية',\n      title: 'خدمات احترافية',\n      subtitle: 'خدمات خبيرة لدعم نمو أعمالك'\n    },\n    {\n      url: 'https://images.unsplash.com/photo-1494412574643-ff11b0a5c1c3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',\n      alt: 'منتجات عالية الجودة',\n      title: 'منتجات عالية الجودة',\n      subtitle: 'منتجات متميزة مع ضمان الجودة'\n    }\n  ]\n};\n\n// حلول الأعمال حسب اللغة\nconst solutionsData = {\n  en: [\n    {\n      icon: <ShoppingCart size={24} />,\n      title: \"Retail (B2C)\",\n      description: \"Shop our extensive catalog of products with competitive pricing and fast delivery options.\",\n      features: [\n        \"Premium product selection\",\n        \"Fast worldwide shipping\",\n        \"Secure payment processing\"\n      ],\n      image: \"https://images.pexels.com/photos/3862130/pexels-photo-3862130.jpeg\"\n    },\n    {\n      icon: <Users size={24} />,\n      title: \"Wholesale (B2B)\",\n      description: \"Request quotes for bulk orders with special pricing available for business customers.\",\n      features: [\n        \"Volume discounts\",\n        \"Dedicated account manager\",\n        \"Flexible payment terms\"\n      ],\n      image: \"https://images.pexels.com/photos/4483610/pexels-photo-4483610.jpeg\"\n    },\n    {\n      icon: <Package size={24} />,\n      title: \"Production Lines\",\n      description: \"Browse our selection of turnkey manufacturing solutions with detailed specifications.\",\n      features: [\n        \"Automated systems\",\n        \"Quality control integration\",\n        \"Technical support\"\n      ],\n      image: \"https://images.pexels.com/photos/2159235/pexels-photo-2159235.jpeg\"\n    },\n    {\n      icon: <Truck size={24} />,\n      title: \"Business Services\",\n      description: \"Inspection, storage, shipping, certification, and consulting services to support your operations.\",\n      features: [\n        \"Professional consulting\",\n        \"Quality inspections\",\n        \"Logistics solutions\"\n      ],\n      image: \"https://images.pexels.com/photos/4483608/pexels-photo-4483608.jpeg\"\n    },\n    {\n      icon: <Tag size={24} />,\n      title: \"Clearance Sales\",\n      description: \"Take advantage of special deals on bulk liquidation items and overstock products.\",\n      features: [\n        \"Exclusive discounts\",\n        \"Bulk opportunities\",\n        \"Quick shipping\"\n      ],\n      image: \"https://images.pexels.com/photos/5668473/pexels-photo-5668473.jpeg\"\n    },\n    {\n      icon: <FileText size={24} />,\n      title: \"Industry Insights\",\n      description: \"Stay informed with our blog featuring the latest trends, tips, and best practices.\",\n      features: [\n        \"Expert analysis\",\n        \"Market trends\",\n        \"Industry updates\"\n      ],\n      image: \"https://images.pexels.com/photos/4483602/pexels-photo-4483602.jpeg\"\n    }\n  ],\n  ar: [\n    {\n      icon: <ShoppingCart size={24} />,\n      title: \"البيع بالتجزئة (B2C)\",\n      description: \"تسوق من كتالوج منتجاتنا الواسع بأسعار تنافسية وخيارات توصيل سريعة.\",\n      features: [\n        \"اختيار منتجات متميزة\",\n        \"شحن عالمي سريع\",\n        \"معالجة آمنة للمدفوعات\"\n      ],\n      image: \"https://images.pexels.com/photos/3862130/pexels-photo-3862130.jpeg\"\n    },\n    {\n      icon: <Users size={24} />,\n      title: \"البيع بالجملة (B2B)\",\n      description: \"اطلب عروض أسعار للطلبات الكبيرة مع أسعار خاصة متاحة لعملاء الأعمال.\",\n      features: [\n        \"خصومات على الكميات\",\n        \"مدير حساب مخصص\",\n        \"شروط دفع مرنة\"\n      ],\n      image: \"https://images.pexels.com/photos/4483610/pexels-photo-4483610.jpeg\"\n    },\n    {\n      icon: <Package size={24} />,\n      title: \"خطوط الإنتاج\",\n      description: \"تصفح مجموعتنا من حلول التصنيع الجاهزة مع مواصفات مفصلة.\",\n      features: [\n        \"أنظمة آلية\",\n        \"تكامل مراقبة الجودة\",\n        \"دعم فني\"\n      ],\n      image: \"https://images.pexels.com/photos/2159235/pexels-photo-2159235.jpeg\"\n    },\n    {\n      icon: <Truck size={24} />,\n      title: \"خدمات الأعمال\",\n      description: \"خدمات الفحص والتخزين والشحن والشهادات والاستشارات لدعم عملياتك.\",\n      features: [\n        \"استشارات احترافية\",\n        \"فحوصات الجودة\",\n        \"حلول لوجستية\"\n      ],\n      image: \"https://images.pexels.com/photos/4483608/pexels-photo-4483608.jpeg\"\n    },\n    {\n      icon: <Tag size={24} />,\n      title: \"تصفية المبيعات\",\n      description: \"استفد من عروضنا المحدودة والصفقات الحصرية على الحلول الصناعية المتميزة.\",\n      features: [\n        \"خصومات حصرية\",\n        \"فرص للشراء بالجملة\",\n        \"شحن سريع\"\n      ],\n      image: \"https://images.pexels.com/photos/5668473/pexels-photo-5668473.jpeg\"\n    },\n    {\n      icon: <FileText size={24} />,\n      title: \"رؤى الصناعة\",\n      description: \"ابق على اطلاع من خلال مدونتنا التي تعرض أحدث الاتجاهات والنصائح وأفضل الممارسات.\",\n      features: [\n        \"تحليل الخبراء\",\n        \"اتجاهات السوق\",\n        \"تحديثات الصناعة\"\n      ],\n      image: \"https://images.pexels.com/photos/4483602/pexels-photo-4483602.jpeg\"\n    }\n  ]\n};\n\nfunction HomePage() {\n  const { locale } = useTranslation();\n  const { language, direction } = useLanguageStore(); // Get direction here\n  const { t } = useTranslation();\n\n  // استخدام البيانات المناسبة حسب اللغة المحددة\n  const currentLanguage = (locale as 'ar' | 'en') || language;\n  const heroImages = heroImagesData[currentLanguage];\n  const currentSolutions = solutionsData[currentLanguage];\n\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  const [hoveredSolution, setHoveredSolution] = useState<number | null>(null);\n  const [showWholesaleForm, setShowWholesaleForm] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);\n  const [showQuickView, setShowQuickView] = useState(false);\n  const [selectedClearanceItem, setSelectedClearanceItem] = useState<ClearanceItem | null>(null);\n  const [showClearanceQuoteForm, setShowClearanceQuoteForm] = useState(false);\n  const [selectedService, setSelectedService] = useState<Service | null>(null);\n\n  // فتح نافذة العرض السريع للمنتج\n  const handleQuickView = (product: Product) => {\n    setSelectedProduct(product);\n    setShowQuickView(true);\n  };\n\n  // فتح نموذج طلب عرض سعر للجملة\n  const handleWholesaleQuote = (product: Product) => {\n    setSelectedProduct(product);\n    setShowWholesaleForm(true);\n  };\n\n  // التحقق من تسجيل دخول المستخدم قبل إضافة المنتج إلى السلة أو المفضلة\n  const { openModal } = useAuthModalStore();\n  const { user } = useAuthStore();\n\n  const handleUnauthenticated = () => {\n    openModal('sign-in');\n  };\n\n  // إضافة المنتج إلى السلة - يمكن للمستخدم إضافة المنتج إلى السلة حتى لو لم يكن مسجل دخول\n  const handleAddToCart = (product: Product) => {\n    // التحقق من توفر المنتج في المخزون\n    if (!product.inStock && product.stock <= 0) {\n      // يمكن إضافة إشعار هنا بأن المنتج غير متوفر\n      console.warn(`المنتج ${product.name} غير متوفر في المخزون`);\n      return;\n    }\n\n    // تجهيز بيانات المنتج للإضافة إلى السلة\n    const cartItem = {\n      id: product.id,\n      name: product.name,\n      name_ar: product.name_ar || product.name,\n      price: product.price,\n      image: product.images && product.images.length > 0 ? product.images[0] : '/images/product-placeholder-light.svg',\n      quantity: 1,\n    };\n\n    // إضافة المنتج إلى السلة باستخدام getState للحصول على حالة المتجر\n    const cart = useCartStore.getState();\n    cart.addItem(cartItem, 1);\n\n    // إظهار إشعار بنجاح الإضافة (يمكن تحسين هذه الوظيفة لاحقاً بإضافة نظام إشعارات)\n    console.log(`تمت إضافة ${product.name} إلى السلة`);\n\n    // يمكن إضافة تحليلات هنا لتتبع سلوك المستخدم\n    // trackEvent('add_to_cart', { product_id: product.id, product_name: product.name });\n  };\n\n  // إضافة المنتج إلى المفضلة - لا يتطلب تسجيل الدخول\n  const handleAddToWishlist = (product: Product) => {\n    const wishlist = useWishlistStore.getState();\n\n    // التحقق مما إذا كان المنتج موجوداً بالفعل في المفضلة\n    if (wishlist.isInWishlist(product.id)) {\n      // إزالة المنتج من المفضلة\n      wishlist.removeItem(product.id);\n      console.log(`تمت إزالة ${product.name} من المفضلة`);\n    } else {\n      // إضافة المنتج إلى المفضلة\n      wishlist.addItem(product);\n      console.log(`تمت إضافة ${product.name} إلى المفضلة`);\n    }\n\n    // يمكن إضافة تحليلات هنا لتتبع سلوك المستخدم\n    // trackEvent('toggle_wishlist', { product_id: product.id, product_name: product.name });\n  };\n\n\n\n  // مقارنة المنتجات\n  const handleCompare = (product: Product) => {\n    // TODO: Implement product comparison\n    console.log('Compare product:', product.name);\n  };\n\n  // مشاركة المنتج\n  const handleShare = (product: Product) => {\n    // TODO: Implement product sharing\n    if (navigator.share) {\n      navigator.share({\n        title: product.name,\n        text: product.description,\n        url: `${window.location.origin}/${currentLanguage}/shop/${product.slug}`\n      });\n    } else {\n      // Fallback to copy to clipboard\n      navigator.clipboard.writeText(`${window.location.origin}/${currentLanguage}/shop/${product.slug}`);\n    }\n  };\n\n  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);\n  const [isLoadingProducts, setIsLoadingProducts] = useState(true);\n  const [showExitIntentPopup, setShowExitIntentPopup] = useState(false);\n\n  // تحميل المنتجات المميزة - محسن للتزامن مع صفحة المتجر\n  useEffect(() => {\n    const loadFeaturedProducts = async () => {\n      setIsLoadingProducts(true);\n      try {\n        // تحميل البيانات من نفس المصدر المستخدم في صفحة المتجر\n        const module = await import('../data/products');\n        const productsData = module.products || [];\n\n        // تصفية المنتجات المميزة فقط مع ضمان التزامن\n        const featuredProducts = productsData.filter(product => product.featured === true);\n\n        // ترتيب المنتجات المميزة حسب التقييم والمبيعات\n        const sortedFeatured = featuredProducts.sort((a, b) => {\n          // أولوية للمنتجات المتوفرة في المخزون\n          if (a.stock > 0 && b.stock <= 0) return -1;\n          if (a.stock <= 0 && b.stock > 0) return 1;\n\n          // ثم حسب التقييم\n          const ratingDiff = (b.rating || 0) - (a.rating || 0);\n          if (ratingDiff !== 0) return ratingDiff;\n\n          // ثم حسب عدد المبيعات\n          return (b.salesCount || 0) - (a.salesCount || 0);\n        });\n\n        // أخذ أول 4 منتجات مميزة للعرض في الصفحة الرئيسية\n        const productsToShow = sortedFeatured.slice(0, 4);\n\n        // التحقق من صحة البيانات وإضافة الحقول المطلوبة\n        const validatedProducts = productsToShow.map(product => ({\n          ...product,\n          // ضمان وجود صور المنتج\n          images: product.images && product.images.length > 0\n            ? product.images\n            : ['/images/product-placeholder-light.svg'],\n          // ضمان وجود السعر\n          price: typeof product.price === 'number' ? product.price : 0,\n          // ضمان وجود المخزون\n          stock: typeof product.stock === 'number' ? product.stock : 0,\n          // ضمان وجود التقييم\n          rating: typeof product.rating === 'number' ? product.rating : 0,\n          reviewCount: typeof product.reviewCount === 'number' ? product.reviewCount : 0,\n          // حساب حالة التوفر\n          inStock: typeof product.stock === 'number' ? product.stock > 0 : false,\n          // حساب نسبة الخصم\n          discount: product.compareAtPrice && product.compareAtPrice > product.price\n            ? Math.round((1 - product.price / product.compareAtPrice) * 100)\n            : 0,\n          // ضمان وجود الترجمة العربية\n          name_ar: product.name_ar || product.name,\n          description_ar: product.description_ar || product.description,\n          // ضمان وجود الحقول المطلوبة للمتجر\n          inventoryStatus: product.inventoryStatus || (product.stock > 0 ? 'in-stock' : 'out-of-stock'),\n          featured: true // تأكيد أن المنتج مميز\n        }));\n\n        setFeaturedProducts(validatedProducts);\n        setIsLoadingProducts(false);\n\n        // تسجيل معلومات للتطوير\n        console.log(`Loaded ${validatedProducts.length} featured products for home page (limit: 4)`);\n\n      } catch (error) {\n        console.error(\"Failed to load featured products:\", error);\n        setFeaturedProducts([]);\n        setIsLoadingProducts(false);\n      }\n    };\n\n    loadFeaturedProducts();\n  }, []);\n\n  // تحسين مدة الانتقال بين الصور وإضافة تأثيرات انتقالية أكثر سلاسة\n  useEffect(() => {\n    // زيادة مدة عرض كل صورة إلى 7 ثوانٍ لإعطاء المستخدم وقتًا كافيًا لقراءة المحتوى\n    const interval = setInterval(() => {\n      // تغيير الصورة مباشرة بدون تأثير انتقالي على الصفحة بأكملها\n      setCurrentImageIndex((prev) => (prev + 1) % heroImages.length);\n    }, 7000);\n\n    return () => clearInterval(interval);\n  }, [heroImages.length]);\n\n  // استخدام A/B Testing للصور البارزة\n  const { getVariant, trackConversion } = useABTesting();\n  const heroVariant = getVariant('heroImage');\n\n  // تتبع التحويل عند النقر على زر \"استكشف المنتجات\"\n  const handleExploreClick = () => {\n    trackConversion('heroImage');\n  };\n\n  // Exit-intent popup logic\n  useEffect(() => {\n    const handleMouseLeave = (event: MouseEvent) => {\n      if (event.clientY <= 0 && !sessionStorage.getItem('exitIntentShown')) {\n        setShowExitIntentPopup(true);\n        sessionStorage.setItem('exitIntentShown', 'true');\n      }\n    };\n\n    document.addEventListener('mouseleave', handleMouseLeave);\n    return () => {\n      document.removeEventListener('mouseleave', handleMouseLeave);\n    };\n  }, []);\n\n  // Adapter function to make ClearanceItem compatible with Product-expecting components\n  const clearanceProductAdapter = (item: ClearanceItem): Product => ({\n    id: item.id,\n    name: item.name,\n    slug: `/clearance/${item.id}`, // Generate a relevant slug\n    description: item.description,\n    price: item.clearancePrice,\n    compareAtPrice: item.originalPrice,\n    images: item.image ? [item.image] : [],\n    category: item.category,\n    tags: [item.category], // Simple tagging based on category\n    stock: item.availableQuantity,\n    featured: false, // Clearance items are generally not featured\n    specifications: {}, // No detailed specs for clearance items\n    createdAt: new Date().toISOString(),\n    // Optional fields from Product, can be undefined or have default values\n    reviews: [],\n    rating: 0,\n    reviewCount: 0,\n    relatedProducts: [],\n  });\n\n  return (\n    <div>\n      {/* نافذة منبثقة عند محاولة المغادرة */}\n      {showExitIntentPopup && featuredProducts.length > 0 && (\n        <ExitIntentPopup\n          onClose={() => setShowExitIntentPopup(false)}\n          product={featuredProducts[Math.floor(Math.random() * featuredProducts.length)]} // Use a random product from fetched list\n        />\n      )}\n\n      {/* Hero Section - تصميم محسن مع عناصر تفاعلية متقدمة */}\n      <section className=\"relative h-[90vh] overflow-hidden bg-gradient-to-br from-primary-900 via-primary-800 to-secondary-900 text-white\" dir={direction}>\n        {/* طبقة التراكب الأساسية مع تأثيرات محسنة */}\n        <div className=\"absolute inset-0 bg-gradient-to-br from-primary-900/90 via-black/50 to-secondary-900/80 z-10 mix-blend-multiply\" />\n\n        {/* تأثير الجسيمات المتحركة */}\n        <div className=\"absolute inset-0 z-5 opacity-30\">\n          <div className=\"absolute inset-0 overflow-hidden\">\n            <div className=\"particles-container absolute inset-0\">\n              {[...Array(20)].map((_, i) => (\n                <div\n                  key={`particle-${i}`}\n                  className={`absolute rounded-full bg-white/20 animate-pulse`}\n                  style={{\n                    width: `${Math.random() * 5 + 2}px`,\n                    height: `${Math.random() * 5 + 2}px`,\n                    top: `${Math.random() * 100}%`,\n                    left: `${Math.random() * 100}%`,\n                    animationDuration: `${Math.random() * 8 + 2}s`,\n                    animationDelay: `${Math.random() * 5}s`\n                  }}\n                />\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* أنماط هندسية متحركة في الخلفية */}\n        <div className=\"absolute inset-0 z-5 opacity-20\">\n          <div className=\"absolute top-0 left-0 w-full h-full\">\n            <svg className=\"w-full h-full\" viewBox=\"0 0 100 100\" preserveAspectRatio=\"none\">\n              <defs>\n                <linearGradient id=\"grid-gradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                  <stop offset=\"0%\" stopColor=\"var(--color-primary-300)\" stopOpacity=\"0.4\" />\n                  <stop offset=\"100%\" stopColor=\"var(--color-secondary-300)\" stopOpacity=\"0.4\" />\n                </linearGradient>\n                <pattern id=\"dots-pattern\" x=\"0\" y=\"0\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\">\n                  <circle cx=\"5\" cy=\"5\" r=\"0.5\" fill=\"rgba(255,255,255,0.2)\" />\n                </pattern>\n              </defs>\n              <path d=\"M0,0 L100,0 L100,100 L0,100 Z\" fill=\"url(#grid-gradient)\" />\n              <rect x=\"0\" y=\"0\" width=\"100\" height=\"100\" fill=\"url(#dots-pattern)\" />\n\n              {/* خطوط شبكية متحركة محسنة */}\n              {[...Array(12)].map((_, i) => (\n                <line\n                  key={`h-${i}`}\n                  x1=\"0\"\n                  y1={i * 8.33}\n                  x2=\"100\"\n                  y2={i * 8.33}\n                  stroke=\"rgba(255,255,255,0.15)\"\n                  strokeWidth=\"0.1\"\n                  strokeDasharray=\"0.5,3\"\n                />\n              ))}\n              {[...Array(12)].map((_, i) => (\n                <line\n                  key={`v-${i}`}\n                  x1={i * 8.33}\n                  y1=\"0\"\n                  x2={i * 8.33}\n                  y2=\"100\"\n                  stroke=\"rgba(255,255,255,0.15)\"\n                  strokeWidth=\"0.1\"\n                  strokeDasharray=\"0.5,3\"\n                />\n              ))}\n            </svg>\n          </div>\n        </div>\n\n        {/* صور الخلفية المتحركة - تحسين أبعاد الصورة */}\n        <div className=\"absolute inset-0 z-0\">\n          {heroImages.map((image, index) => (\n            <div\n              key={index}\n              className={`absolute inset-0 transition-all duration-2000 ${\n                index === currentImageIndex\n                  ? 'opacity-100 scale-100'\n                  : 'opacity-0 scale-110'\n              }`}\n            >\n              <div className=\"absolute inset-0 bg-gradient-to-t from-primary-900/80 via-primary-900/40 to-transparent z-10\" />\n              <EnhancedImage\n                src={image.url}\n                alt={image.alt}\n                fill={true}\n                objectFit=\"cover\"\n                progressive={true}\n                placeholder=\"shimmer\"\n                className=\"object-center filter brightness-[0.8] contrast-[1.1]\"\n                containerClassName=\"w-full h-full\"\n                sizes=\"(max-width: 768px) 100vw, 100vw\"\n                priority={index === 0}\n                quality={95}\n              />\n\n\n            </div>\n          ))}\n        </div>\n\n        {/* المحتوى الرئيسي - مركز مع تأثيرات محسنة */}\n        <div className=\"container-custom relative z-20 h-full flex items-center\">\n          <div className=\"flex flex-col items-center justify-center h-full py-16 w-full\">\n            {/* المحتوى النصي المركز */}\n            <div className=\"flex flex-col justify-center text-center w-full max-w-4xl mx-auto\">\n              {/* شعار الشركة المتحرك مع تأثيرات محسنة */}\n              <SmoothTransition type=\"fade\" duration={0.5} delay={0.1}>\n                <div className=\"flex items-center justify-center mb-8 relative\">\n                  <div className=\"absolute -inset-4 bg-gradient-to-r from-accent-500/0 via-accent-500/20 to-accent-500/0 rounded-full blur-xl opacity-70 animate-pulse\"></div>\n                  <div className=\"w-16 h-16 rounded-2xl bg-gradient-to-br from-accent-500/40 to-primary-500/40 backdrop-blur-md flex items-center justify-center shadow-lg shadow-primary-500/20 relative overflow-hidden group\">\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-accent-500/0 to-primary-500/0 group-hover:from-accent-500/20 group-hover:to-primary-500/20 transition-all duration-700\"></div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      width=\"32\"\n                      height=\"32\"\n                      viewBox=\"0 0 24 24\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      strokeWidth=\"2\"\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      className=\"text-accent-300 relative z-10\"\n                    >\n                      <path d=\"M12 2L2 7l10 5 10-5-10-5z\"></path>\n                      <path d=\"M2 17l10 5 10-5\"></path>\n                      <path d=\"M2 12l10 5 10-5\"></path>\n                    </svg>\n                  </div>\n                  <span className={`text-3xl font-bold ${currentLanguage === 'ar' ? 'mr-4' : 'ml-4'} bg-clip-text text-transparent bg-gradient-to-r from-accent-300 to-white relative`}>\n                    {currentLanguage === 'ar' ? 'ارتال' : 'ARTAL'}\n                    <span className=\"absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-accent-500/0 via-accent-500/80 to-accent-500/0\"></span>\n                  </span>\n                </div>\n              </SmoothTransition>\n\n              {/* العنوان الرئيسي مع تأثيرات محسنة */}\n              <SmoothTransition type=\"slide\" direction=\"up\" duration={0.7} delay={0.2}>\n                <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-extrabold mb-8 leading-tight\">\n                  <span className=\"bg-clip-text text-transparent bg-gradient-to-r from-white via-primary-100 to-white relative inline-block\">\n                    {currentLanguage === 'ar'\n                      ? 'حلول أعمال متكاملة'\n                      : 'Integrated Business Solutions'}\n                  </span>\n                  <br />\n                  <span className=\"bg-clip-text text-transparent bg-gradient-to-r from-accent-300 via-white to-accent-300 relative inline-block mt-2\">\n                    {currentLanguage === 'ar'\n                      ? 'من ارتال'\n                      : 'by ARTAL'}\n                    <div className=\"absolute -bottom-2 left-1/4 right-1/4 h-1 bg-gradient-to-r from-accent-500/0 via-accent-500 to-accent-500/0 rounded-full\"></div>\n                  </span>\n                </h1>\n              </SmoothTransition>\n\n              {/* العنوان الفرعي مع تأثيرات محسنة */}\n              <SmoothTransition type=\"slide\" direction=\"up\" duration={0.7} delay={0.3}>\n                <p className=\"text-xl md:text-2xl text-white/90 mb-10 max-w-3xl mx-auto leading-relaxed\">\n                  {currentLanguage === 'ar'\n                    ? 'نقدم حلولاً متكاملة للتجارة الإلكترونية والخدمات اللوجستية وخطوط الإنتاج لتعزيز نمو أعمالك'\n                    : 'We offer integrated solutions for e-commerce, logistics, and production lines to enhance your business growth'}\n                </p>\n              </SmoothTransition>\n\n              {/* أزرار الدعوة للعمل مع تأثيرات محسنة */}\n              <SmoothTransition type=\"slide\" direction=\"up\" duration={0.7} delay={0.5}>\n                <div className=\"flex flex-wrap gap-6 mb-16 justify-center\">\n                  <HeroButton\n                    href={`/${currentLanguage}/shop`}\n                    variant=\"accent\"\n                    className=\"flex items-center py-4 px-10 relative overflow-hidden shadow-xl hover:shadow-accent-500/30 group rounded-xl text-lg font-medium transition-all duration-300\"\n                    onClick={handleExploreClick}\n                  >\n                    <span className=\"absolute inset-0 bg-gradient-to-r from-accent-600 to-accent-500 z-0\"></span>\n                    <span className=\"absolute inset-0 bg-gradient-to-r from-accent-500 to-accent-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-0\"></span>\n                    <span className=\"relative z-10\">{currentLanguage === 'ar' ? 'استكشف حلولنا' : 'Explore Our Solutions'}</span>\n                    <ArrowRight className={`w-5 h-5 ${currentLanguage === 'ar' ? 'mr-3 rotate-180' : 'ml-3'} relative z-10 transition-transform duration-300 group-hover:translate-x-1`} />\n                  </HeroButton>\n                  <HeroButton\n                    href={`/${currentLanguage}/contact`}\n                    variant=\"outline\"\n                    className=\"py-4 px-10 relative overflow-hidden border-2 backdrop-blur-sm rounded-xl hover:bg-white/10 transition-all duration-300 text-lg font-medium group\"\n                  >\n                    <span className=\"absolute inset-0 bg-gradient-to-r from-white/0 to-white/0 group-hover:from-white/5 group-hover:to-white/10 transition-all duration-300 z-0\"></span>\n                    <span className=\"relative z-10\">{currentLanguage === 'ar' ? 'طلب استشارة مجانية' : 'Free Consultation'}</span>\n                  </HeroButton>\n                </div>\n              </SmoothTransition>\n            </div>\n          </div>\n        </div>\n\n        {/* مؤشرات الشرائح مع تصميم محسن */}\n        <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20\">\n          <div className=\"flex justify-center gap-4\">\n            {heroImages.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => setCurrentImageIndex(index)}\n                className={`relative h-3 rounded-full transition-all duration-500 shadow-lg overflow-hidden ${\n                  index === currentImageIndex\n                    ? 'bg-accent-500 w-12'\n                    : 'bg-white/40 hover:bg-white/60 w-3'\n                }`}\n                aria-label={`Go to slide ${index + 1}`}\n              >\n                {index === currentImageIndex && (\n                  <span className=\"absolute inset-0 bg-gradient-to-r from-accent-500/0 via-white/30 to-accent-500/0 animate-slide-right\"></span>\n                )}\n              </button>\n            ))}\n          </div>\n        </div>\n\n\n      </section>\n\n\n\n      {/* Our Solutions Section - تحسين قسم الحلول بشكل احترافي */}\n      <section id=\"solutions\" className=\"py-24 bg-gradient-to-b from-slate-50 via-white to-slate-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900\" dir={direction}>\n        <div className=\"container-custom\">\n          {/* عنوان القسم مع تأثيرات بصرية متقدمة */}\n          <div className=\"text-center mb-20 relative\">\n            {/* خط زخرفي علوي */}\n            <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-1.5 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full opacity-80\"></div>\n\n            {/* أيقونة القسم */}\n            <div className=\"inline-flex items-center justify-center w-16 h-16 mb-6 rounded-full bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400\">\n              <Settings className=\"w-8 h-8\" />\n            </div>\n\n            {/* العنوان الرئيسي */}\n            <h2 className=\"text-3xl md:text-5xl font-bold tracking-tight text-gray-900 dark:text-white mb-6 relative inline-block\">\n              <span className=\"bg-clip-text text-transparent bg-gradient-to-r from-primary-600 to-accent-600 dark:from-primary-400 dark:to-accent-400\">\n                {t('solutions.title')}\n              </span>\n              <div className=\"absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full\"></div>\n            </h2>\n\n            {/* العنوان الفرعي */}\n            <p className=\"mt-4 text-lg md:text-xl leading-8 text-slate-600 dark:text-slate-300 max-w-3xl mx-auto\">\n              {t('solutions.subtitle')}\n            </p>\n          </div>\n\n          {/* شبكة الحلول */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10\">\n            {currentSolutions.map((solution, index) => (\n              <div\n                key={index}\n                className=\"group relative overflow-hidden rounded-xl bg-white dark:bg-slate-800/90 shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2\"\n              >\n                {/* خط علوي متحرك */}\n                <div className=\"absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-primary-500 to-accent-500 transform origin-left transition-transform duration-500 scale-x-0 group-hover:scale-x-100\"></div>\n\n                {/* رأس البطاقة مع الأيقونة والعنوان */}\n                <div className=\"relative p-6 pb-0\">\n                  <div className=\"flex items-center mb-4\">\n                    <div className=\"p-3 bg-primary-50 dark:bg-primary-900/30 rounded-xl text-primary-600 dark:text-primary-400 shadow-sm\">\n                      {solution.icon}\n                    </div>\n                    <h3 className={`text-xl font-bold ${currentLanguage === 'ar' ? 'mr-4' : 'ml-4'} text-slate-900 dark:text-white`}>\n                      {solution.title}\n                    </h3>\n                  </div>\n\n                  {/* وصف الحل */}\n                  <p className=\"text-base text-slate-600 dark:text-slate-300 mb-6 leading-relaxed\">\n                    {solution.description}\n                  </p>\n                </div>\n\n                {/* محتوى الحل والميزات */}\n                <div className=\"p-6 pt-0\">\n                  {solution.features && solution.features.length > 0 && (\n                    <div className=\"mt-2\">\n                      <h4 className=\"text-sm font-semibold text-slate-800 dark:text-slate-200 mb-4 flex items-center\">\n                        <Star size={16} className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} text-accent-500`} />\n                        {t('solutions.features')}\n                      </h4>\n                      <ul className=\"space-y-3 text-sm text-slate-600 dark:text-slate-300\">\n                        {solution.features.map((feature, fIndex) => (\n                          <li key={fIndex} className=\"flex items-start\">\n                            <CheckCircle size={16} className={`flex-shrink-0 w-4 h-4 text-primary-500 dark:text-primary-400 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} mt-0.5`} />\n                            <span>{feature}</span>\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  )}\n\n                  {/* زر التفاصيل مع رابط صحيح */}\n                  <div className=\"mt-6 pt-4 border-t border-slate-100 dark:border-slate-700/50\">\n                    <Link\n                      href={`/${currentLanguage}/${solution.title === \"Retail (B2C)\" || solution.title === \"البيع بالتجزئة (B2C)\" ? 'shop' :\n                             solution.title === \"Wholesale (B2B)\" || solution.title === \"البيع بالجملة (B2B)\" ? 'wholesale' :\n                             solution.title === \"Production Lines\" || solution.title === \"خطوط الإنتاج\" ? 'production-lines' :\n                             solution.title === \"Business Services\" || solution.title === \"خدمات الأعمال\" ? 'services' :\n                             solution.title === \"Clearance Sales\" || solution.title === \"تصفية المبيعات\" ? 'clearance' :\n                             solution.title === \"Industry Insights\" || solution.title === \"رؤى الصناعة\" ? 'blog' : 'solutions'}`}\n                      className=\"flex items-center justify-between text-primary-600 dark:text-primary-400 font-medium hover:text-primary-700 dark:hover:text-primary-300 transition-colors group\"\n                      aria-label={`${currentLanguage === 'ar' ? 'اكتشف المزيد عن' : 'Discover more about'} ${solution.title}`}\n                    >\n                      <span>{currentLanguage === 'ar' ? 'اكتشف المزيد' : 'Discover More'}</span>\n                      <ArrowRight className={`w-4 h-4 transform transition-transform duration-300 group-hover:translate-x-1 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} />\n                    </Link>\n                  </div>\n                </div>\n\n                {/* خط سفلي متحرك */}\n                <div className=\"absolute bottom-0 left-0 w-full h-1.5 bg-gradient-to-r from-accent-500 to-primary-500 transform origin-right transition-transform duration-500 scale-x-0 group-hover:scale-x-100\"></div>\n              </div>\n            ))}\n          </div>\n\n\n        </div>\n      </section>\n\n      {/* Featured Products - قسم المنتجات المميزة */}\n      <section id=\"featured-products\" className=\"py-16 md:py-24 bg-gradient-to-b from-background via-background to-slate-50 dark:from-background dark:via-background dark:to-slate-900/20\" dir={direction}>\n        <div className=\"container-custom\">\n          {/* عنوان القسم مع تأثيرات بصرية */}\n          <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-12\">\n            <div className=\"relative\">\n              <div className={`absolute ${currentLanguage === 'ar' ? '-right-3' : '-left-3'} top-0 h-12 w-1 bg-gradient-to-b from-primary-500 to-primary-700 rounded-full hidden md:block`}></div>\n              <h2 className=\"text-3xl md:text-4xl font-bold mb-4 text-slate-900 dark:text-white\">\n                {currentLanguage === 'ar' ? 'المنتجات المميزة' : 'Featured Products'}\n              </h2>\n              <p className=\"text-lg text-slate-600 dark:text-slate-300 max-w-2xl\">\n                {currentLanguage === 'ar' ? 'اكتشف أحدث ابتكاراتنا وأفضل الحلول الصناعية مبيعاً' : 'Discover our latest innovations and best-selling industrial solutions'}\n              </p>\n            </div>\n            <div className=\"flex flex-wrap items-center gap-4 mt-6 md:mt-0\">\n              <Link href={`/${currentLanguage}/shop`}>\n                <Button\n                  variant=\"outline\"\n                  className=\"flex items-center gap-2 shadow-sm hover:shadow transition-shadow\"\n                  aria-label={currentLanguage === 'ar' ? 'عرض جميع المنتجات' : 'View all products'}\n                >\n                  {currentLanguage === 'ar' ? 'عرض جميع المنتجات' : 'View All Products'}\n                  <ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} />\n                </Button>\n              </Link>\n              <Link href={`/${currentLanguage}/shop?new=true`}>\n                <Button\n                  variant=\"primary\"\n                  className=\"flex items-center gap-2 shadow-sm hover:shadow-md transition-all duration-300 hover:scale-105\"\n                  aria-label={currentLanguage === 'ar' ? 'تصفح المنتجات الجديدة' : 'Browse new arrivals'}\n                >\n                  {currentLanguage === 'ar' ? 'واصل حديثاً' : 'New Arrivals'}\n                  <ArrowRight className={`w-4 h-4 transition-transform duration-300 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} />\n                </Button>\n              </Link>\n            </div>\n          </div>\n\n\n\n          {/* عرض المنتجات مع حالة التحميل */}\n          {isLoadingProducts ? (\n            // حالة التحميل - عرض بطاقات نبضية\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8\">\n              {[...Array(4)].map((_, index) => (\n                <Card key={index} className=\"flex flex-col h-full overflow-hidden bg-white dark:bg-slate-800 shadow-lg\">\n                  <div className=\"relative w-full aspect-square bg-slate-200 dark:bg-slate-700 animate-pulse\"></div>\n                  <div className=\"p-4 flex flex-col flex-grow\">\n                    <div className=\"h-6 bg-slate-200 dark:bg-slate-700 rounded mb-2 animate-pulse w-3/4\"></div>\n                    <div className=\"h-4 bg-slate-200 dark:bg-slate-700 rounded mb-4 animate-pulse w-1/2\"></div>\n                    <div className=\"h-8 bg-slate-200 dark:bg-slate-700 rounded animate-pulse w-full mt-auto\"></div>\n                  </div>\n                </Card>\n              ))}\n            </div>\n          ) : featuredProducts.length > 0 ? (\n            // حالة وجود منتجات مميزة - استخدام ProductGrid المحسن\n            <>\n              {/* Enhanced Featured Products Grid with Equal Heights */}\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n                {featuredProducts.map((product, index) => (\n                  <div key={product.id} className=\"flex h-full\">\n                    <div className=\"group relative overflow-hidden rounded-xl bg-white dark:bg-slate-800 shadow-lg hover:shadow-xl transition-all duration-300 flex flex-col h-full w-full\">\n                      {/* شارة المنتج المميز */}\n                      <div className=\"absolute top-3 left-3 z-10\">\n                        <span className=\"bg-amber-500 text-white text-xs font-bold px-2.5 py-1 rounded-full\">\n                          {currentLanguage === 'ar' ? 'مميز' : 'Featured'}\n                        </span>\n                      </div>\n\n                      {/* شارة الخصم (إذا كان هناك خصم) */}\n                      {product.compareAtPrice && product.compareAtPrice > product.price && (\n                        <div className=\"absolute top-3 right-3 z-10\">\n                          <span className=\"bg-red-500 text-white text-xs font-bold px-2.5 py-1 rounded-full\">\n                            {`${product.discount}% ${currentLanguage === 'ar' ? 'خصم' : 'OFF'}`}\n                          </span>\n                        </div>\n                      )}\n\n                      {/* صورة المنتج مع رابط للتفاصيل */}\n                      <Link href={`/${currentLanguage}/shop/product/${product.slug}`} className=\"block relative overflow-hidden\">\n                        <div className=\"relative w-full aspect-square overflow-hidden\">\n                          <EnhancedImage\n                            src={product.images && product.images.length > 0 ? product.images[0] : '/images/product-placeholder-light.svg'}\n                            alt={product.name}\n                            fill={true}\n                            objectFit=\"cover\"\n                            progressive={true}\n                            placeholder=\"shimmer\"\n                            className=\"transition-transform duration-500 group-hover:scale-105\"\n                            sizes=\"(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw\"\n                            priority={index < 4}\n                          />\n                        </div>\n\n                        {/* أزرار الإجراءات السريعة */}\n                        <div className=\"absolute top-12 right-3 flex flex-col gap-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                          {/* زر المفضلة */}\n                          <Button\n                            variant=\"icon\"\n                            size=\"sm\"\n                            className={`p-2 rounded-full shadow-md transform transition-transform duration-300 hover:scale-110 ${\n                              useWishlistStore.getState().isInWishlist(product.id)\n                                ? \"bg-primary-500 text-white\"\n                                : \"bg-white text-slate-700 dark:bg-slate-700 dark:text-white\"\n                            }`}\n                            onClick={(e) => {\n                              e.preventDefault();\n                              e.stopPropagation();\n                              handleAddToWishlist(product);\n                            }}\n                            aria-label={currentLanguage === 'ar' ? 'إضافة إلى المفضلة' : 'Add to wishlist'}\n                          >\n                            <Heart\n                              className={`h-5 w-5 ${useWishlistStore.getState().isInWishlist(product.id) && \"fill-current\"}`}\n                            />\n                          </Button>\n\n                          {/* زر العرض السريع */}\n                          <Button\n                            variant=\"icon\"\n                            size=\"sm\"\n                            className=\"p-2 rounded-full bg-white text-slate-700 shadow-md dark:bg-slate-700 dark:text-white transform transition-transform duration-300 hover:scale-110\"\n                            onClick={(e) => {\n                              e.preventDefault();\n                              e.stopPropagation();\n                              setSelectedProduct(product);\n                              setShowQuickView(true);\n                            }}\n                            aria-label={currentLanguage === 'ar' ? 'عرض سريع' : 'Quick view'}\n                          >\n                            <Eye className=\"h-5 w-5\" />\n                          </Button>\n                        </div>\n                      </Link>\n\n                      {/* معلومات المنتج */}\n                      <div className=\"p-4 flex flex-col flex-grow\">\n                        {/* التصنيف والتقييم */}\n                        <div className=\"flex items-center justify-between mb-2\">\n                          {product.category && (\n                            <span className=\"text-xs px-2 py-0.5 bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300 rounded-full\">\n                              {product.category}\n                            </span>\n                          )}\n\n                          <div className=\"flex items-center text-sm text-slate-500 dark:text-slate-400\">\n                            <Star className={`h-4 w-4 text-yellow-400 ${currentLanguage === 'ar' ? 'ml-1' : 'mr-1'}`} />\n                            <span>\n                              {product.rating?.toFixed(1) ?? 'N/A'}\n                              {product.reviewCount ? `(${product.reviewCount})` : ''}\n                            </span>\n                          </div>\n                        </div>\n\n                        {/* اسم المنتج */}\n                        <Link href={`/${currentLanguage}/shop/product/${product.slug}`} className=\"block\">\n                          <h3 className=\"text-lg font-semibold text-slate-900 dark:text-white mb-1 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 line-clamp-1\">\n                            {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}\n                          </h3>\n                        </Link>\n\n                        {/* وصف المنتج */}\n                        <p className=\"text-slate-600 dark:text-slate-300 mb-4 line-clamp-2 text-sm\">\n                          {currentLanguage === 'ar'\n                            ? (product.description_ar || product.description)\n                            : product.description}\n                        </p>\n\n                        {/* السعر والمخزون */}\n                        <div className=\"flex items-center justify-between mb-3 mt-auto\">\n                          <div className=\"flex items-baseline gap-1\">\n                            <span className=\"text-lg font-bold text-slate-900 dark:text-white\">\n                              {formatCurrency(product.price)}\n                            </span>\n                            {product.compareAtPrice && product.compareAtPrice > product.price && (\n                              <span className=\"text-sm text-slate-500 line-through\">\n                                {formatCurrency(product.compareAtPrice)}\n                              </span>\n                            )}\n                          </div>\n\n                          <div className=\"text-xs font-medium\">\n                            {product.stock > 0 ? (\n                              <span className=\"text-green-600 dark:text-green-400\">\n                                {currentLanguage === 'ar' ? 'متوفر' : 'In Stock'}\n                              </span>\n                            ) : (\n                              <span className=\"text-red-600 dark:text-red-400\">\n                                {currentLanguage === 'ar' ? 'نفذ المخزون' : 'Out of Stock'}\n                              </span>\n                            )}\n                          </div>\n                        </div>\n\n                        {/* أزرار الإجراءات */}\n                        <div className=\"flex gap-2\">\n                          {/* زر إضافة إلى السلة */}\n                          <Button\n                            variant=\"primary\"\n                            size=\"sm\"\n                            className=\"flex-1 rounded-md\"\n                            onClick={(e) => {\n                              e.preventDefault();\n                              e.stopPropagation();\n                              handleAddToCart(product);\n                            }}\n                            disabled={product.stock <= 0}\n                            aria-label={currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to cart'}\n                          >\n                            <ShoppingCart className={`h-4 w-4 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}`} />\n                            <span>{currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}</span>\n                          </Button>\n\n                          {/* زر طلب عرض سعر للجملة */}\n                          <Button\n                            variant=\"outline\"\n                            size=\"sm\"\n                            className=\"rounded-md flex items-center gap-1\"\n                            onClick={(e) => {\n                              e.preventDefault();\n                              e.stopPropagation();\n                              setSelectedProduct(product);\n                              setShowWholesaleForm(true);\n                            }}\n                            aria-label={currentLanguage === 'ar' ? 'طلب عرض سعر جملة' : 'Request wholesale quote'}\n                          >\n                            <Users className=\"h-4 w-4\" />\n                            <span className=\"hidden sm:inline\">{currentLanguage === 'ar' ? 'جملة' : 'B2B'}</span>\n                          </Button>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n\n\n              {/* نافذة العرض السريع للمنتج */}\n              {showQuickView && selectedProduct && (\n                <QuickView\n                  product={selectedProduct}\n                  onClose={() => {\n                    setShowQuickView(false);\n                    setSelectedProduct(null);\n                  }}\n                  onAddToCart={handleAddToCart}\n                  onToggleWishlist={handleAddToWishlist}\n                />\n              )}\n\n              {/* نموذج طلب عرض سعر للجملة */}\n              <WholesaleQuoteForm\n                isOpen={showWholesaleForm && !!selectedProduct}\n                onClose={() => {\n                  setShowWholesaleForm(false);\n                  setSelectedProduct(null);\n                }}\n                isCustomProduct={false}\n                selectedProduct={selectedProduct}\n              />\n            </>\n          ) : (\n            // حالة عدم وجود منتجات مميزة\n            <div className=\"text-center py-12 bg-slate-50 dark:bg-slate-800/30 rounded-lg\">\n              <Package className=\"w-12 h-12 mx-auto text-slate-400 dark:text-slate-500 mb-4\" />\n              <p className=\"text-lg font-medium text-slate-700 dark:text-slate-300 mb-2\">\n                {currentLanguage === 'ar' ? 'لا توجد منتجات مميزة حالياً' : 'No featured products available'}\n              </p>\n              <p className=\"text-slate-600 dark:text-slate-400 max-w-md mx-auto mb-6\">\n                {currentLanguage === 'ar'\n                  ? 'لم يتم العثور على منتجات مميزة. يرجى التحقق مرة أخرى لاحقاً أو تصفح جميع المنتجات.'\n                  : 'No featured products found. Please check back later or browse all products.'}\n              </p>\n              <Link href={`/${currentLanguage}/shop`}>\n                <Button variant=\"primary\">\n                  {currentLanguage === 'ar' ? 'تصفح جميع المنتجات' : 'Browse all products'}\n                </Button>\n              </Link>\n            </div>\n          )}\n\n          {/* زر عرض جميع المنتجات */}\n          {featuredProducts.length > 0 && (\n            <div className=\"mt-12 text-center\">\n              <Link href={`/${currentLanguage}/shop?featured=true`}>\n                <Button\n                  variant=\"outline\"\n                  size=\"lg\"\n                  className=\"px-8 shadow-sm hover:shadow transition-shadow group\"\n                  aria-label={currentLanguage === 'ar' ? 'عرض جميع المنتجات المميزة في المتجر' : 'View all featured products in shop'}\n                >\n                  {currentLanguage === 'ar' ? 'عرض جميع المنتجات المميزة' : 'View All Featured Products'}\n                  <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-5 w-5 transition-transform duration-300 group-hover:translate-x-1`} />\n                </Button>\n              </Link>\n            </div>\n          )}\n        </div>\n      </section>\n\n\n\n      {/* أحدث خطوط الإنتاج - Latest Production Lines */}\n      <section className=\"py-16 md:py-24 bg-gradient-to-b from-slate-50 to-white dark:from-slate-900 dark:to-slate-800\" dir={direction}>\n        <div className=\"container-custom\">\n          {/* عنوان القسم مع تأثيرات بصرية متقدمة */}\n          <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-12\">\n            <div className=\"relative\">\n              {/* شارة القسم */}\n              <div className=\"inline-flex items-center justify-center px-6 py-2.5 rounded-full bg-gradient-to-r from-primary-100 to-accent-100 dark:from-primary-900/50 dark:to-accent-900/50 text-primary-600 dark:text-primary-300 text-sm font-medium mb-4 shadow-sm\">\n                <Factory className=\"w-4 h-4 mr-2 text-accent-500\" />\n                {currentLanguage === 'ar' ? 'تقنيات متطورة' : 'Advanced Technology'}\n              </div>\n\n              <h2 className=\"text-3xl md:text-4xl font-bold mb-4 text-slate-900 dark:text-white\">\n                {currentLanguage === 'ar' ? 'الآلات الصناعية المميزة' : 'Featured Industrial Machinery'}\n              </h2>\n              <p className=\"text-lg text-slate-600 dark:text-slate-300 max-w-2xl\">\n                {currentLanguage === 'ar'\n                  ? 'استكشف أحدث الآلات والمعدات الصناعية المتطورة لتعزيز كفاءة عملياتك الصناعية وزيادة الإنتاجية.'\n                  : 'Explore our cutting-edge industrial machinery and equipment to enhance your operations and increase productivity.'}\n              </p>\n            </div>\n            <div className=\"flex flex-wrap items-center gap-4 mt-6 md:mt-0\">\n              <Link href={`/${currentLanguage}/machinery`}>\n                <Button\n                  variant=\"outline\"\n                  className=\"flex items-center gap-2 shadow-sm hover:shadow transition-shadow\"\n                >\n                  {currentLanguage === 'ar' ? 'عرض جميع الآلات' : 'View All Machinery'}\n                  <ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} />\n                </Button>\n              </Link>\n              <Link href={`/${currentLanguage}/contact?subject=machinery-inquiry`}>\n                <Button\n                  variant=\"primary\"\n                  className=\"flex items-center gap-2 shadow-sm hover:shadow-md transition-shadow\"\n                >\n                  {currentLanguage === 'ar' ? 'طلب آلة مخصصة' : 'Request Custom Machinery'}\n                  <ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} />\n                </Button>\n              </Link>\n            </div>\n          </div>\n\n          {/* عرض الآلات الصناعية المميزة */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {machineryData.filter(machine => machine.featured).slice(0, 3).map((machine) => (\n              <div key={machine.id} className=\"group relative overflow-hidden bg-white dark:bg-slate-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex flex-col h-full\">\n                {/* شارة الفئة */}\n                <div className=\"absolute top-4 left-4 z-10 bg-primary-500/90 text-white px-3 py-1 rounded-full text-sm font-medium backdrop-blur-sm\">\n                  {currentLanguage === 'ar' ? machine.category_ar : machine.category}\n                </div>\n\n                {/* شارة B2B */}\n                <div className=\"absolute top-4 right-4 z-10 bg-accent-500/90 text-white px-3 py-1 rounded-full text-sm font-medium backdrop-blur-sm\">\n                  B2B\n                </div>\n\n                {/* صورة الآلة */}\n                <div className=\"relative aspect-video overflow-hidden\">\n                  <EnhancedImage\n                    src={machine.images[0]}\n                    alt={currentLanguage === 'ar' ? machine.name_ar : machine.name}\n                    fill={true}\n                    objectFit=\"cover\"\n                    progressive={true}\n                    placeholder=\"shimmer\"\n                    className=\"transition-transform duration-500 group-hover:scale-105\"\n                    containerClassName=\"w-full h-full\"\n                    sizes=\"(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0\" />\n                </div>\n\n                <div className=\"p-6 flex-grow flex flex-col\">\n                  {/* اسم الآلة */}\n                  <Link href={`/${currentLanguage}/machinery/${machine.slug}`} className=\"block\">\n                    <h3 className=\"text-xl font-semibold text-slate-900 dark:text-white mb-3 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors\">\n                      {currentLanguage === 'ar' ? machine.name_ar : machine.name}\n                    </h3>\n                  </Link>\n\n                  {/* وصف الآلة */}\n                  <p className=\"text-slate-600 dark:text-slate-300 mb-4 line-clamp-2\">\n                    {currentLanguage === 'ar' ? machine.description_ar : machine.description}\n                  </p>\n\n                  {/* معلومات B2B */}\n                  <div className=\"flex items-center text-sm text-slate-700 dark:text-slate-300 mb-4\">\n                    <Package className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} flex-shrink-0 w-4 h-4 text-primary-500`} />\n                    <span>\n                      {currentLanguage === 'ar' ? 'اتصل للحصول على عرض سعر مخصص' : 'Contact for custom quote'}\n                    </span>\n                  </div>\n\n                  {/* المميزات الرئيسية */}\n                  <div className=\"space-y-2 mb-6\">\n                    {(currentLanguage === 'ar' ? machine.features_ar : machine.features).slice(0, 3).map((feature, index) => (\n                      <div key={index} className=\"flex items-start text-sm\">\n                        <div className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} mt-0.5 text-accent-500`}>\n                          <CheckCircle className=\"w-4 h-4\" />\n                        </div>\n                        <span className=\"text-slate-600 dark:text-slate-300\">\n                          {feature}\n                        </span>\n                      </div>\n                    ))}\n                  </div>\n\n                  {/* أزرار الإجراءات */}\n                  <div className=\"mt-auto space-y-3\">\n                    <Link href={`/${currentLanguage}/machinery/${machine.slug}`} className=\"block w-full\">\n                      <Button\n                        variant=\"outline\"\n                        className=\"w-full flex items-center justify-center gap-2 group\"\n                      >\n                        <span>{currentLanguage === 'ar' ? 'عرض التفاصيل' : 'View Details'}</span>\n                        <ArrowRight className={`w-4 h-4 transition-transform duration-300 group-hover:translate-x-1 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} />\n                      </Button>\n                    </Link>\n                    <Link href={`/${currentLanguage}/machinery/${machine.slug}?action=quote`} className=\"block w-full\">\n                      <Button\n                        variant=\"primary\"\n                        className=\"w-full flex items-center justify-center gap-2\"\n                      >\n                        {currentLanguage === 'ar' ? 'طلب عرض سعر' : 'Request Quote'}\n                      </Button>\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* زر عرض جميع الآلات */}\n          <div className=\"mt-12 text-center\">\n            <Link href={`/${currentLanguage}/machinery`}>\n              <Button\n                variant=\"outline\"\n                size=\"lg\"\n                className=\"px-8 shadow-sm hover:shadow transition-shadow group\"\n              >\n                {currentLanguage === 'ar' ? 'استكشاف جميع الآلات الصناعية' : 'Explore All Industrial Machinery'}\n                <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-5 w-5 transition-transform duration-300 group-hover:translate-x-1`} />\n              </Button>\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* تصفية المخزون وأفضل العروض */}\n      <section className=\"py-16 md:py-24 bg-gradient-to-b from-background to-primary-50\" dir={direction}>\n        <div className=\"container-custom\">\n          <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-12\">\n            <div>\n              <h2 className=\"text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4\">\n                {language === 'ar' ? 'تصفية المخزون بأسعار زهيدة' : 'Clearance Stock at Low Prices'}\n              </h2>\n              <p className=\"text-lg text-slate-600 dark:text-slate-300 max-w-2xl\">\n                {language === 'ar'\n                  ? 'فرصة ذهبية للحصول على منتجات بكميات كبيرة وبأسعار مخفضة. مثالية للشركات التي تبحث عن توفير التكاليف.'\n                  : 'Golden opportunity to get products in bulk quantities at discounted prices. Perfect for businesses looking to save costs.'}\n              </p>\n            </div>\n            <div className=\"flex items-center gap-4 mt-6 md:mt-0\">\n              <Link href={`/${currentLanguage}/clearance`}>\n                <Button\n                  variant=\"primary\"\n                  className=\"flex items-center gap-2\"\n                >\n                  {currentLanguage === 'ar' ? 'عرض جميع منتجات التصفية' : 'View All Clearance Items'}\n                  <ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} />\n                </Button>\n              </Link>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {clearanceItems.slice(0, 3).map((item: ClearanceItem) => (\n              <div key={item.id} className=\"group relative overflow-hidden bg-white dark:bg-slate-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\">\n                {/* شارة الخصم */}\n                <div className=\"absolute top-4 left-4 z-10 bg-error-500 text-white px-3 py-1 rounded-full text-sm font-bold\">\n                  {currentLanguage === 'ar'\n                    ? `خصم ${Math.round((1 - item.clearancePrice / item.originalPrice) * 100)}%`\n                    : `${Math.round((1 - item.clearancePrice / item.originalPrice) * 100)}% OFF`}\n                </div>\n\n                {/* شارة تصفية المخزون */}\n                <div className=\"absolute top-4 right-4 z-10 bg-amber-500 text-white px-3 py-1 rounded-full text-sm font-bold\">\n                  {currentLanguage === 'ar' ? 'تصفية' : 'Clearance'}\n                </div>\n\n                {/* صورة المنتج */}\n                <div className=\"relative aspect-video overflow-hidden\">\n                  <EnhancedImage\n                    src={item.image}\n                    alt={item.name}\n                    fill={true}\n                    objectFit=\"cover\"\n                    progressive={true}\n                    placeholder=\"shimmer\"\n                    className=\"transition-transform duration-500 group-hover:scale-105\"\n                    containerClassName=\"w-full h-full\"\n                    sizes=\"(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0\" />\n                </div>\n\n                <div className=\"p-6\">\n                  {/* تصنيف المنتج وحالته */}\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <span className=\"text-xs font-medium px-2.5 py-0.5 rounded-full bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300\">\n                      {item.category}\n                    </span>\n                    <span className={`text-xs font-medium px-2.5 py-0.5 rounded-full ${\n                      item.condition === 'new' ? 'bg-success-50 dark:bg-success-900/30 text-success-700 dark:text-success-300' :\n                      item.condition === 'like-new' ? 'bg-info-50 dark:bg-info-900/30 text-info-700 dark:text-info-300' :\n                      'bg-warning-50 dark:bg-warning-900/30 text-warning-700 dark:text-warning-300'\n                    }`}>\n                      {currentLanguage === 'ar'\n                        ? (item.condition === 'new' ? 'جديد' : item.condition === 'like-new' ? 'كالجديد' : 'مستعمل')\n                        : item.condition}\n                    </span>\n                  </div>\n\n                  {/* اسم المنتج */}\n                  <Link\n                    href={`/${currentLanguage}/clearance/${item.id}`}\n                    className=\"block\"\n                  >\n                    <h3 className=\"text-xl font-semibold text-slate-900 dark:text-white mb-2 line-clamp-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors\">\n                      {item.name}\n                    </h3>\n                  </Link>\n\n                  {/* وصف المنتج */}\n                  <p className=\"text-slate-600 dark:text-slate-300 text-sm mb-4 line-clamp-2\">\n                    {item.description}\n                  </p>\n\n                  {/* معلومات إضافية */}\n                  <div className=\"space-y-2 mb-4\">\n                    <div className=\"flex items-center text-sm text-slate-600 dark:text-slate-400\">\n                      <Package size={16} className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} flex-shrink-0`} />\n                      {currentLanguage === 'ar'\n                        ? `الحد الأدنى للطلب: ${item.minOrder} وحدة`\n                        : `Min. Order: ${item.minOrder} units`}\n                    </div>\n                    <div className=\"flex items-center text-sm text-slate-600 dark:text-slate-400\">\n                      <MapPin size={16} className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} flex-shrink-0`} />\n                      {item.location}\n                    </div>\n                  </div>\n\n                  {/* السعر والكمية المتوفرة */}\n                  <div className=\"flex items-end justify-between mb-4\">\n                    <div>\n                      <div className=\"text-2xl font-bold text-slate-900 dark:text-white\">\n                        {formatCurrency(item.clearancePrice)}\n                      </div>\n                      <div className=\"text-sm text-slate-500 dark:text-slate-400 line-through\">\n                        {formatCurrency(item.originalPrice)}\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-xs text-slate-600 dark:text-slate-400\">\n                        {currentLanguage === 'ar' ? 'الكمية المتوفرة:' : 'Available:'}\n                      </div>\n                      <div className=\"font-medium text-slate-900 dark:text-white\">\n                        {currentLanguage === 'ar'\n                          ? `${item.availableQuantity} وحدة`\n                          : `${item.availableQuantity} units`}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* أزرار الإجراءات */}\n                  <div className=\"space-y-2\">\n                    <Button\n                      variant=\"primary\"\n                      className=\"w-full flex items-center justify-center gap-2\"\n                      onClick={(e) => {\n                        e.preventDefault();\n                        setSelectedClearanceItem(item);\n                        setShowClearanceQuoteForm(true);\n                      }}\n                    >\n                      <Send size={16} />\n                      {currentLanguage === 'ar' ? 'طلب عرض سعر للكمية' : 'Request Bulk Quote'}\n                    </Button>\n                    <Link\n                      href={`/${currentLanguage}/clearance/${item.id}`}\n                      className=\"block text-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors\"\n                    >\n                      {currentLanguage === 'ar' ? 'عرض التفاصيل الكاملة' : 'View Full Details'}\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          <div className=\"mt-12 text-center\">\n            <Link href={`/${currentLanguage}/clearance`}>\n              <Button\n                variant=\"outline\"\n                size=\"lg\"\n                className=\"px-8 shadow-sm hover:shadow transition-shadow group\"\n              >\n                {currentLanguage === 'ar' ? 'عرض جميع منتجات التصفية' : 'View All Clearance Items'}\n                <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-5 w-5 transition-transform duration-300 group-hover:translate-x-1`} />\n              </Button>\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* نموذج طلب عرض سعر لمنتجات التصفية */}\n      <WholesaleQuoteForm\n        isOpen={showClearanceQuoteForm && !!selectedClearanceItem}\n        onClose={() => {\n          setShowClearanceQuoteForm(false);\n          setSelectedClearanceItem(null);\n        }}\n        isCustomProduct={false}\n        product={selectedClearanceItem ? clearanceProductAdapter(selectedClearanceItem) : undefined}\n      />\n\n      {/* Most Requested Services - قسم الخدمات الأكثر طلباً */}\n      <section className=\"py-20 md:py-28 bg-gradient-to-b from-white via-primary-50/30 to-primary-50 dark:from-slate-900 dark:via-slate-900/80 dark:to-primary-900/20\" dir={direction}>\n        <div className=\"container-custom\">\n          {/* عنوان القسم مع تأثيرات بصرية متقدمة */}\n          <div className=\"text-center mb-20 relative\">\n            {/* شارة القسم */}\n            <div className=\"inline-flex items-center justify-center px-6 py-2.5 rounded-full bg-gradient-to-r from-primary-100 to-accent-100 dark:from-primary-900/50 dark:to-accent-900/50 text-primary-600 dark:text-primary-300 text-sm font-medium mb-6 shadow-sm\">\n              <Star className=\"w-4 h-4 mr-2 text-accent-500\" />\n              {language === 'ar' ? 'خدمات متميزة' : 'Premium Services'}\n            </div>\n\n            {/* العنوان الرئيسي مع تأثير التدرج */}\n            <h2 className=\"text-3xl md:text-5xl font-bold mb-6 leading-tight relative inline-block\">\n              <span className=\"bg-clip-text text-transparent bg-gradient-to-r from-primary-600 to-accent-600 dark:from-primary-400 dark:to-accent-400\">\n                {language === 'ar' ? 'الخدمات الأكثر طلباً' : 'Most Requested Services'}\n              </span>\n              <div className=\"absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full transform scale-x-100\"></div>\n            </h2>\n\n            {/* الوصف مع تحسين الخط والمساحة */}\n            <p className=\"text-lg md:text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto leading-relaxed\">\n              {language === 'ar' ? 'اكتشف مجموعتنا الشاملة من خدمات الأعمال المصممة خصيصاً لتحسين عملياتك وتعزيز النمو وتحقيق النجاح المستدام.' : 'Discover our comprehensive range of business services tailored to optimize your operations, drive growth, and achieve sustainable success.'}\n            </p>\n\n            {/* زخرفة خلفية */}\n            <div className=\"absolute -top-10 left-1/2 transform -translate-x-1/2 w-64 h-64 bg-primary-500/5 dark:bg-primary-500/10 rounded-full blur-3xl -z-10\"></div>\n          </div>\n\n          {/* شبكة بطاقات الخدمات مع تباعد محسن */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10\">\n            {services.slice(0, 6).map((service: Service, index: number) => {\n              const Icon = service.icon === 'Search' ? Search :\n                          service.icon === 'Package' ? Package :\n                          service.icon === 'Truck' ? Truck :\n                          service.icon === 'FileCheck' ? FileCheck :\n                          service.icon === 'Users' ? Users :\n                          service.icon === 'ClipboardList' ? FileText :\n                          Building2;\n\n              return (\n                <div\n                  key={service.id}\n                >\n                  <div className=\"group h-full perspective-1000\">\n                    <Card className=\"relative overflow-hidden h-full hover:shadow-xl transition-all duration-500 border border-slate-200/80 dark:border-slate-700/50 hover:border-primary-200 dark:hover:border-primary-800/70 flex flex-col transform-gpu group-hover:translate-y-[-8px] group-hover:rotate-y-1\">\n                      {/* حدود متدرجة متحركة */}\n                      <div className=\"absolute inset-0 rounded-xl border-2 border-transparent bg-gradient-to-br from-primary-500/0 via-primary-500/0 to-accent-500/0 group-hover:from-primary-500/20 group-hover:via-primary-500/10 group-hover:to-accent-500/20 transition-colors duration-700 -z-10\"></div>\n\n                      {/* شريط علوي متدرج */}\n                      <div className=\"absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-primary-500 to-accent-500 transform origin-left transition-transform duration-500 scale-x-0 group-hover:scale-x-100\"></div>\n\n                      {/* شارة الخدمة */}\n                      <div className=\"absolute top-6 right-6 bg-primary-50/90 dark:bg-primary-900/50 text-primary-600 dark:text-primary-300 px-3 py-1 rounded-full text-xs font-medium backdrop-blur-sm shadow-sm\">\n                        {currentLanguage === 'ar' ? 'خدمة احترافية' : 'Professional Service'}\n                      </div>\n\n                      <div className=\"p-8 flex-grow flex flex-col\">\n                        {/* أيقونة الخدمة مع خلفية متحركة */}\n                        <div className=\"relative mb-8 group-hover:scale-105 transition-transform duration-500\">\n                          <div className=\"w-20 h-20 bg-gradient-to-br from-primary-100 to-primary-50 dark:from-primary-900/60 dark:to-primary-800/30 rounded-2xl flex items-center justify-center text-primary-600 dark:text-primary-400 transform transition-all duration-500 group-hover:shadow-lg group-hover:shadow-primary-500/10\">\n                            <Icon size={36} className=\"transform transition-transform duration-700 group-hover:rotate-12\" />\n                          </div>\n                          <div className=\"absolute -inset-2 bg-primary-200 dark:bg-primary-800/50 rounded-3xl -z-10 blur-xl opacity-0 group-hover:opacity-40 transition-opacity duration-700\"></div>\n                        </div>\n\n                        {/* عنوان الخدمة */}\n                        <h3 className=\"text-2xl font-bold mb-4 text-slate-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors\">\n                          {currentLanguage === 'ar' ? service.name_ar || service.name : service.name}\n                        </h3>\n\n                        {/* وصف الخدمة */}\n                        <p className=\"text-slate-600 dark:text-slate-300 mb-6 line-clamp-3 leading-relaxed\">\n                          {currentLanguage === 'ar' ? service.description_ar || service.description : service.description}\n                        </p>\n\n                        {/* الميزات الرئيسية مع تحسين العرض */}\n                        <div className=\"space-y-3 mb-8 flex-grow\">\n                          {(currentLanguage === 'ar' ? service.features_ar || service.features : service.features).slice(0, 4).map((feature: string, fIndex: number) => (\n                            <div key={fIndex} className=\"flex items-start text-sm group/feature\">\n                              <div className=\"mt-0.5 transform transition-transform duration-300 group-hover/feature:scale-110\">\n                                <CheckCircle className={`w-5 h-5 text-accent-500 dark:text-accent-400 ${currentLanguage === 'ar' ? 'ml-3' : 'mr-3'} flex-shrink-0`} />\n                              </div>\n                              <span className=\"text-slate-700 dark:text-slate-300 group-hover/feature:text-primary-700 dark:group-hover/feature:text-primary-300 transition-colors\">{feature}</span>\n                            </div>\n                          ))}\n                        </div>\n\n                        {/* أزرار الإجراءات مع تحسين التفاعل */}\n                        <div className=\"mt-auto\">\n                          <Link href={`/${currentLanguage}/services/${service.slug}`} className=\"block w-full\">\n                            <Button\n                              variant=\"primary\"\n                              className=\"w-full flex items-center justify-center gap-2 group-hover:shadow-md transition-all duration-300 group-hover:shadow-primary-500/20 py-3\"\n                            >\n                              {currentLanguage === 'ar' ? 'احجز الخدمة الآن' : 'Book This Service'}\n                              <ArrowRight className={`w-4 h-4 transition-transform duration-300 group-hover:translate-x-1 ${currentLanguage === 'ar' ? 'rotate-rtl' : ''}`} />\n                            </Button>\n                          </Link>\n                        </div>\n                      </div>\n\n                      {/* شريط سفلي متدرج */}\n                      <div className=\"absolute bottom-0 left-0 w-full h-1.5 bg-gradient-to-r from-accent-500 to-primary-500 transform origin-right transition-transform duration-500 scale-x-0 group-hover:scale-x-100\"></div>\n                    </Card>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n\n          {/* زر عرض جميع الخدمات مع تحسين التصميم */}\n          <div className=\"mt-20 text-center\">\n            <Link href={`/${currentLanguage}/services`}>\n              <Button\n                variant=\"outline\"\n                size=\"lg\"\n                className=\"px-10 py-6 text-lg border-2 hover:border-primary-500 dark:hover:border-primary-400 group shadow-sm hover:shadow-md transition-shadow\"\n              >\n                {currentLanguage === 'ar' ? 'استكشاف جميع خدماتنا الاحترافية' : 'Explore All Our Professional Services'}\n                <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-3' : 'ml-3'} h-5 w-5 transition-transform duration-300 group-hover:translate-x-2 ${currentLanguage === 'ar' ? 'rotate-rtl' : ''}`} />\n              </Button>\n            </Link>\n          </div>\n\n\n        </div>\n      </section>\n\n      {/* CTA Section - قسم الدعوة للعمل المحسن */}\n      <section className=\"relative py-12 md:py-16 overflow-hidden\" dir={direction}>\n        {/* خلفية متدرجة مع تأثيرات */}\n        <div className=\"absolute inset-0 bg-gradient-to-br from-primary-800 via-primary-700 to-secondary-800 z-0\"></div>\n\n        {/* طبقة التراكب مع تأثير الشبكة */}\n        <div className=\"absolute inset-0 bg-black/20 z-10\"></div>\n        <div className=\"absolute inset-0 z-10 opacity-10\">\n          <div className=\"absolute top-0 left-0 w-full h-full\">\n            <svg className=\"w-full h-full\" viewBox=\"0 0 100 100\" preserveAspectRatio=\"none\">\n              <defs>\n                <pattern id=\"cta-grid\" x=\"0\" y=\"0\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\">\n                  <path d=\"M10 0H0V10\" fill=\"none\" stroke=\"rgba(255,255,255,0.3)\" strokeWidth=\"0.5\" />\n                </pattern>\n              </defs>\n              <rect x=\"0\" y=\"0\" width=\"100\" height=\"100\" fill=\"url(#cta-grid)\" />\n            </svg>\n          </div>\n        </div>\n\n        {/* المحتوى الرئيسي */}\n        <div className=\"container-custom relative z-20\">\n          <div className=\"max-w-4xl mx-auto\">\n            {/* بطاقة محتوى CTA مع تأثيرات زجاجية */}\n            <div className=\"bg-white/10 backdrop-blur-lg rounded-xl p-6 md:p-8 border border-white/20 shadow-xl relative overflow-hidden\">\n              {/* تأثير التوهج */}\n              <div className=\"absolute -top-20 -left-20 w-40 h-40 bg-accent-500/30 rounded-full blur-3xl\"></div>\n              <div className=\"absolute -bottom-20 -right-20 w-40 h-40 bg-primary-500/30 rounded-full blur-3xl\"></div>\n\n              <div className=\"text-center relative\">\n                {/* العنوان الرئيسي مع تأثير متدرج */}\n                <h2 className=\"text-3xl md:text-4xl font-bold mb-4 leading-tight\">\n                  <span className=\"bg-clip-text text-transparent bg-gradient-to-r from-white via-primary-100 to-white relative inline-block\">\n                    {language === 'ar' ? 'هل أنت مستعد لتحويل أعمالك؟' : 'Ready to Transform Your Business?'}\n                  </span>\n                </h2>\n\n                {/* وصف مع تأثيرات متحركة */}\n                <p className=\"text-base md:text-lg text-white/90 mb-6 max-w-2xl mx-auto\">\n                  {language === 'ar'\n                    ? 'انضم إلى آلاف العملاء الراضين الذين رفعوا مستوى عملياتهم مع حلولنا الشاملة.'\n                    : 'Join thousands of satisfied customers who have elevated their operations with our comprehensive solutions.'}\n                </p>\n\n                {/* أزرار الدعوة للعمل المحسنة */}\n                <div className=\"flex flex-wrap justify-center gap-4\">\n                  <HeroButton\n                    href={`/${currentLanguage}/shop`}\n                    variant=\"accent\"\n                    className=\"py-2.5 px-6 shadow-lg hover:shadow-accent-500/30 transition-all duration-300 group\"\n                  >\n                    <span className=\"relative z-10\">{currentLanguage === 'ar' ? 'استكشف منتجاتنا' : 'Explore Our Products'}</span>\n                    <ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} relative z-10 transition-transform duration-300 group-hover:translate-x-1`} />\n                  </HeroButton>\n                  <HeroButton\n                    href={`/${currentLanguage}/contact`}\n                    variant=\"outline\"\n                    className=\"py-2.5 px-6 border backdrop-blur-sm hover:bg-white/10 transition-all duration-300\"\n                  >\n                    <span className=\"relative z-10\">{currentLanguage === 'ar' ? 'طلب استشارة' : 'Request Consultation'}</span>\n                  </HeroButton>\n                </div>\n\n                {/* شارة الثقة */}\n                <div className=\"mt-6 inline-flex items-center justify-center px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-sm border border-white/20\">\n                  <Shield className=\"w-4 h-4 text-accent-300 mr-1.5\" />\n                  <span className=\"text-white/90 text-xs font-medium\">\n                    {currentLanguage === 'ar' ? 'ضمان الجودة 100%' : '100% Quality Guarantee'}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Latest Industry Insights - قسم أحدث رؤى الصناعة */}\n      <section className=\"py-16 md:py-24 bg-gradient-to-b from-background to-slate-50 dark:from-slate-900 dark:to-slate-800/50\" dir={direction}>\n        <div className=\"container-custom\">\n          {/* عنوان القسم مع تأثيرات بصرية متقدمة */}\n          <div className=\"relative mb-16\">\n            <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center\">\n              <div className=\"relative mb-8 md:mb-0\">\n                {/* شارة القسم */}\n                <div className=\"inline-flex items-center justify-center px-6 py-2.5 rounded-full bg-gradient-to-r from-primary-100 to-accent-100 dark:from-primary-900/50 dark:to-accent-900/50 text-primary-600 dark:text-primary-300 text-sm font-medium mb-4 shadow-sm\">\n                  <FileText className=\"w-4 h-4 mr-2 text-accent-500\" />\n                  {currentLanguage === 'ar' ? 'رؤى وتحليلات صناعية' : 'Industry Insights'}\n                </div>\n\n                <h2 className=\"text-3xl md:text-4xl font-bold mb-4 text-slate-900 dark:text-white\">\n                  {currentLanguage === 'ar' ? 'أحدث المقالات والتحليلات' : 'Latest Articles & Analysis'}\n                </h2>\n                <p className=\"text-lg text-slate-600 dark:text-slate-300 max-w-2xl\">\n                  {currentLanguage === 'ar'\n                    ? 'اكتشف أحدث الاتجاهات والرؤى في مجال الأعمال والصناعة من خبرائنا المتخصصين'\n                    : 'Discover the latest trends and insights in business and industry from our expert specialists'}\n                </p>\n\n                {/* خط زخرفي */}\n                <div className=\"absolute -bottom-4 left-0 w-20 h-1 bg-accent-500 rounded-full\"></div>\n              </div>\n\n              <div className=\"flex flex-wrap items-center gap-4\">\n                <Link href={`/${currentLanguage}/blog`}>\n                  <Button\n                    variant=\"outline\"\n                    className=\"flex items-center gap-2 shadow-sm hover:shadow transition-shadow\"\n                  >\n                    {currentLanguage === 'ar' ? 'جميع المقالات' : 'All Articles'}\n                    <ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} />\n                  </Button>\n                </Link>\n                <Link href={`/${currentLanguage}/blog/featured`}>\n                  <Button\n                    variant=\"primary\"\n                    className=\"flex items-center gap-2 shadow-sm hover:shadow-md transition-shadow\"\n                  >\n                    {currentLanguage === 'ar' ? 'المقالات المميزة' : 'Featured Articles'}\n                    <ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} />\n                  </Button>\n                </Link>\n              </div>\n            </div>\n          </div>\n\n          {/* عرض المقالات المميزة والأحدث - 4 مقالات في صف واحد */}\n          <div className=\"relative mt-8 mb-16\">\n            {/* خلفية زخرفية */}\n            <div className=\"absolute inset-0 -z-10 bg-gradient-to-r from-primary-50/30 to-accent-50/30 dark:from-primary-900/10 dark:to-accent-900/10 rounded-3xl transform -skew-y-1\"></div>\n\n            {/* شريط زخرفي */}\n            <div className=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded-t-xl\"></div>\n\n            {/* المقالات */}\n            <div className=\"py-10 px-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8\">\n                {(() => {\n                  // تحضير المقالات المميزة\n                  const featuredPosts = blogPosts.filter(post => post.featured);\n\n                  // تحضير المقالات الأحدث\n                  const latestPosts = blogPosts\n                    .sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime())\n                    .filter(post => !post.featured);\n\n                  // دمج المقالات المميزة والأحدث، مع الأولوية للمميزة\n                  const postsToShow = [...featuredPosts, ...latestPosts].slice(0, 4);\n\n                  return postsToShow.map((post, index) => {\n                    const isFeatured = post.featured;\n\n                    return (\n                      <div key={post.id} className=\"group relative\">\n                        {/* بطاقة المقال */}\n                        <div className=\"relative h-full bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-md group-hover:shadow-xl transition-all duration-500 flex flex-col border border-slate-200/50 dark:border-slate-700/50 transform group-hover:-translate-y-1 group-hover:scale-[1.01]\">\n                          {/* شارة المقال المميز */}\n                          {isFeatured && (\n                            <div className=\"absolute top-3 right-3 z-10\">\n                              <span className=\"bg-accent-500 text-white text-xs font-bold px-2.5 py-1 rounded-full shadow-lg flex items-center\">\n                                <Star className=\"w-3 h-3 mr-1\" fill=\"white\" />\n                                {currentLanguage === 'ar' ? 'مميز' : 'Featured'}\n                              </span>\n                            </div>\n                          )}\n\n                          {/* صورة المقال */}\n                          <Link href={`/${currentLanguage}/blog/${post.slug}`} className=\"block\">\n                            <div className=\"relative aspect-[4/3] overflow-hidden\">\n                              <EnhancedImage\n                                src={post.coverImage}\n                                alt={post.title}\n                                fill={true}\n                                objectFit=\"cover\"\n                                sizes=\"(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw\"\n                                progressive={true}\n                                placeholder=\"shimmer\"\n                                className=\"transition-transform duration-700 group-hover:scale-105\"\n                              />\n                              <div className=\"absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-black/10\" />\n\n                              {/* فئة المقال */}\n                              <div className=\"absolute top-3 left-3\">\n                                <span className=\"px-2.5 py-1 rounded-lg text-xs font-medium bg-white/90 backdrop-blur-sm text-primary-700 dark:text-primary-700 shadow-sm\">\n                                  {post.category}\n                                </span>\n                              </div>\n\n                              {/* تاريخ النشر */}\n                              <div className=\"absolute bottom-3 left-3 right-3 flex justify-between items-center\">\n                                <div className=\"flex items-center gap-2 text-white text-xs bg-black/30 backdrop-blur-sm px-2.5 py-1 rounded-lg\">\n                                  <Calendar className=\"w-3 h-3\" />\n                                  {new Date(post.publishedAt).toLocaleDateString(currentLanguage === 'ar' ? 'ar-SA' : 'en-US', {\n                                    year: 'numeric',\n                                    month: 'short',\n                                    day: 'numeric'\n                                  })}\n                                </div>\n                                <div className=\"flex items-center gap-1 text-white text-xs bg-black/30 backdrop-blur-sm px-2.5 py-1 rounded-lg\">\n                                  <Clock className=\"w-3 h-3\" />\n                                  {post.readTime}\n                                </div>\n                              </div>\n                            </div>\n                          </Link>\n\n                          {/* محتوى المقال */}\n                          <div className=\"p-5 flex-grow flex flex-col\">\n                            <Link href={`/${currentLanguage}/blog/${post.slug}`} className=\"block group-hover:text-primary-600 dark:group-hover:text-primary-400\">\n                              <h3 className=\"text-lg font-semibold text-slate-900 dark:text-white mb-3 transition-colors line-clamp-2\">\n                                {post.title}\n                              </h3>\n                            </Link>\n\n                            <p className=\"text-slate-600 dark:text-slate-400 mb-4 line-clamp-2 text-sm\">\n                              {post.excerpt}\n                            </p>\n\n                            {/* معلومات الكاتب */}\n                            <div className=\"mt-auto pt-4 border-t border-slate-100 dark:border-slate-700/50 flex items-center justify-between\">\n                              <div className=\"flex items-center gap-2\">\n                                <div className=\"w-8 h-8 rounded-full overflow-hidden border-2 border-white dark:border-slate-700 shadow-sm\">\n                                  <EnhancedImage\n                                    src={post.authorImage}\n                                    alt={post.author}\n                                    fill={true}\n                                    objectFit=\"cover\"\n                                    rounded=\"full\"\n                                    progressive={true}\n                                    placeholder=\"blur\"\n                                    className=\"w-full h-full\"\n                                    containerClassName=\"w-full h-full\"\n                                  />\n                                </div>\n                                <div>\n                                  <p className=\"text-xs font-medium text-slate-900 dark:text-white line-clamp-1\">\n                                    {post.author}\n                                  </p>\n                                  <p className=\"text-xs text-slate-500 dark:text-slate-400\">\n                                    {post.authorTitle.split(' ')[0]}\n                                  </p>\n                                </div>\n                              </div>\n\n                              {/* زر قراءة المزيد */}\n                              <Link href={`/${currentLanguage}/blog/${post.slug}`} className=\"inline-flex items-center justify-center w-8 h-8 rounded-full bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 transition-transform group-hover:scale-110\">\n                                <ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} />\n                              </Link>\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* تأثير الظل */}\n                        <div className=\"absolute -bottom-2 left-2 right-2 h-full rounded-xl bg-gradient-to-r from-primary-500/10 to-accent-500/10 blur-xl -z-10 opacity-0 group-hover:opacity-100 transition-opacity\"></div>\n                      </div>\n                    );\n                  });\n                })()}\n              </div>\n            </div>\n          </div>\n\n          <div className=\"mt-12 text-center\">\n            <Link href={`/${currentLanguage}/blog`}>\n              <Button\n                variant=\"outline\"\n                size=\"lg\"\n                className=\"px-8 shadow-sm hover:shadow transition-shadow group\"\n              >\n                {currentLanguage === 'ar' ? 'استكشاف جميع المقالات' : 'Explore All Articles'}\n                <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-5 w-5 transition-transform duration-300 group-hover:translate-x-1`} />\n              </Button>\n            </Link>\n          </div>\n\n          {/* Newsletter subscription - قسم الاشتراك بالنشرة البريدية */}\n          <div className=\"mt-20 relative\">\n            <div className=\"absolute inset-0 bg-gradient-to-r from-primary-600 to-accent-600 rounded-2xl opacity-90\"></div>\n            <div className=\"absolute inset-0 bg-[url('/images/pattern-dots.svg')] bg-repeat opacity-10\"></div>\n\n            {/* تأثير الزخرفة */}\n            <div className=\"absolute -top-5 -left-5 w-20 h-20 bg-primary-300 rounded-full blur-3xl opacity-30\"></div>\n            <div className=\"absolute -bottom-5 -right-5 w-20 h-20 bg-accent-300 rounded-full blur-3xl opacity-30\"></div>\n\n            <div className=\"relative p-8 md:p-12 rounded-2xl overflow-hidden\">\n              <div className=\"max-w-4xl mx-auto\">\n                <div className=\"flex flex-col md:flex-row items-center justify-between gap-8\">\n                  <div className=\"text-white text-center md:text-left md:max-w-md\">\n                    <div className=\"inline-flex items-center justify-center px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-sm text-white text-sm font-medium mb-4\">\n                      <Bell className=\"w-4 h-4 mr-2\" />\n                      {currentLanguage === 'ar' ? 'ابق على اطلاع' : 'Stay Updated'}\n                    </div>\n                    <h3 className=\"text-2xl md:text-3xl font-bold mb-4\">\n                      {currentLanguage === 'ar'\n                        ? 'اشترك في نشرتنا الإخبارية للحصول على آخر التحديثات'\n                        : 'Subscribe to Our Newsletter for Latest Updates'}\n                    </h3>\n                    <p className=\"text-white/80 text-base md:text-lg\">\n                      {currentLanguage === 'ar'\n                        ? 'احصل على آخر العروض والمنتجات الجديدة والتحديثات الصناعية مباشرة إلى بريدك الإلكتروني.'\n                        : 'Get the latest offers, new products, and industry updates delivered directly to your inbox.'}\n                    </p>\n                  </div>\n\n                  <div className=\"w-full md:w-auto bg-white/10 backdrop-blur-sm p-6 rounded-xl border border-white/20 shadow-xl\">\n                    <NewsletterForm\n                      variant=\"inline\"\n                      className=\"w-full md:min-w-[400px]\"\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {showWholesaleForm && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50\">\n          <div className=\"max-w-2xl w-full\">\n            <WholesaleQuoteForm\n              onClose={() => {\n                setShowWholesaleForm(false);\n                setSelectedProduct(null);\n                setSelectedService(null);\n              }}\n              isCustomProduct={false}\n              product={selectedProduct}\n              serviceName={selectedService?.name}\n            />\n          </div>\n        </div>\n      )}\n\n    </div>\n  );\n}\n\nexport default HomePage;"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAEA;AACA;AACA;AACA;;;AA9BA;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA,+EAA+E;AAC/E,MAAM,iBAAiB;IACrB,IAAI;QACF;YACE,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;QACA;YACE,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;QACA;YACE,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;KACD;IACD,IAAI;QACF;YACE,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;QACA;YACE,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;QACA;YACE,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;KACD;AACH;AAEA,yBAAyB;AACzB,MAAM,gBAAgB;IACpB,IAAI;QACF;YACE,oBAAM,6LAAC,yNAAA,CAAA,eAAY;gBAAC,MAAM;;;;;;YAC1B,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;YACnB,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,2MAAA,CAAA,UAAO;gBAAC,MAAM;;;;;;YACrB,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;YACnB,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,mMAAA,CAAA,MAAG;gBAAC,MAAM;;;;;;YACjB,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,MAAM;;;;;;YACtB,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;aACD;YACD,OAAO;QACT;KACD;IACD,IAAI;QACF;YACE,oBAAM,6LAAC,yNAAA,CAAA,eAAY;gBAAC,MAAM;;;;;;YAC1B,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;YACnB,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,2MAAA,CAAA,UAAO;gBAAC,MAAM;;;;;;YACrB,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;YACnB,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,mMAAA,CAAA,MAAG;gBAAC,MAAM;;;;;;YACjB,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;aACD;YACD,OAAO;QACT;QACA;YACE,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,MAAM;;;;;;YACtB,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;aACD;YACD,OAAO;QACT;KACD;AACH;AAEA,SAAS;;IACP,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAChC,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,KAAK,qBAAqB;IACzE,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAE3B,8CAA8C;IAC9C,MAAM,kBAAkB,AAAC,UAA0B;IACnD,MAAM,aAAa,cAAc,CAAC,gBAAgB;IAClD,MAAM,mBAAmB,aAAa,CAAC,gBAAgB;IAEvD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACzF,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAEvE,gCAAgC;IAChC,MAAM,kBAAkB,CAAC;QACvB,mBAAmB;QACnB,iBAAiB;IACnB;IAEA,+BAA+B;IAC/B,MAAM,uBAAuB,CAAC;QAC5B,mBAAmB;QACnB,qBAAqB;IACvB;IAEA,sEAAsE;IACtE,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,oBAAiB,AAAD;IACtC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,wBAAwB;QAC5B,UAAU;IACZ;IAEA,wFAAwF;IACxF,MAAM,kBAAkB,CAAC;QACvB,mCAAmC;QACnC,IAAI,CAAC,QAAQ,OAAO,IAAI,QAAQ,KAAK,IAAI,GAAG;YAC1C,4CAA4C;YAC5C,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,IAAI,CAAC,qBAAqB,CAAC;YAC1D;QACF;QAEA,wCAAwC;QACxC,MAAM,WAAW;YACf,IAAI,QAAQ,EAAE;YACd,MAAM,QAAQ,IAAI;YAClB,SAAS,QAAQ,OAAO,IAAI,QAAQ,IAAI;YACxC,OAAO,QAAQ,KAAK;YACpB,OAAO,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,IAAI,QAAQ,MAAM,CAAC,EAAE,GAAG;YACzE,UAAU;QACZ;QAEA,kEAAkE;QAClE,MAAM,OAAO,6HAAA,CAAA,eAAY,CAAC,QAAQ;QAClC,KAAK,OAAO,CAAC,UAAU;QAEvB,gFAAgF;QAChF,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,IAAI,CAAC,UAAU,CAAC;IAEjD,6CAA6C;IAC7C,qFAAqF;IACvF;IAEA,mDAAmD;IACnD,MAAM,sBAAsB,CAAC;QAC3B,MAAM,WAAW,iIAAA,CAAA,mBAAgB,CAAC,QAAQ;QAE1C,sDAAsD;QACtD,IAAI,SAAS,YAAY,CAAC,QAAQ,EAAE,GAAG;YACrC,0BAA0B;YAC1B,SAAS,UAAU,CAAC,QAAQ,EAAE;YAC9B,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,IAAI,CAAC,WAAW,CAAC;QACpD,OAAO;YACL,2BAA2B;YAC3B,SAAS,OAAO,CAAC;YACjB,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,IAAI,CAAC,YAAY,CAAC;QACrD;IAEA,6CAA6C;IAC7C,yFAAyF;IAC3F;IAIA,kBAAkB;IAClB,MAAM,gBAAgB,CAAC;QACrB,qCAAqC;QACrC,QAAQ,GAAG,CAAC,oBAAoB,QAAQ,IAAI;IAC9C;IAEA,gBAAgB;IAChB,MAAM,cAAc,CAAC;QACnB,kCAAkC;QAClC,IAAI,UAAU,KAAK,EAAE;YACnB,UAAU,KAAK,CAAC;gBACd,OAAO,QAAQ,IAAI;gBACnB,MAAM,QAAQ,WAAW;gBACzB,KAAK,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,MAAM,EAAE,QAAQ,IAAI,EAAE;YAC1E;QACF,OAAO;YACL,gCAAgC;YAChC,UAAU,SAAS,CAAC,SAAS,CAAC,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,MAAM,EAAE,QAAQ,IAAI,EAAE;QACnG;IACF;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,uDAAuD;IACvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM;2DAAuB;oBAC3B,qBAAqB;oBACrB,IAAI;wBACF,uDAAuD;wBACvD,MAAM,SAAS;wBACf,MAAM,eAAe,OAAO,QAAQ,IAAI,EAAE;wBAE1C,6CAA6C;wBAC7C,MAAM,mBAAmB,aAAa,MAAM;wFAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;;wBAE7E,+CAA+C;wBAC/C,MAAM,iBAAiB,iBAAiB,IAAI;sFAAC,CAAC,GAAG;gCAC/C,sCAAsC;gCACtC,IAAI,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,IAAI,GAAG,OAAO,CAAC;gCACzC,IAAI,EAAE,KAAK,IAAI,KAAK,EAAE,KAAK,GAAG,GAAG,OAAO;gCAExC,iBAAiB;gCACjB,MAAM,aAAa,CAAC,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,IAAI,CAAC;gCACnD,IAAI,eAAe,GAAG,OAAO;gCAE7B,sBAAsB;gCACtB,OAAO,CAAC,EAAE,UAAU,IAAI,CAAC,IAAI,CAAC,EAAE,UAAU,IAAI,CAAC;4BACjD;;wBAEA,kDAAkD;wBAClD,MAAM,iBAAiB,eAAe,KAAK,CAAC,GAAG;wBAE/C,gDAAgD;wBAChD,MAAM,oBAAoB,eAAe,GAAG;yFAAC,CAAA,UAAW,CAAC;oCACvD,GAAG,OAAO;oCACV,uBAAuB;oCACvB,QAAQ,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,IAC9C,QAAQ,MAAM,GACd;wCAAC;qCAAwC;oCAC7C,kBAAkB;oCAClB,OAAO,OAAO,QAAQ,KAAK,KAAK,WAAW,QAAQ,KAAK,GAAG;oCAC3D,oBAAoB;oCACpB,OAAO,OAAO,QAAQ,KAAK,KAAK,WAAW,QAAQ,KAAK,GAAG;oCAC3D,oBAAoB;oCACpB,QAAQ,OAAO,QAAQ,MAAM,KAAK,WAAW,QAAQ,MAAM,GAAG;oCAC9D,aAAa,OAAO,QAAQ,WAAW,KAAK,WAAW,QAAQ,WAAW,GAAG;oCAC7E,mBAAmB;oCACnB,SAAS,OAAO,QAAQ,KAAK,KAAK,WAAW,QAAQ,KAAK,GAAG,IAAI;oCACjE,kBAAkB;oCAClB,UAAU,QAAQ,cAAc,IAAI,QAAQ,cAAc,GAAG,QAAQ,KAAK,GACtE,KAAK,KAAK,CAAC,CAAC,IAAI,QAAQ,KAAK,GAAG,QAAQ,cAAc,IAAI,OAC1D;oCACJ,4BAA4B;oCAC5B,SAAS,QAAQ,OAAO,IAAI,QAAQ,IAAI;oCACxC,gBAAgB,QAAQ,cAAc,IAAI,QAAQ,WAAW;oCAC7D,mCAAmC;oCACnC,iBAAiB,QAAQ,eAAe,IAAI,CAAC,QAAQ,KAAK,GAAG,IAAI,aAAa,cAAc;oCAC5F,UAAU,KAAK,uBAAuB;gCACxC,CAAC;;wBAED,oBAAoB;wBACpB,qBAAqB;wBAErB,wBAAwB;wBACxB,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,kBAAkB,MAAM,CAAC,2CAA2C,CAAC;oBAE7F,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,qCAAqC;wBACnD,oBAAoB,EAAE;wBACtB,qBAAqB;oBACvB;gBACF;;YAEA;QACF;6BAAG,EAAE;IAEL,kEAAkE;IAClE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,gFAAgF;YAChF,MAAM,WAAW;+CAAY;oBAC3B,4DAA4D;oBAC5D;uDAAqB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,WAAW,MAAM;;gBAC/D;8CAAG;YAEH;sCAAO,IAAM,cAAc;;QAC7B;6BAAG;QAAC,WAAW,MAAM;KAAC;IAEtB,oCAAoC;IACpC,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,eAAY,AAAD;IACnD,MAAM,cAAc,WAAW;IAE/B,kDAAkD;IAClD,MAAM,qBAAqB;QACzB,gBAAgB;IAClB;IAEA,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM;uDAAmB,CAAC;oBACxB,IAAI,MAAM,OAAO,IAAI,KAAK,CAAC,eAAe,OAAO,CAAC,oBAAoB;wBACpE,uBAAuB;wBACvB,eAAe,OAAO,CAAC,mBAAmB;oBAC5C;gBACF;;YAEA,SAAS,gBAAgB,CAAC,cAAc;YACxC;sCAAO;oBACL,SAAS,mBAAmB,CAAC,cAAc;gBAC7C;;QACF;6BAAG,EAAE;IAEL,sFAAsF;IACtF,MAAM,0BAA0B,CAAC,OAAiC,CAAC;YACjE,IAAI,KAAK,EAAE;YACX,MAAM,KAAK,IAAI;YACf,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE;YAC7B,aAAa,KAAK,WAAW;YAC7B,OAAO,KAAK,cAAc;YAC1B,gBAAgB,KAAK,aAAa;YAClC,QAAQ,KAAK,KAAK,GAAG;gBAAC,KAAK,KAAK;aAAC,GAAG,EAAE;YACtC,UAAU,KAAK,QAAQ;YACvB,MAAM;gBAAC,KAAK,QAAQ;aAAC;YACrB,OAAO,KAAK,iBAAiB;YAC7B,UAAU;YACV,gBAAgB,CAAC;YACjB,WAAW,IAAI,OAAO,WAAW;YACjC,wEAAwE;YACxE,SAAS,EAAE;YACX,QAAQ;YACR,aAAa;YACb,iBAAiB,EAAE;QACrB,CAAC;IAED,qBACE,6LAAC;;YAEE,uBAAuB,iBAAiB,MAAM,GAAG,mBAChD,6LAAC,qJAAA,CAAA,kBAAe;gBACd,SAAS,IAAM,uBAAuB;gBACtC,SAAS,gBAAgB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM,EAAE;;;;;;0BAKlF,6LAAC;gBAAQ,WAAU;gBAAmH,KAAK;;kCAEzI,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;wCAEC,WAAW,CAAC,+CAA+C,CAAC;wCAC5D,OAAO;4CACL,OAAO,GAAG,KAAK,MAAM,KAAK,IAAI,EAAE,EAAE,CAAC;4CACnC,QAAQ,GAAG,KAAK,MAAM,KAAK,IAAI,EAAE,EAAE,CAAC;4CACpC,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4CAC9B,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4CAC/B,mBAAmB,GAAG,KAAK,MAAM,KAAK,IAAI,EAAE,CAAC,CAAC;4CAC9C,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;wCACzC;uCATK,CAAC,SAAS,EAAE,GAAG;;;;;;;;;;;;;;;;;;;;kCAiB9B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;gCAAgB,SAAQ;gCAAc,qBAAoB;;kDACvE,6LAAC;;0DACC,6LAAC;gDAAe,IAAG;gDAAgB,IAAG;gDAAK,IAAG;gDAAK,IAAG;gDAAO,IAAG;;kEAC9D,6LAAC;wDAAK,QAAO;wDAAK,WAAU;wDAA2B,aAAY;;;;;;kEACnE,6LAAC;wDAAK,QAAO;wDAAO,WAAU;wDAA6B,aAAY;;;;;;;;;;;;0DAEzE,6LAAC;gDAAQ,IAAG;gDAAe,GAAE;gDAAI,GAAE;gDAAI,OAAM;gDAAK,QAAO;gDAAK,cAAa;0DACzE,cAAA,6LAAC;oDAAO,IAAG;oDAAI,IAAG;oDAAI,GAAE;oDAAM,MAAK;;;;;;;;;;;;;;;;;kDAGvC,6LAAC;wCAAK,GAAE;wCAAgC,MAAK;;;;;;kDAC7C,6LAAC;wCAAK,GAAE;wCAAI,GAAE;wCAAI,OAAM;wCAAM,QAAO;wCAAM,MAAK;;;;;;oCAG/C;2CAAI,MAAM;qCAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;4CAEC,IAAG;4CACH,IAAI,IAAI;4CACR,IAAG;4CACH,IAAI,IAAI;4CACR,QAAO;4CACP,aAAY;4CACZ,iBAAgB;2CAPX,CAAC,EAAE,EAAE,GAAG;;;;;oCAUhB;2CAAI,MAAM;qCAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;4CAEC,IAAI,IAAI;4CACR,IAAG;4CACH,IAAI,IAAI;4CACR,IAAG;4CACH,QAAO;4CACP,aAAY;4CACZ,iBAAgB;2CAPX,CAAC,EAAE,EAAE,GAAG;;;;;;;;;;;;;;;;;;;;;kCAevB,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,OAAO,sBACtB,6LAAC;gCAEC,WAAW,CAAC,8CAA8C,EACxD,UAAU,oBACN,0BACA,uBACJ;;kDAEF,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC,4IAAA,CAAA,gBAAa;wCACZ,KAAK,MAAM,GAAG;wCACd,KAAK,MAAM,GAAG;wCACd,MAAM;wCACN,WAAU;wCACV,aAAa;wCACb,aAAY;wCACZ,WAAU;wCACV,oBAAmB;wCACnB,OAAM;wCACN,UAAU,UAAU;wCACpB,SAAS;;;;;;;+BAnBN;;;;;;;;;;kCA4BX,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCAEb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,6JAAA,CAAA,mBAAgB;wCAAC,MAAK;wCAAO,UAAU;wCAAK,OAAO;kDAClD,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DACC,OAAM;4DACN,OAAM;4DACN,QAAO;4DACP,SAAQ;4DACR,MAAK;4DACL,QAAO;4DACP,aAAY;4DACZ,eAAc;4DACd,gBAAe;4DACf,WAAU;;8EAEV,6LAAC;oEAAK,GAAE;;;;;;8EACR,6LAAC;oEAAK,GAAE;;;;;;8EACR,6LAAC;oEAAK,GAAE;;;;;;;;;;;;;;;;;;8DAGZ,6LAAC;oDAAK,WAAW,CAAC,mBAAmB,EAAE,oBAAoB,OAAO,SAAS,OAAO,iFAAiF,CAAC;;wDACjK,oBAAoB,OAAO,UAAU;sEACtC,6LAAC;4DAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAMtB,6LAAC,6JAAA,CAAA,mBAAgB;wCAAC,MAAK;wCAAQ,WAAU;wCAAK,UAAU;wCAAK,OAAO;kDAClE,cAAA,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DACb,oBAAoB,OACjB,uBACA;;;;;;8DAEN,6LAAC;;;;;8DACD,6LAAC;oDAAK,WAAU;;wDACb,oBAAoB,OACjB,aACA;sEACJ,6LAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAMrB,6LAAC,6JAAA,CAAA,mBAAgB;wCAAC,MAAK;wCAAQ,WAAU;wCAAK,UAAU;wCAAK,OAAO;kDAClE,cAAA,6LAAC;4CAAE,WAAU;sDACV,oBAAoB,OACjB,+FACA;;;;;;;;;;;kDAKR,6LAAC,6JAAA,CAAA,mBAAgB;wCAAC,MAAK;wCAAQ,WAAU;wCAAK,UAAU;wCAAK,OAAO;kDAClE,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yIAAA,CAAA,aAAU;oDACT,MAAM,CAAC,CAAC,EAAE,gBAAgB,KAAK,CAAC;oDAChC,SAAQ;oDACR,WAAU;oDACV,SAAS;;sEAET,6LAAC;4DAAK,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAiB,oBAAoB,OAAO,kBAAkB;;;;;;sEAC9E,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAW,CAAC,QAAQ,EAAE,oBAAoB,OAAO,oBAAoB,OAAO,0EAA0E,CAAC;;;;;;;;;;;;8DAErK,6LAAC,yIAAA,CAAA,aAAU;oDACT,MAAM,CAAC,CAAC,EAAE,gBAAgB,QAAQ,CAAC;oDACnC,SAAQ;oDACR,WAAU;;sEAEV,6LAAC;4DAAK,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAiB,oBAAoB,OAAO,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/F,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,GAAG,sBAClB,6LAAC;oCAEC,SAAS,IAAM,qBAAqB;oCACpC,WAAW,CAAC,gFAAgF,EAC1F,UAAU,oBACN,uBACA,qCACJ;oCACF,cAAY,CAAC,YAAY,EAAE,QAAQ,GAAG;8CAErC,UAAU,mCACT,6LAAC;wCAAK,WAAU;;;;;;mCAVb;;;;;;;;;;;;;;;;;;;;;0BAuBf,6LAAC;gBAAQ,IAAG;gBAAY,WAAU;gBAAsH,KAAK;0BAC3J,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;;;;;8CAGf,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAItB,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAK,WAAU;sDACb,EAAE;;;;;;sDAEL,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAIjB,6LAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;sCAKP,6LAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,UAAU,sBAC/B,6LAAC;oCAEC,WAAU;;sDAGV,6LAAC;4CAAI,WAAU;;;;;;sDAGf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,SAAS,IAAI;;;;;;sEAEhB,6LAAC;4DAAG,WAAW,CAAC,kBAAkB,EAAE,oBAAoB,OAAO,SAAS,OAAO,+BAA+B,CAAC;sEAC5G,SAAS,KAAK;;;;;;;;;;;;8DAKnB,6LAAC;oDAAE,WAAU;8DACV,SAAS,WAAW;;;;;;;;;;;;sDAKzB,6LAAC;4CAAI,WAAU;;gDACZ,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,mBAC/C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC,qMAAA,CAAA,OAAI;oEAAC,MAAM;oEAAI,WAAW,GAAG,oBAAoB,OAAO,SAAS,OAAO,gBAAgB,CAAC;;;;;;gEACzF,EAAE;;;;;;;sEAEL,6LAAC;4DAAG,WAAU;sEACX,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,uBAC/B,6LAAC;oEAAgB,WAAU;;sFACzB,6LAAC,uNAAA,CAAA,cAAW;4EAAC,MAAM;4EAAI,WAAW,CAAC,6DAA6D,EAAE,oBAAoB,OAAO,SAAS,OAAO,OAAO,CAAC;;;;;;sFACrJ,6LAAC;sFAAM;;;;;;;mEAFA;;;;;;;;;;;;;;;;8DAUjB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,CAAC,EAAE,gBAAgB,CAAC,EAAE,SAAS,KAAK,KAAK,kBAAkB,SAAS,KAAK,KAAK,yBAAyB,SACvG,SAAS,KAAK,KAAK,qBAAqB,SAAS,KAAK,KAAK,wBAAwB,cACnF,SAAS,KAAK,KAAK,sBAAsB,SAAS,KAAK,KAAK,iBAAiB,qBAC7E,SAAS,KAAK,KAAK,uBAAuB,SAAS,KAAK,KAAK,kBAAkB,aAC/E,SAAS,KAAK,KAAK,qBAAqB,SAAS,KAAK,KAAK,mBAAmB,cAC9E,SAAS,KAAK,KAAK,uBAAuB,SAAS,KAAK,KAAK,gBAAgB,SAAS,aAAa;wDAC1G,WAAU;wDACV,cAAY,GAAG,oBAAoB,OAAO,oBAAoB,sBAAsB,CAAC,EAAE,SAAS,KAAK,EAAE;;0EAEvG,6LAAC;0EAAM,oBAAoB,OAAO,iBAAiB;;;;;;0EACnD,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAW,CAAC,8EAA8E,EAAE,oBAAoB,OAAO,eAAe,IAAI;;;;;;;;;;;;;;;;;;;;;;;sDAM5J,6LAAC;4CAAI,WAAU;;;;;;;mCA7DV;;;;;;;;;;;;;;;;;;;;;0BAuEf,6LAAC;gBAAQ,IAAG;gBAAoB,WAAU;gBAA2I,KAAK;0BACxL,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC,SAAS,EAAE,oBAAoB,OAAO,aAAa,UAAU,6FAA6F,CAAC;;;;;;sDAC5K,6LAAC;4CAAG,WAAU;sDACX,oBAAoB,OAAO,qBAAqB;;;;;;sDAEnD,6LAAC;4CAAE,WAAU;sDACV,oBAAoB,OAAO,uDAAuD;;;;;;;;;;;;8CAGvF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,KAAK,CAAC;sDACpC,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;gDACV,cAAY,oBAAoB,OAAO,sBAAsB;;oDAE5D,oBAAoB,OAAO,sBAAsB;kEAClD,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAW,CAAC,QAAQ,EAAE,oBAAoB,OAAO,eAAe,IAAI;;;;;;;;;;;;;;;;;sDAGpF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,cAAc,CAAC;sDAC7C,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;gDACV,cAAY,oBAAoB,OAAO,0BAA0B;;oDAEhE,oBAAoB,OAAO,gBAAgB;kEAC5C,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAW,CAAC,0CAA0C,EAAE,oBAAoB,OAAO,eAAe,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBASzH,oBACC,kCAAkC;sCAClC,6LAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,6LAAC,mIAAA,CAAA,OAAI;oCAAa,WAAU;;sDAC1B,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;mCALR;;;;;;;;;mCAUb,iBAAiB,MAAM,GAAG,IAC5B,sDAAsD;sCACtD;;8CAEE,6LAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC;4CAAqB,WAAU;sDAC9B,cAAA,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEACb,oBAAoB,OAAO,SAAS;;;;;;;;;;;oDAKxC,QAAQ,cAAc,IAAI,QAAQ,cAAc,GAAG,QAAQ,KAAK,kBAC/D,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEACb,GAAG,QAAQ,QAAQ,CAAC,EAAE,EAAE,oBAAoB,OAAO,QAAQ,OAAO;;;;;;;;;;;kEAMzE,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,cAAc,EAAE,QAAQ,IAAI,EAAE;wDAAE,WAAU;;0EACxE,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,4IAAA,CAAA,gBAAa;oEACZ,KAAK,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,IAAI,QAAQ,MAAM,CAAC,EAAE,GAAG;oEACvE,KAAK,QAAQ,IAAI;oEACjB,MAAM;oEACN,WAAU;oEACV,aAAa;oEACb,aAAY;oEACZ,WAAU;oEACV,OAAM;oEACN,UAAU,QAAQ;;;;;;;;;;;0EAKtB,6LAAC;gEAAI,WAAU;;kFAEb,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAW,CAAC,uFAAuF,EACjG,iIAAA,CAAA,mBAAgB,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,EAAE,IAC/C,8BACA,6DACJ;wEACF,SAAS,CAAC;4EACR,EAAE,cAAc;4EAChB,EAAE,eAAe;4EACjB,oBAAoB;wEACtB;wEACA,cAAY,oBAAoB,OAAO,sBAAsB;kFAE7D,cAAA,6LAAC,uMAAA,CAAA,QAAK;4EACJ,WAAW,CAAC,QAAQ,EAAE,iIAAA,CAAA,mBAAgB,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,EAAE,KAAK,gBAAgB;;;;;;;;;;;kFAKlG,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,CAAC;4EACR,EAAE,cAAc;4EAChB,EAAE,eAAe;4EACjB,mBAAmB;4EACnB,iBAAiB;wEACnB;wEACA,cAAY,oBAAoB,OAAO,aAAa;kFAEpD,cAAA,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAMrB,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;;oEACZ,QAAQ,QAAQ,kBACf,6LAAC;wEAAK,WAAU;kFACb,QAAQ,QAAQ;;;;;;kFAIrB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAW,CAAC,wBAAwB,EAAE,oBAAoB,OAAO,SAAS,QAAQ;;;;;;0FACxF,6LAAC;;oFACE,QAAQ,MAAM,EAAE,QAAQ,MAAM;oFAC9B,QAAQ,WAAW,GAAG,CAAC,CAAC,EAAE,QAAQ,WAAW,CAAC,CAAC,CAAC,GAAG;;;;;;;;;;;;;;;;;;;0EAM1D,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,cAAc,EAAE,QAAQ,IAAI,EAAE;gEAAE,WAAU;0EACxE,cAAA,6LAAC;oEAAG,WAAU;8EACX,oBAAoB,OAAQ,QAAQ,OAAO,IAAI,QAAQ,IAAI,GAAI,QAAQ,IAAI;;;;;;;;;;;0EAKhF,6LAAC;gEAAE,WAAU;0EACV,oBAAoB,OAChB,QAAQ,cAAc,IAAI,QAAQ,WAAW,GAC9C,QAAQ,WAAW;;;;;;0EAIzB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,KAAK;;;;;;4EAE9B,QAAQ,cAAc,IAAI,QAAQ,cAAc,GAAG,QAAQ,KAAK,kBAC/D,6LAAC;gFAAK,WAAU;0FACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,cAAc;;;;;;;;;;;;kFAK5C,6LAAC;wEAAI,WAAU;kFACZ,QAAQ,KAAK,GAAG,kBACf,6LAAC;4EAAK,WAAU;sFACb,oBAAoB,OAAO,UAAU;;;;;iGAGxC,6LAAC;4EAAK,WAAU;sFACb,oBAAoB,OAAO,gBAAgB;;;;;;;;;;;;;;;;;0EAOpD,6LAAC;gEAAI,WAAU;;kFAEb,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,CAAC;4EACR,EAAE,cAAc;4EAChB,EAAE,eAAe;4EACjB,gBAAgB;wEAClB;wEACA,UAAU,QAAQ,KAAK,IAAI;wEAC3B,cAAY,oBAAoB,OAAO,cAAc;;0FAErD,6LAAC,yNAAA,CAAA,eAAY;gFAAC,WAAW,CAAC,QAAQ,EAAE,oBAAoB,OAAO,SAAS,QAAQ;;;;;;0FAChF,6LAAC;0FAAM,oBAAoB,OAAO,cAAc;;;;;;;;;;;;kFAIlD,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,CAAC;4EACR,EAAE,cAAc;4EAChB,EAAE,eAAe;4EACjB,mBAAmB;4EACnB,qBAAqB;wEACvB;wEACA,cAAY,oBAAoB,OAAO,qBAAqB;;0FAE5D,6LAAC,uMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;0FACjB,6LAAC;gFAAK,WAAU;0FAAoB,oBAAoB,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CAvKxE,QAAQ,EAAE;;;;;;;;;;gCAmLvB,iBAAiB,iCAChB,6LAAC,0IAAA,CAAA,YAAS;oCACR,SAAS;oCACT,SAAS;wCACP,iBAAiB;wCACjB,mBAAmB;oCACrB;oCACA,aAAa;oCACb,kBAAkB;;;;;;8CAKtB,6LAAC,oJAAA,CAAA,qBAAkB;oCACjB,QAAQ,qBAAqB,CAAC,CAAC;oCAC/B,SAAS;wCACP,qBAAqB;wCACrB,mBAAmB;oCACrB;oCACA,iBAAiB;oCACjB,iBAAiB;;;;;;;2CAIrB,6BAA6B;sCAC7B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,2MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;oCAAE,WAAU;8CACV,oBAAoB,OAAO,gCAAgC;;;;;;8CAE9D,6LAAC;oCAAE,WAAU;8CACV,oBAAoB,OACjB,uFACA;;;;;;8CAEN,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,KAAK,CAAC;8CACpC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;kDACb,oBAAoB,OAAO,uBAAuB;;;;;;;;;;;;;;;;;wBAO1D,iBAAiB,MAAM,GAAG,mBACzB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,mBAAmB,CAAC;0CAClD,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,cAAY,oBAAoB,OAAO,wCAAwC;;wCAE9E,oBAAoB,OAAO,8BAA8B;sDAC1D,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAW,GAAG,oBAAoB,OAAO,oBAAoB,OAAO,oEAAoE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWjK,6LAAC;gBAAQ,WAAU;gBAA+F,KAAK;0BACrH,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAClB,oBAAoB,OAAO,kBAAkB;;;;;;;sDAGhD,6LAAC;4CAAG,WAAU;sDACX,oBAAoB,OAAO,4BAA4B;;;;;;sDAE1D,6LAAC;4CAAE,WAAU;sDACV,oBAAoB,OACjB,kGACA;;;;;;;;;;;;8CAGR,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,UAAU,CAAC;sDACzC,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;;oDAET,oBAAoB,OAAO,oBAAoB;kEAChD,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAW,CAAC,QAAQ,EAAE,oBAAoB,OAAO,eAAe,IAAI;;;;;;;;;;;;;;;;;sDAGpF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,kCAAkC,CAAC;sDACjE,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;;oDAET,oBAAoB,OAAO,kBAAkB;kEAC9C,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAW,CAAC,QAAQ,EAAE,oBAAoB,OAAO,eAAe,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOxF,6LAAC;4BAAI,WAAU;sCACZ,2HAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBAClE,6LAAC;oCAAqB,WAAU;;sDAE9B,6LAAC;4CAAI,WAAU;sDACZ,oBAAoB,OAAO,QAAQ,WAAW,GAAG,QAAQ,QAAQ;;;;;;sDAIpE,6LAAC;4CAAI,WAAU;sDAAsH;;;;;;sDAKrI,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,4IAAA,CAAA,gBAAa;oDACZ,KAAK,QAAQ,MAAM,CAAC,EAAE;oDACtB,KAAK,oBAAoB,OAAO,QAAQ,OAAO,GAAG,QAAQ,IAAI;oDAC9D,MAAM;oDACN,WAAU;oDACV,aAAa;oDACb,aAAY;oDACZ,WAAU;oDACV,oBAAmB;oDACnB,OAAM;;;;;;8DAER,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAGjB,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,WAAW,EAAE,QAAQ,IAAI,EAAE;oDAAE,WAAU;8DACrE,cAAA,6LAAC;wDAAG,WAAU;kEACX,oBAAoB,OAAO,QAAQ,OAAO,GAAG,QAAQ,IAAI;;;;;;;;;;;8DAK9D,6LAAC;oDAAE,WAAU;8DACV,oBAAoB,OAAO,QAAQ,cAAc,GAAG,QAAQ,WAAW;;;;;;8DAI1E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,2MAAA,CAAA,UAAO;4DAAC,WAAW,GAAG,oBAAoB,OAAO,SAAS,OAAO,uCAAuC,CAAC;;;;;;sEAC1G,6LAAC;sEACE,oBAAoB,OAAO,iCAAiC;;;;;;;;;;;;8DAKjE,6LAAC;oDAAI,WAAU;8DACZ,CAAC,oBAAoB,OAAO,QAAQ,WAAW,GAAG,QAAQ,QAAQ,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAC7F,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;oEAAI,WAAW,GAAG,oBAAoB,OAAO,SAAS,OAAO,uBAAuB,CAAC;8EACpF,cAAA,6LAAC,uNAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;;;;;;8EAEzB,6LAAC;oEAAK,WAAU;8EACb;;;;;;;2DALK;;;;;;;;;;8DAYd,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,WAAW,EAAE,QAAQ,IAAI,EAAE;4DAAE,WAAU;sEACrE,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,WAAU;;kFAEV,6LAAC;kFAAM,oBAAoB,OAAO,iBAAiB;;;;;;kFACnD,6LAAC,qNAAA,CAAA,aAAU;wEAAC,WAAW,CAAC,oEAAoE,EAAE,oBAAoB,OAAO,eAAe,IAAI;;;;;;;;;;;;;;;;;sEAGhJ,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,WAAW,EAAE,QAAQ,IAAI,CAAC,aAAa,CAAC;4DAAE,WAAU;sEAClF,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,WAAU;0EAET,oBAAoB,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;mCA9E5C,QAAQ,EAAE;;;;;;;;;;sCAwFxB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,UAAU,CAAC;0CACzC,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;wCAET,oBAAoB,OAAO,iCAAiC;sDAC7D,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAW,GAAG,oBAAoB,OAAO,oBAAoB,OAAO,oEAAoE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/J,6LAAC;gBAAQ,WAAU;gBAAgE,KAAK;0BACtF,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDACX,aAAa,OAAO,+BAA+B;;;;;;sDAEtD,6LAAC;4CAAE,WAAU;sDACV,aAAa,OACV,yGACA;;;;;;;;;;;;8CAGR,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,UAAU,CAAC;kDACzC,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;;gDAET,oBAAoB,OAAO,4BAA4B;8DACxD,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAW,CAAC,QAAQ,EAAE,oBAAoB,OAAO,eAAe,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMxF,6LAAC;4BAAI,WAAU;sCACZ,gIAAA,CAAA,iBAAc,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBAC/B,6LAAC;oCAAkB,WAAU;;sDAE3B,6LAAC;4CAAI,WAAU;sDACZ,oBAAoB,OACjB,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,CAAC,IAAI,KAAK,cAAc,GAAG,KAAK,aAAa,IAAI,KAAK,CAAC,CAAC,GAC1E,GAAG,KAAK,KAAK,CAAC,CAAC,IAAI,KAAK,cAAc,GAAG,KAAK,aAAa,IAAI,KAAK,KAAK,CAAC;;;;;;sDAIhF,6LAAC;4CAAI,WAAU;sDACZ,oBAAoB,OAAO,UAAU;;;;;;sDAIxC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,4IAAA,CAAA,gBAAa;oDACZ,KAAK,KAAK,KAAK;oDACf,KAAK,KAAK,IAAI;oDACd,MAAM;oDACN,WAAU;oDACV,aAAa;oDACb,aAAY;oDACZ,WAAU;oDACV,oBAAmB;oDACnB,OAAM;;;;;;8DAER,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAGjB,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,KAAK,QAAQ;;;;;;sEAEhB,6LAAC;4DAAK,WAAW,CAAC,+CAA+C,EAC/D,KAAK,SAAS,KAAK,QAAQ,gFAC3B,KAAK,SAAS,KAAK,aAAa,oEAChC,+EACA;sEACC,oBAAoB,OAChB,KAAK,SAAS,KAAK,QAAQ,SAAS,KAAK,SAAS,KAAK,aAAa,YAAY,WACjF,KAAK,SAAS;;;;;;;;;;;;8DAKtB,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,CAAC,EAAE,gBAAgB,WAAW,EAAE,KAAK,EAAE,EAAE;oDAChD,WAAU;8DAEV,cAAA,6LAAC;wDAAG,WAAU;kEACX,KAAK,IAAI;;;;;;;;;;;8DAKd,6LAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;8DAInB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,2MAAA,CAAA,UAAO;oEAAC,MAAM;oEAAI,WAAW,GAAG,oBAAoB,OAAO,SAAS,OAAO,cAAc,CAAC;;;;;;gEAC1F,oBAAoB,OACjB,CAAC,mBAAmB,EAAE,KAAK,QAAQ,CAAC,KAAK,CAAC,GAC1C,CAAC,YAAY,EAAE,KAAK,QAAQ,CAAC,MAAM,CAAC;;;;;;;sEAE1C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,SAAM;oEAAC,MAAM;oEAAI,WAAW,GAAG,oBAAoB,OAAO,SAAS,OAAO,cAAc,CAAC;;;;;;gEACzF,KAAK,QAAQ;;;;;;;;;;;;;8DAKlB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,cAAc;;;;;;8EAErC,6LAAC;oEAAI,WAAU;8EACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,aAAa;;;;;;;;;;;;sEAGtC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ,oBAAoB,OAAO,qBAAqB;;;;;;8EAEnD,6LAAC;oEAAI,WAAU;8EACZ,oBAAoB,OACjB,GAAG,KAAK,iBAAiB,CAAC,KAAK,CAAC,GAChC,GAAG,KAAK,iBAAiB,CAAC,MAAM,CAAC;;;;;;;;;;;;;;;;;;8DAM3C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,WAAU;4DACV,SAAS,CAAC;gEACR,EAAE,cAAc;gEAChB,yBAAyB;gEACzB,0BAA0B;4DAC5B;;8EAEA,6LAAC,qMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;gEACX,oBAAoB,OAAO,uBAAuB;;;;;;;sEAErD,6LAAC,+JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,CAAC,EAAE,gBAAgB,WAAW,EAAE,KAAK,EAAE,EAAE;4DAChD,WAAU;sEAET,oBAAoB,OAAO,yBAAyB;;;;;;;;;;;;;;;;;;;mCAnHnD,KAAK,EAAE;;;;;;;;;;sCA2HrB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,UAAU,CAAC;0CACzC,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;wCAET,oBAAoB,OAAO,4BAA4B;sDACxD,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAW,GAAG,oBAAoB,OAAO,oBAAoB,OAAO,oEAAoE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/J,6LAAC,oJAAA,CAAA,qBAAkB;gBACjB,QAAQ,0BAA0B,CAAC,CAAC;gBACpC,SAAS;oBACP,0BAA0B;oBAC1B,yBAAyB;gBAC3B;gBACA,iBAAiB;gBACjB,SAAS,wBAAwB,wBAAwB,yBAAyB;;;;;;0BAIpF,6LAAC;gBAAQ,WAAU;gBAA8I,KAAK;0BACpK,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,aAAa,OAAO,iBAAiB;;;;;;;8CAIxC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAK,WAAU;sDACb,aAAa,OAAO,yBAAyB;;;;;;sDAEhD,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAIjB,6LAAC;oCAAE,WAAU;8CACV,aAAa,OAAO,+GAA+G;;;;;;8CAItI,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;sCACZ,0HAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAkB;gCAC3C,MAAM,OAAO,QAAQ,IAAI,KAAK,WAAW,yMAAA,CAAA,SAAM,GACnC,QAAQ,IAAI,KAAK,YAAY,2MAAA,CAAA,UAAO,GACpC,QAAQ,IAAI,KAAK,UAAU,uMAAA,CAAA,QAAK,GAChC,QAAQ,IAAI,KAAK,cAAc,mNAAA,CAAA,YAAS,GACxC,QAAQ,IAAI,KAAK,UAAU,uMAAA,CAAA,QAAK,GAChC,QAAQ,IAAI,KAAK,kBAAkB,iNAAA,CAAA,WAAQ,GAC3C,mNAAA,CAAA,YAAS;gCAErB,qBACE,6LAAC;8CAGC,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;;8DAEd,6LAAC;oDAAI,WAAU;;;;;;8DAGf,6LAAC;oDAAI,WAAU;;;;;;8DAGf,6LAAC;oDAAI,WAAU;8DACZ,oBAAoB,OAAO,kBAAkB;;;;;;8DAGhD,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAK,MAAM;wEAAI,WAAU;;;;;;;;;;;8EAE5B,6LAAC;oEAAI,WAAU;;;;;;;;;;;;sEAIjB,6LAAC;4DAAG,WAAU;sEACX,oBAAoB,OAAO,QAAQ,OAAO,IAAI,QAAQ,IAAI,GAAG,QAAQ,IAAI;;;;;;sEAI5E,6LAAC;4DAAE,WAAU;sEACV,oBAAoB,OAAO,QAAQ,cAAc,IAAI,QAAQ,WAAW,GAAG,QAAQ,WAAW;;;;;;sEAIjG,6LAAC;4DAAI,WAAU;sEACZ,CAAC,oBAAoB,OAAO,QAAQ,WAAW,IAAI,QAAQ,QAAQ,GAAG,QAAQ,QAAQ,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAiB,uBACzH,6LAAC;oEAAiB,WAAU;;sFAC1B,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC,uNAAA,CAAA,cAAW;gFAAC,WAAW,CAAC,6CAA6C,EAAE,oBAAoB,OAAO,SAAS,OAAO,cAAc,CAAC;;;;;;;;;;;sFAEpI,6LAAC;4EAAK,WAAU;sFAAuI;;;;;;;mEAJ/I;;;;;;;;;;sEAUd,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,UAAU,EAAE,QAAQ,IAAI,EAAE;gEAAE,WAAU;0EACpE,cAAA,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;;wEAET,oBAAoB,OAAO,qBAAqB;sFACjD,6LAAC,qNAAA,CAAA,aAAU;4EAAC,WAAW,CAAC,oEAAoE,EAAE,oBAAoB,OAAO,eAAe,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAOpJ,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;mCA7Dd,QAAQ,EAAE;;;;;4BAkErB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,SAAS,CAAC;0CACxC,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;wCAET,oBAAoB,OAAO,oCAAoC;sDAChE,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAW,GAAG,oBAAoB,OAAO,SAAS,OAAO,qEAAqE,EAAE,oBAAoB,OAAO,eAAe,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpM,6LAAC;gBAAQ,WAAU;gBAA0C,KAAK;;kCAEhE,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;gCAAgB,SAAQ;gCAAc,qBAAoB;;kDACvE,6LAAC;kDACC,cAAA,6LAAC;4CAAQ,IAAG;4CAAW,GAAE;4CAAI,GAAE;4CAAI,OAAM;4CAAK,QAAO;4CAAK,cAAa;sDACrE,cAAA,6LAAC;gDAAK,GAAE;gDAAa,MAAK;gDAAO,QAAO;gDAAwB,aAAY;;;;;;;;;;;;;;;;kDAGhF,6LAAC;wCAAK,GAAE;wCAAI,GAAE;wCAAI,OAAM;wCAAM,QAAO;wCAAM,MAAK;;;;;;;;;;;;;;;;;;;;;;kCAMtD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCAEb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDAEf,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAK,WAAU;8DACb,aAAa,OAAO,gCAAgC;;;;;;;;;;;0DAKzD,6LAAC;gDAAE,WAAU;0DACV,aAAa,OACV,gFACA;;;;;;0DAIN,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yIAAA,CAAA,aAAU;wDACT,MAAM,CAAC,CAAC,EAAE,gBAAgB,KAAK,CAAC;wDAChC,SAAQ;wDACR,WAAU;;0EAEV,6LAAC;gEAAK,WAAU;0EAAiB,oBAAoB,OAAO,oBAAoB;;;;;;0EAChF,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAW,CAAC,QAAQ,EAAE,oBAAoB,OAAO,oBAAoB,OAAO,0EAA0E,CAAC;;;;;;;;;;;;kEAErK,6LAAC,yIAAA,CAAA,aAAU;wDACT,MAAM,CAAC,CAAC,EAAE,gBAAgB,QAAQ,CAAC;wDACnC,SAAQ;wDACR,WAAU;kEAEV,cAAA,6LAAC;4DAAK,WAAU;sEAAiB,oBAAoB,OAAO,gBAAgB;;;;;;;;;;;;;;;;;0DAKhF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAK,WAAU;kEACb,oBAAoB,OAAO,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU/D,6LAAC;gBAAQ,WAAU;gBAAuG,KAAK;0BAC7H,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDACnB,oBAAoB,OAAO,wBAAwB;;;;;;;0DAGtD,6LAAC;gDAAG,WAAU;0DACX,oBAAoB,OAAO,6BAA6B;;;;;;0DAE3D,6LAAC;gDAAE,WAAU;0DACV,oBAAoB,OACjB,8EACA;;;;;;0DAIN,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAGjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,KAAK,CAAC;0DACpC,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;;wDAET,oBAAoB,OAAO,kBAAkB;sEAC9C,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAW,CAAC,QAAQ,EAAE,oBAAoB,OAAO,eAAe,IAAI;;;;;;;;;;;;;;;;;0DAGpF,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,cAAc,CAAC;0DAC7C,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;;wDAET,oBAAoB,OAAO,qBAAqB;sEACjD,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAW,CAAC,QAAQ,EAAE,oBAAoB,OAAO,eAAe,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ1F,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;;;;;8CAGf,6LAAC;oCAAI,WAAU;;;;;;8CAGf,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ,CAAC;4CACA,yBAAyB;4CACzB,MAAM,gBAAgB,2HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;4CAE5D,wBAAwB;4CACxB,MAAM,cAAc,2HAAA,CAAA,YAAS,CAC1B,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,IAClF,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ;4CAEhC,oDAAoD;4CACpD,MAAM,cAAc;mDAAI;mDAAkB;6CAAY,CAAC,KAAK,CAAC,GAAG;4CAEhE,OAAO,YAAY,GAAG,CAAC,CAAC,MAAM;gDAC5B,MAAM,aAAa,KAAK,QAAQ;gDAEhC,qBACE,6LAAC;oDAAkB,WAAU;;sEAE3B,6LAAC;4DAAI,WAAU;;gEAEZ,4BACC,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAK,WAAU;;0FACd,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;gFAAe,MAAK;;;;;;4EACnC,oBAAoB,OAAO,SAAS;;;;;;;;;;;;8EAM3C,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,MAAM,EAAE,KAAK,IAAI,EAAE;oEAAE,WAAU;8EAC7D,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,4IAAA,CAAA,gBAAa;gFACZ,KAAK,KAAK,UAAU;gFACpB,KAAK,KAAK,KAAK;gFACf,MAAM;gFACN,WAAU;gFACV,OAAM;gFACN,aAAa;gFACb,aAAY;gFACZ,WAAU;;;;;;0FAEZ,6LAAC;gFAAI,WAAU;;;;;;0FAGf,6LAAC;gFAAI,WAAU;0FACb,cAAA,6LAAC;oFAAK,WAAU;8FACb,KAAK,QAAQ;;;;;;;;;;;0FAKlB,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAI,WAAU;;0GACb,6LAAC,6MAAA,CAAA,WAAQ;gGAAC,WAAU;;;;;;4FACnB,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,CAAC,oBAAoB,OAAO,UAAU,SAAS;gGAC3F,MAAM;gGACN,OAAO;gGACP,KAAK;4FACP;;;;;;;kGAEF,6LAAC;wFAAI,WAAU;;0GACb,6LAAC,uMAAA,CAAA,QAAK;gGAAC,WAAU;;;;;;4FAChB,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;8EAOtB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,+JAAA,CAAA,UAAI;4EAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,MAAM,EAAE,KAAK,IAAI,EAAE;4EAAE,WAAU;sFAC7D,cAAA,6LAAC;gFAAG,WAAU;0FACX,KAAK,KAAK;;;;;;;;;;;sFAIf,6LAAC;4EAAE,WAAU;sFACV,KAAK,OAAO;;;;;;sFAIf,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;;sGACb,6LAAC;4FAAI,WAAU;sGACb,cAAA,6LAAC,4IAAA,CAAA,gBAAa;gGACZ,KAAK,KAAK,WAAW;gGACrB,KAAK,KAAK,MAAM;gGAChB,MAAM;gGACN,WAAU;gGACV,SAAQ;gGACR,aAAa;gGACb,aAAY;gGACZ,WAAU;gGACV,oBAAmB;;;;;;;;;;;sGAGvB,6LAAC;;8GACC,6LAAC;oGAAE,WAAU;8GACV,KAAK,MAAM;;;;;;8GAEd,6LAAC;oGAAE,WAAU;8GACV,KAAK,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;;;;;;;8FAMrC,6LAAC,+JAAA,CAAA,UAAI;oFAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,MAAM,EAAE,KAAK,IAAI,EAAE;oFAAE,WAAU;8FAC7D,cAAA,6LAAC,qNAAA,CAAA,aAAU;wFAAC,WAAW,CAAC,QAAQ,EAAE,oBAAoB,OAAO,eAAe,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAOxF,6LAAC;4DAAI,WAAU;;;;;;;mDApGP,KAAK,EAAE;;;;;4CAuGrB;wCACF,CAAC;;;;;;;;;;;;;;;;;sCAKP,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,KAAK,CAAC;0CACpC,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;wCAET,oBAAoB,OAAO,0BAA0B;sDACtD,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAW,GAAG,oBAAoB,OAAO,oBAAoB,OAAO,oEAAoE,CAAC;;;;;;;;;;;;;;;;;;;;;;sCAM3J,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CAGf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CAEf,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEACf,oBAAoB,OAAO,kBAAkB;;;;;;;sEAEhD,6LAAC;4DAAG,WAAU;sEACX,oBAAoB,OACjB,uDACA;;;;;;sEAEN,6LAAC;4DAAE,WAAU;sEACV,oBAAoB,OACjB,2FACA;;;;;;;;;;;;8DAIR,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,gJAAA,CAAA,iBAAc;wDACb,SAAQ;wDACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUzB,mCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,oJAAA,CAAA,qBAAkB;wBACjB,SAAS;4BACP,qBAAqB;4BACrB,mBAAmB;4BACnB,mBAAmB;wBACrB;wBACA,iBAAiB;wBACjB,SAAS;wBACT,aAAa,iBAAiB;;;;;;;;;;;;;;;;;;;;;;AAQ5C;GAtoDS;;QACY,+HAAA,CAAA,iBAAc;QACD,iIAAA,CAAA,mBAAgB;QAClC,+HAAA,CAAA,iBAAc;QA6BN,kIAAA,CAAA,oBAAiB;QACtB,6HAAA,CAAA,eAAY;QAuKW,uJAAA,CAAA,eAAY;;;KAxM7C;uCAwoDM", "debugId": null}}, {"offset": {"line": 8042, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/app/%5Blocale%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { Suspense } from 'react';\nimport HomePage from '../../pages/HomePage';\n\n// Loading fallback component\nconst LoadingFallback = () => (\n  <div className=\"min-h-screen flex items-center justify-center\">\n    <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500\"></div>\n  </div>\n);\n\nexport default function Home() {\n  return (\n    <Suspense fallback={<LoadingFallback />}>\n      <HomePage />\n    </Suspense>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,6BAA6B;AAC7B,MAAM,kBAAkB,kBACtB,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;;;;;;;;;;KAFb;AAMS,SAAS;IACtB,qBACE,6LAAC,6JAAA,CAAA,WAAQ;QAAC,wBAAU,6LAAC;;;;;kBACnB,cAAA,6LAAC,4HAAA,CAAA,UAAQ;;;;;;;;;;AAGf;MANwB", "debugId": null}}]}