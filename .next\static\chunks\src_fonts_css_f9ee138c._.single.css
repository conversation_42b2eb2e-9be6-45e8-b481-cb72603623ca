/* [project]/src/fonts.css [app-client] (css) */
[dir="rtl"] h1, [dir="rtl"] h2, [dir="rtl"] h3, [dir="rtl"] h4, [dir="rtl"] h5, [dir="rtl"] h6 {
  letter-spacing: -.025em;
  line-height: 1.5;
}

[dir="rtl"] {
  font-feature-settings: "calt" 1, "clig" 1, "dlig" 1, "kern" 1, "liga" 1, "salt" 1;
}

[dir="rtl"] p, [dir="rtl"] span, [dir="rtl"] div, [dir="rtl"] button, [dir="rtl"] a {
  letter-spacing: 0;
  word-spacing: .05em;
  line-height: 1.8;
}

[dir="rtl"] input, [dir="rtl"] textarea {
  text-align: right;
}

[dir="rtl"] .ml-2 {
  margin-left: 0;
  margin-right: .5rem;
}

[dir="rtl"] .mr-2 {
  margin-left: .5rem;
  margin-right: 0;
}

[dir="rtl"] .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .mr-4 {
  margin-left: 1rem;
  margin-right: 0;
}

[dir="rtl"] .rotate-rtl {
  transform: rotate(180deg);
}

/*# sourceMappingURL=src_fonts_css_f9ee138c._.single.css.map*/