{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/fonts.css"], "sourcesContent": ["/* تحسين عرض الخط العربي */\n[dir=\"rtl\"] h1,\n[dir=\"rtl\"] h2,\n[dir=\"rtl\"] h3,\n[dir=\"rtl\"] h4,\n[dir=\"rtl\"] h5,\n[dir=\"rtl\"] h6 {\n  letter-spacing: -0.025em;\n  line-height: 1.5;\n}\n\n/* تحسين حجم الخط للعربية */\n[dir=\"rtl\"] {\n  font-feature-settings: \"calt\" 1, \"clig\" 1, \"dlig\" 1, \"kern\" 1, \"liga\" 1, \"salt\" 1;\n}\n\n/* تحسين المسافات بين الأحرف العربية */\n[dir=\"rtl\"] p,\n[dir=\"rtl\"] span,\n[dir=\"rtl\"] div,\n[dir=\"rtl\"] button,\n[dir=\"rtl\"] a {\n  letter-spacing: 0;\n  word-spacing: 0.05em;\n  line-height: 1.8;\n}\n\n/* تحسين اتجاه النص في المدخلات */\n[dir=\"rtl\"] input,\n[dir=\"rtl\"] textarea {\n  text-align: right;\n}\n\n/* تحسين هوامش العناصر في وضع RTL */\n[dir=\"rtl\"] .ml-2 {\n  margin-left: 0;\n  margin-right: 0.5rem;\n}\n\n[dir=\"rtl\"] .mr-2 {\n  margin-right: 0;\n  margin-left: 0.5rem;\n}\n\n[dir=\"rtl\"] .ml-4 {\n  margin-left: 0;\n  margin-right: 1rem;\n}\n\n[dir=\"rtl\"] .mr-4 {\n  margin-right: 0;\n  margin-left: 1rem;\n}\n\n/* تحسين أيقونات الأسهم في وضع RTL */\n[dir=\"rtl\"] .rotate-rtl {\n  transform: rotate(180deg);\n}\n"], "names": [], "mappings": "AACA;;;;;AAWA;;;;AAKA;;;;;;AAWA;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAMA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/index.css"], "sourcesContent": ["/* استيراد ملف الخطوط */\n@import './fonts.css';\n\n*, ::before, ::after{\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n\n::backdrop{\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n\n/* ! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com */\n\n/*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\n\n::before,\n::after {\n  --tw-content: '';\n}\n\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\n\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  -o-tab-size: 4;\n     tab-size: 4; /* 3 */\n  font-family: var(--font-inter), ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\n\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\n\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\n\nabbr:where([title]) {\n  -webkit-text-decoration: underline dotted;\n          text-decoration: underline dotted;\n}\n\n/*\nRemove the default font size and weight for headings.\n*/\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\n\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n\n/*\nAdd the correct font weight in Edge and Safari.\n*/\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\n\ncode,\nkbd,\nsamp,\npre {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n\n/*\nAdd the correct font size in all browsers.\n*/\n\nsmall {\n  font-size: 80%;\n}\n\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\n\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\n\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n\n:-moz-focusring {\n  outline: auto;\n}\n\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\n\nprogress {\n  vertical-align: baseline;\n}\n\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/*\nAdd the correct display in Chrome and Safari.\n*/\n\nsummary {\n  display: list-item;\n}\n\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\n\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\n\nfieldset {\n  margin: 0;\n  padding: 0;\n}\n\nlegend {\n  padding: 0;\n}\n\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n/*\nReset default styling for dialogs.\n*/\n\ndialog {\n  padding: 0;\n}\n\n/*\nPrevent resizing textareas horizontally by default.\n*/\n\ntextarea {\n  resize: vertical;\n}\n\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\n\ninput::-moz-placeholder, textarea::-moz-placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\n/*\nSet the default cursor for buttons.\n*/\n\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\n\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n\n:disabled {\n  cursor: default;\n}\n\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\n\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\n\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n\n/* Make elements with the HTML hidden attribute stay hidden by default */\n\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\n\n:root {\n    /* أرجواني/وردي - اللون الأساسي من الشعار */\n    --primary-50: 250 245 255;\n    --primary-100: 243 232 255;\n    --primary-200: 233 213 255;\n    --primary-300: 216 180 254;\n    --primary-400: 192 132 252;\n    --primary-500: 168 85 247;\n    --primary-600: 147 51 234;\n    --primary-700: 126 34 206;\n    --primary-800: 107 33 168;\n    --primary-900: 88 28 135;\n    --primary-950: 59 7 100;\n\n    /* أزرق/سماوي - اللون الثانوي من الشعار */\n    --secondary-50: 236 254 255;\n    --secondary-100: 207 250 254;\n    --secondary-200: 165 243 252;\n    --secondary-300: 103 232 249;\n    --secondary-400: 34 211 238;\n    --secondary-500: 6 182 212;\n    --secondary-600: 8 145 178;\n    --secondary-700: 14 116 144;\n    --secondary-800: 21 94 117;\n    --secondary-900: 22 78 99;\n    --secondary-950: 8 51 68;\n\n    /* برتقالي/أصفر - اللون الثالث من الشعار */\n    --accent-50: 255 247 237;\n    --accent-100: 255 237 213;\n    --accent-200: 254 215 170;\n    --accent-300: 253 186 116;\n    --accent-400: 251 146 60;\n    --accent-500: 249 115 22;\n    --accent-600: 234 88 12;\n    --accent-700: 194 65 12;\n    --accent-800: 154 52 18;\n    --accent-900: 124 45 18;\n    --accent-950: 67 20 7;\n\n    --success-50: 240 253 244;\n    --success-100: 220 252 231;\n    --success-500: 34 197 94;\n    --success-600: 22 163 74;\n    --success-700: 21 128 61;\n\n    --error-50: 254 242 242;\n    --error-100: 254 226 226;\n    --error-500: 239 68 68;\n    --error-600: 220 38 38;\n    --error-700: 185 28 28;\n  }\n\nhtml{\n  scroll-behavior: smooth;\n}\n\nbody{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n  font-family: var(--font-inter), ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  --tw-text-opacity: 1;\n  color: rgb(15 23 42 / var(--tw-text-opacity, 1));\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.dark body{\n  --tw-bg-opacity: 1;\n  background-color: rgb(2 6 23 / var(--tw-bg-opacity, 1));\n  --tw-text-opacity: 1;\n  color: rgb(241 245 249 / var(--tw-text-opacity, 1));\n}\n\n/* تحسينات للأجهزة المحمولة */\n\n@media (max-width: 767px) {\n    html {\n      font-size: 15px;\n    }\n\n    .dark html {\n      font-size: 14px;\n    }\n\n    body{\n    touch-action: manipulation;\n  }\n  }\n\nh1, h2, h3, h4, h5, h6{\n  font-weight: 600;\n  letter-spacing: -0.025em;\n}\n\nh1{\n  font-size: 2.25rem;\n  line-height: 2.5rem;\n}\n\n@media (min-width: 768px){\n\n  h1{\n    font-size: 3rem;\n    line-height: 1;\n  }\n}\n\n@media (min-width: 1024px){\n\n  h1{\n    font-size: 3.75rem;\n    line-height: 1;\n  }\n}\n\nh2{\n  font-size: 1.875rem;\n  line-height: 2.25rem;\n}\n\n@media (min-width: 768px){\n\n  h2{\n    font-size: 2.25rem;\n    line-height: 2.5rem;\n  }\n}\n\n@media (min-width: 1024px){\n\n  h2{\n    font-size: 3rem;\n    line-height: 1;\n  }\n}\n\nh3{\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\n\n@media (min-width: 768px){\n\n  h3{\n    font-size: 1.875rem;\n    line-height: 2.25rem;\n  }\n}\n\nh4{\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\n\n@media (min-width: 768px){\n\n  h4{\n    font-size: 1.5rem;\n    line-height: 2rem;\n  }\n}\n\nh5{\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\n\n@media (min-width: 768px){\n\n  h5{\n    font-size: 1.25rem;\n    line-height: 1.75rem;\n  }\n}\n\nh6{\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\n\n@media (min-width: 768px){\n\n  h6{\n    font-size: 1.125rem;\n    line-height: 1.75rem;\n  }\n}\n\na{\n  --tw-text-opacity: 1;\n  color: rgb(168 85 247 / var(--tw-text-opacity, 1));\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 200ms;\n}\n\na:hover{\n  --tw-text-opacity: 1;\n  color: rgb(147 51 234 / var(--tw-text-opacity, 1));\n}\n\n.dark a{\n  --tw-text-opacity: 1;\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\n}\n\n.dark a:hover{\n  --tw-text-opacity: 1;\n  color: rgb(216 180 254 / var(--tw-text-opacity, 1));\n}\n.container{\n  width: 100%;\n}\n@media (min-width: 640px){\n\n  .container{\n    max-width: 640px;\n  }\n}\n@media (min-width: 768px){\n\n  .container{\n    max-width: 768px;\n  }\n}\n@media (min-width: 1024px){\n\n  .container{\n    max-width: 1024px;\n  }\n}\n@media (min-width: 1280px){\n\n  .container{\n    max-width: 1280px;\n  }\n}\n@media (min-width: 1536px){\n\n  .container{\n    max-width: 1536px;\n  }\n}\n.dark .btn-primary{\n  --tw-bg-opacity: 1;\n  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));\n}\n.dark .btn-primary:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));\n}\n.dark .btn-primary:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(192 132 252 / var(--tw-ring-opacity, 1));\n}\n.dark .btn-secondary{\n  --tw-bg-opacity: 1;\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\n  --tw-text-opacity: 1;\n  color: rgb(241 245 249 / var(--tw-text-opacity, 1));\n}\n.dark .btn-secondary:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));\n}\n.dark .btn-secondary:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(148 163 184 / var(--tw-ring-opacity, 1));\n}\n.dark .btn-accent{\n  --tw-bg-opacity: 1;\n  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));\n}\n.dark .btn-accent:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(194 65 12 / var(--tw-bg-opacity, 1));\n}\n.dark .btn-accent:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(251 146 60 / var(--tw-ring-opacity, 1));\n}\n/* تأثير التحويم للأزرار - تم إصلاح مشكلة اختفاء النص */\n.dark .btn-primary::after,\n  .dark .btn-accent::after{\n  z-index: -10;\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n  opacity: 0;\n}\n.dark .btn-primary:hover::after,\n  .dark .btn-accent:hover::after{\n  opacity: 0.2;\n}\n.container-custom{\n  margin-left: auto;\n  margin-right: auto;\n  max-width: 90rem;\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\n@media (min-width: 640px){\n\n  .container-custom{\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\n}\n@media (min-width: 1024px){\n\n  .container-custom{\n    padding-left: 2rem;\n    padding-right: 2rem;\n  }\n}\n.container-custom { /* زيادة العرض من 80rem (1280px) إلى 90rem (1440px) */\n  }\n.section{\n  padding-top: 3rem;\n  padding-bottom: 3rem;\n}\n@media (min-width: 768px){\n\n  .section{\n    padding-top: 4rem;\n    padding-bottom: 4rem;\n  }\n}\n@media (min-width: 1024px){\n\n  .section{\n    padding-top: 6rem;\n    padding-bottom: 6rem;\n  }\n}\n.\\!card{\n  overflow: hidden;\n  border-radius: 0.5rem;\n  border-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 300ms;\n}\n.\\!card:hover{\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.card{\n  overflow: hidden;\n  border-radius: 0.5rem;\n  border-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 300ms;\n}\n.card:hover{\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.dark .card{\n  --tw-border-opacity: 1;\n  border-color: rgb(30 41 59 / var(--tw-border-opacity, 1));\n  --tw-bg-opacity: 1;\n  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.dark .card:hover{\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.dark .\\!card{\n  --tw-border-opacity: 1;\n  border-color: rgb(30 41 59 / var(--tw-border-opacity, 1));\n  --tw-bg-opacity: 1;\n  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.dark .\\!card:hover{\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.sr-only{\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n.pointer-events-none{\n  pointer-events: none;\n}\n.visible{\n  visibility: visible;\n}\n.invisible{\n  visibility: hidden;\n}\n.static{\n  position: static;\n}\n.fixed{\n  position: fixed;\n}\n.absolute{\n  position: absolute;\n}\n.relative{\n  position: relative;\n}\n.sticky{\n  position: sticky;\n}\n.-inset-10{\n  inset: -2.5rem;\n}\n.-inset-2{\n  inset: -0.5rem;\n}\n.-inset-4{\n  inset: -1rem;\n}\n.inset-0{\n  inset: 0px;\n}\n.inset-y-0{\n  top: 0px;\n  bottom: 0px;\n}\n.-bottom-1{\n  bottom: -0.25rem;\n}\n.-bottom-2{\n  bottom: -0.5rem;\n}\n.-bottom-20{\n  bottom: -5rem;\n}\n.-bottom-24{\n  bottom: -6rem;\n}\n.-bottom-4{\n  bottom: -1rem;\n}\n.-bottom-40{\n  bottom: -10rem;\n}\n.-bottom-5{\n  bottom: -1.25rem;\n}\n.-left-20{\n  left: -5rem;\n}\n.-left-24{\n  left: -6rem;\n}\n.-left-3{\n  left: -0.75rem;\n}\n.-left-40{\n  left: -10rem;\n}\n.-left-5{\n  left: -1.25rem;\n}\n.-right-1{\n  right: -0.25rem;\n}\n.-right-2{\n  right: -0.5rem;\n}\n.-right-20{\n  right: -5rem;\n}\n.-right-24{\n  right: -6rem;\n}\n.-right-3{\n  right: -0.75rem;\n}\n.-right-40{\n  right: -10rem;\n}\n.-right-5{\n  right: -1.25rem;\n}\n.-top-1{\n  top: -0.25rem;\n}\n.-top-10{\n  top: -2.5rem;\n}\n.-top-2{\n  top: -0.5rem;\n}\n.-top-20{\n  top: -5rem;\n}\n.-top-24{\n  top: -6rem;\n}\n.-top-4{\n  top: -1rem;\n}\n.-top-40{\n  top: -10rem;\n}\n.-top-5{\n  top: -1.25rem;\n}\n.bottom-0{\n  bottom: 0px;\n}\n.bottom-16{\n  bottom: 4rem;\n}\n.bottom-2{\n  bottom: 0.5rem;\n}\n.bottom-24{\n  bottom: 6rem;\n}\n.bottom-3{\n  bottom: 0.75rem;\n}\n.bottom-4{\n  bottom: 1rem;\n}\n.bottom-6{\n  bottom: 1.5rem;\n}\n.bottom-8{\n  bottom: 2rem;\n}\n.left-0{\n  left: 0px;\n}\n.left-1\\/2{\n  left: 50%;\n}\n.left-1\\/4{\n  left: 25%;\n}\n.left-2{\n  left: 0.5rem;\n}\n.left-3{\n  left: 0.75rem;\n}\n.left-4{\n  left: 1rem;\n}\n.left-6{\n  left: 1.5rem;\n}\n.left-\\[-10000px\\]{\n  left: -10000px;\n}\n.left-\\[50\\%\\]{\n  left: 50%;\n}\n.left-auto{\n  left: auto;\n}\n.left-full{\n  left: 100%;\n}\n.right-0{\n  right: 0px;\n}\n.right-1{\n  right: 0.25rem;\n}\n.right-1\\/4{\n  right: 25%;\n}\n.right-2{\n  right: 0.5rem;\n}\n.right-3{\n  right: 0.75rem;\n}\n.right-4{\n  right: 1rem;\n}\n.right-6{\n  right: 1.5rem;\n}\n.top-0{\n  top: 0px;\n}\n.top-1{\n  top: 0.25rem;\n}\n.top-1\\/2{\n  top: 50%;\n}\n.top-10{\n  top: 2.5rem;\n}\n.top-12{\n  top: 3rem;\n}\n.top-2{\n  top: 0.5rem;\n}\n.top-24{\n  top: 6rem;\n}\n.top-3{\n  top: 0.75rem;\n}\n.top-4{\n  top: 1rem;\n}\n.top-5{\n  top: 1.25rem;\n}\n.top-6{\n  top: 1.5rem;\n}\n.top-8{\n  top: 2rem;\n}\n.top-\\[50\\%\\]{\n  top: 50%;\n}\n.top-auto{\n  top: auto;\n}\n.-z-0{\n  z-index: 0;\n}\n.-z-10{\n  z-index: -10;\n}\n.z-0{\n  z-index: 0;\n}\n.z-10{\n  z-index: 10;\n}\n.z-20{\n  z-index: 20;\n}\n.z-30{\n  z-index: 30;\n}\n.z-40{\n  z-index: 40;\n}\n.z-50{\n  z-index: 50;\n}\n.z-\\[60\\]{\n  z-index: 60;\n}\n.col-span-1{\n  grid-column: span 1 / span 1;\n}\n.col-span-2{\n  grid-column: span 2 / span 2;\n}\n.col-span-5{\n  grid-column: span 5 / span 5;\n}\n.m-auto{\n  margin: auto;\n}\n.mx-2{\n  margin-left: 0.5rem;\n  margin-right: 0.5rem;\n}\n.mx-3{\n  margin-left: 0.75rem;\n  margin-right: 0.75rem;\n}\n.mx-4{\n  margin-left: 1rem;\n  margin-right: 1rem;\n}\n.mx-6{\n  margin-left: 1.5rem;\n  margin-right: 1.5rem;\n}\n.mx-auto{\n  margin-left: auto;\n  margin-right: auto;\n}\n.my-4{\n  margin-top: 1rem;\n  margin-bottom: 1rem;\n}\n.my-6{\n  margin-top: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n.my-8{\n  margin-top: 2rem;\n  margin-bottom: 2rem;\n}\n.-ml-1{\n  margin-left: -0.25rem;\n}\n.mb-0\\.5{\n  margin-bottom: 0.125rem;\n}\n.mb-1{\n  margin-bottom: 0.25rem;\n}\n.mb-1\\.5{\n  margin-bottom: 0.375rem;\n}\n.mb-10{\n  margin-bottom: 2.5rem;\n}\n.mb-12{\n  margin-bottom: 3rem;\n}\n.mb-16{\n  margin-bottom: 4rem;\n}\n.mb-2{\n  margin-bottom: 0.5rem;\n}\n.mb-20{\n  margin-bottom: 5rem;\n}\n.mb-3{\n  margin-bottom: 0.75rem;\n}\n.mb-4{\n  margin-bottom: 1rem;\n}\n.mb-5{\n  margin-bottom: 1.25rem;\n}\n.mb-6{\n  margin-bottom: 1.5rem;\n}\n.mb-8{\n  margin-bottom: 2rem;\n}\n.me-1{\n  margin-inline-end: 0.25rem;\n}\n.me-2{\n  margin-inline-end: 0.5rem;\n}\n.me-3{\n  margin-inline-end: 0.75rem;\n}\n.me-4{\n  margin-inline-end: 1rem;\n}\n.me-6{\n  margin-inline-end: 1.5rem;\n}\n.me-8{\n  margin-inline-end: 2rem;\n}\n.ml-1{\n  margin-left: 0.25rem;\n}\n.ml-2{\n  margin-left: 0.5rem;\n}\n.ml-3{\n  margin-left: 0.75rem;\n}\n.ml-4{\n  margin-left: 1rem;\n}\n.ml-7{\n  margin-left: 1.75rem;\n}\n.ml-auto{\n  margin-left: auto;\n}\n.mr-0{\n  margin-right: 0px;\n}\n.mr-0\\.5{\n  margin-right: 0.125rem;\n}\n.mr-1{\n  margin-right: 0.25rem;\n}\n.mr-1\\.5{\n  margin-right: 0.375rem;\n}\n.mr-2{\n  margin-right: 0.5rem;\n}\n.mr-3{\n  margin-right: 0.75rem;\n}\n.mr-4{\n  margin-right: 1rem;\n}\n.mr-6{\n  margin-right: 1.5rem;\n}\n.ms-1{\n  margin-inline-start: 0.25rem;\n}\n.ms-2{\n  margin-inline-start: 0.5rem;\n}\n.ms-3{\n  margin-inline-start: 0.75rem;\n}\n.ms-4{\n  margin-inline-start: 1rem;\n}\n.ms-6{\n  margin-inline-start: 1.5rem;\n}\n.ms-8{\n  margin-inline-start: 2rem;\n}\n.mt-0\\.5{\n  margin-top: 0.125rem;\n}\n.mt-1{\n  margin-top: 0.25rem;\n}\n.mt-1\\.5{\n  margin-top: 0.375rem;\n}\n.mt-12{\n  margin-top: 3rem;\n}\n.mt-16{\n  margin-top: 4rem;\n}\n.mt-2{\n  margin-top: 0.5rem;\n}\n.mt-20{\n  margin-top: 5rem;\n}\n.mt-24{\n  margin-top: 6rem;\n}\n.mt-3{\n  margin-top: 0.75rem;\n}\n.mt-4{\n  margin-top: 1rem;\n}\n.mt-5{\n  margin-top: 1.25rem;\n}\n.mt-6{\n  margin-top: 1.5rem;\n}\n.mt-8{\n  margin-top: 2rem;\n}\n.mt-auto{\n  margin-top: auto;\n}\n.line-clamp-1{\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n}\n.line-clamp-2{\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n}\n.line-clamp-3{\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 3;\n}\n.block{\n  display: block;\n}\n.inline-block{\n  display: inline-block;\n}\n.inline{\n  display: inline;\n}\n.flex{\n  display: flex;\n}\n.inline-flex{\n  display: inline-flex;\n}\n.table{\n  display: table;\n}\n.grid{\n  display: grid;\n}\n.hidden{\n  display: none;\n}\n.aspect-\\[16\\/9\\]{\n  aspect-ratio: 16/9;\n}\n.aspect-\\[21\\/9\\]{\n  aspect-ratio: 21/9;\n}\n.aspect-\\[4\\/3\\]{\n  aspect-ratio: 4/3;\n}\n.aspect-square{\n  aspect-ratio: 1 / 1;\n}\n.aspect-video{\n  aspect-ratio: 16 / 9;\n}\n.h-0\\.5{\n  height: 0.125rem;\n}\n.h-1{\n  height: 0.25rem;\n}\n.h-1\\.5{\n  height: 0.375rem;\n}\n.h-10{\n  height: 2.5rem;\n}\n.h-11{\n  height: 2.75rem;\n}\n.h-12{\n  height: 3rem;\n}\n.h-14{\n  height: 3.5rem;\n}\n.h-16{\n  height: 4rem;\n}\n.h-2{\n  height: 0.5rem;\n}\n.h-2\\.5{\n  height: 0.625rem;\n}\n.h-20{\n  height: 5rem;\n}\n.h-24{\n  height: 6rem;\n}\n.h-3{\n  height: 0.75rem;\n}\n.h-3\\.5{\n  height: 0.875rem;\n}\n.h-32{\n  height: 8rem;\n}\n.h-4{\n  height: 1rem;\n}\n.h-40{\n  height: 10rem;\n}\n.h-48{\n  height: 12rem;\n}\n.h-5{\n  height: 1.25rem;\n}\n.h-52{\n  height: 13rem;\n}\n.h-6{\n  height: 1.5rem;\n}\n.h-64{\n  height: 16rem;\n}\n.h-7{\n  height: 1.75rem;\n}\n.h-8{\n  height: 2rem;\n}\n.h-80{\n  height: 20rem;\n}\n.h-9{\n  height: 2.25rem;\n}\n.h-96{\n  height: 24rem;\n}\n.h-\\[500px\\]{\n  height: 500px;\n}\n.h-\\[90vh\\]{\n  height: 90vh;\n}\n.h-auto{\n  height: auto;\n}\n.h-full{\n  height: 100%;\n}\n.max-h-0{\n  max-height: 0px;\n}\n.max-h-40{\n  max-height: 10rem;\n}\n.max-h-60{\n  max-height: 15rem;\n}\n.max-h-80{\n  max-height: 20rem;\n}\n.max-h-\\[2000px\\]{\n  max-height: 2000px;\n}\n.max-h-\\[60vh\\]{\n  max-height: 60vh;\n}\n.max-h-\\[80vh\\]{\n  max-height: 80vh;\n}\n.max-h-\\[90vh\\]{\n  max-height: 90vh;\n}\n.max-h-\\[calc\\(90vh-200px\\)\\]{\n  max-height: calc(90vh - 200px);\n}\n.max-h-full{\n  max-height: 100%;\n}\n.min-h-\\[100px\\]{\n  min-height: 100px;\n}\n.min-h-\\[120px\\]{\n  min-height: 120px;\n}\n.min-h-\\[150px\\]{\n  min-height: 150px;\n}\n.min-h-\\[160px\\]{\n  min-height: 160px;\n}\n.min-h-\\[200px\\]{\n  min-height: 200px;\n}\n.min-h-\\[400px\\]{\n  min-height: 400px;\n}\n.min-h-\\[44px\\]{\n  min-height: 44px;\n}\n.min-h-\\[80vh\\]{\n  min-height: 80vh;\n}\n.min-h-screen{\n  min-height: 100vh;\n}\n.w-0{\n  width: 0px;\n}\n.w-0\\.5{\n  width: 0.125rem;\n}\n.w-1{\n  width: 0.25rem;\n}\n.w-1\\.5{\n  width: 0.375rem;\n}\n.w-1\\/2{\n  width: 50%;\n}\n.w-1\\/3{\n  width: 33.333333%;\n}\n.w-1\\/4{\n  width: 25%;\n}\n.w-1\\/5{\n  width: 20%;\n}\n.w-10{\n  width: 2.5rem;\n}\n.w-12{\n  width: 3rem;\n}\n.w-14{\n  width: 3.5rem;\n}\n.w-16{\n  width: 4rem;\n}\n.w-2{\n  width: 0.5rem;\n}\n.w-2\\.5{\n  width: 0.625rem;\n}\n.w-2\\/3{\n  width: 66.666667%;\n}\n.w-20{\n  width: 5rem;\n}\n.w-24{\n  width: 6rem;\n}\n.w-3{\n  width: 0.75rem;\n}\n.w-3\\.5{\n  width: 0.875rem;\n}\n.w-3\\/4{\n  width: 75%;\n}\n.w-32{\n  width: 8rem;\n}\n.w-4{\n  width: 1rem;\n}\n.w-4\\/6{\n  width: 66.666667%;\n}\n.w-40{\n  width: 10rem;\n}\n.w-48{\n  width: 12rem;\n}\n.w-5{\n  width: 1.25rem;\n}\n.w-5\\/6{\n  width: 83.333333%;\n}\n.w-56{\n  width: 14rem;\n}\n.w-6{\n  width: 1.5rem;\n}\n.w-64{\n  width: 16rem;\n}\n.w-7{\n  width: 1.75rem;\n}\n.w-8{\n  width: 2rem;\n}\n.w-80{\n  width: 20rem;\n}\n.w-9{\n  width: 2.25rem;\n}\n.w-96{\n  width: 24rem;\n}\n.w-\\[280px\\]{\n  width: 280px;\n}\n.w-auto{\n  width: auto;\n}\n.w-fit{\n  width: -moz-fit-content;\n  width: fit-content;\n}\n.w-full{\n  width: 100%;\n}\n.min-w-0{\n  min-width: 0px;\n}\n.min-w-\\[160px\\]{\n  min-width: 160px;\n}\n.min-w-\\[180px\\]{\n  min-width: 180px;\n}\n.min-w-\\[2rem\\]{\n  min-width: 2rem;\n}\n.min-w-\\[3rem\\]{\n  min-width: 3rem;\n}\n.min-w-\\[40px\\]{\n  min-width: 40px;\n}\n.min-w-\\[44px\\]{\n  min-width: 44px;\n}\n.min-w-full{\n  min-width: 100%;\n}\n.max-w-2xl{\n  max-width: 42rem;\n}\n.max-w-3xl{\n  max-width: 48rem;\n}\n.max-w-4xl{\n  max-width: 56rem;\n}\n.max-w-6xl{\n  max-width: 72rem;\n}\n.max-w-7xl{\n  max-width: 80rem;\n}\n.max-w-\\[100px\\]{\n  max-width: 100px;\n}\n.max-w-\\[200px\\]{\n  max-width: 200px;\n}\n.max-w-\\[80\\%\\]{\n  max-width: 80%;\n}\n.max-w-full{\n  max-width: 100%;\n}\n.max-w-lg{\n  max-width: 32rem;\n}\n.max-w-md{\n  max-width: 28rem;\n}\n.max-w-none{\n  max-width: none;\n}\n.max-w-sm{\n  max-width: 24rem;\n}\n.max-w-xs{\n  max-width: 20rem;\n}\n.flex-1{\n  flex: 1 1 0%;\n}\n.flex-shrink-0{\n  flex-shrink: 0;\n}\n.flex-grow{\n  flex-grow: 1;\n}\n.grow{\n  flex-grow: 1;\n}\n.origin-center{\n  transform-origin: center;\n}\n.origin-left{\n  transform-origin: left;\n}\n.origin-right{\n  transform-origin: right;\n}\n.origin-top-left{\n  transform-origin: top left;\n}\n.origin-top-right{\n  transform-origin: top right;\n}\n.-translate-x-1\\/2{\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-x-32{\n  --tw-translate-x: -8rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-x-48{\n  --tw-translate-x: -12rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-x-full{\n  --tw-translate-x: -100%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-y-1\\/2{\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-y-32{\n  --tw-translate-y: -8rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-y-48{\n  --tw-translate-y: -12rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-y-full{\n  --tw-translate-y: -100%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-0{\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-32{\n  --tw-translate-x: 8rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-4{\n  --tw-translate-x: 1rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-48{\n  --tw-translate-x: 12rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-\\[-50\\%\\]{\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-full{\n  --tw-translate-x: 100%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-y-0{\n  --tw-translate-y: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-y-2{\n  --tw-translate-y: 0.5rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-y-32{\n  --tw-translate-y: 8rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-y-48{\n  --tw-translate-y: 12rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-y-\\[-50\\%\\]{\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rotate-180{\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-skew-y-1{\n  --tw-skew-y: -1deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-100{\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-105{\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-110{\n  --tw-scale-x: 1.1;\n  --tw-scale-y: 1.1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-50{\n  --tw-scale-x: .5;\n  --tw-scale-y: .5;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-95{\n  --tw-scale-x: .95;\n  --tw-scale-y: .95;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-x-0{\n  --tw-scale-x: 0;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-x-100{\n  --tw-scale-x: 1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.transform{\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.transform-gpu{\n  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n@keyframes bounce{\n\n  0%, 100%{\n    transform: translateY(-25%);\n    animation-timing-function: cubic-bezier(0.8,0,1,1);\n  }\n\n  50%{\n    transform: none;\n    animation-timing-function: cubic-bezier(0,0,0.2,1);\n  }\n}\n.animate-bounce{\n  animation: bounce 1s infinite;\n}\n@keyframes bounceIn{\n\n  0%{\n    transform: scale(0.3);\n    opacity: 0;\n  }\n\n  50%{\n    transform: scale(1.05);\n    opacity: 0.8;\n  }\n\n  100%{\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n.animate-bounce-in{\n  animation: bounceIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n}\n@keyframes pulse{\n\n  50%{\n    opacity: 0.5;\n  }\n\n  0%, 100%{\n    opacity: 1;\n  }\n}\n.animate-pulse{\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n@keyframes slideRight{\n\n  0%{\n    transform: translateX(-10px);\n    opacity: 0;\n  }\n\n  100%{\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n.animate-slide-right{\n  animation: slideRight 0.3s ease-in-out;\n}\n@keyframes spin{\n\n  to{\n    transform: rotate(360deg);\n  }\n}\n.animate-spin{\n  animation: spin 1s linear infinite;\n}\n.cursor-not-allowed{\n  cursor: not-allowed;\n}\n.cursor-pointer{\n  cursor: pointer;\n}\n.touch-none{\n  touch-action: none;\n}\n.touch-manipulation{\n  touch-action: manipulation;\n}\n.select-none{\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n}\n.resize-none{\n  resize: none;\n}\n.resize-y{\n  resize: vertical;\n}\n.resize{\n  resize: both;\n}\n.scroll-mt-20{\n  scroll-margin-top: 5rem;\n}\n.list-decimal{\n  list-style-type: decimal;\n}\n.list-disc{\n  list-style-type: disc;\n}\n.list-none{\n  list-style-type: none;\n}\n.appearance-none{\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n}\n.grid-cols-1{\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n.grid-cols-12{\n  grid-template-columns: repeat(12, minmax(0, 1fr));\n}\n.grid-cols-2{\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\n.grid-cols-3{\n  grid-template-columns: repeat(3, minmax(0, 1fr));\n}\n.grid-cols-4{\n  grid-template-columns: repeat(4, minmax(0, 1fr));\n}\n.flex-row{\n  flex-direction: row;\n}\n.flex-row-reverse{\n  flex-direction: row-reverse;\n}\n.flex-col{\n  flex-direction: column;\n}\n.flex-col-reverse{\n  flex-direction: column-reverse;\n}\n.flex-wrap{\n  flex-wrap: wrap;\n}\n.items-start{\n  align-items: flex-start;\n}\n.items-end{\n  align-items: flex-end;\n}\n.items-center{\n  align-items: center;\n}\n.items-baseline{\n  align-items: baseline;\n}\n.justify-start{\n  justify-content: flex-start;\n}\n.justify-end{\n  justify-content: flex-end;\n}\n.justify-center{\n  justify-content: center;\n}\n.justify-between{\n  justify-content: space-between;\n}\n.justify-around{\n  justify-content: space-around;\n}\n.gap-0{\n  gap: 0px;\n}\n.gap-1{\n  gap: 0.25rem;\n}\n.gap-1\\.5{\n  gap: 0.375rem;\n}\n.gap-10{\n  gap: 2.5rem;\n}\n.gap-12{\n  gap: 3rem;\n}\n.gap-16{\n  gap: 4rem;\n}\n.gap-2{\n  gap: 0.5rem;\n}\n.gap-3{\n  gap: 0.75rem;\n}\n.gap-4{\n  gap: 1rem;\n}\n.gap-6{\n  gap: 1.5rem;\n}\n.gap-8{\n  gap: 2rem;\n}\n.gap-x-6{\n  -moz-column-gap: 1.5rem;\n       column-gap: 1.5rem;\n}\n.gap-y-2{\n  row-gap: 0.5rem;\n}\n.space-x-2 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-3 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-4 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-6 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-8 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-y-1 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\n}\n.space-y-1\\.5 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\n}\n.space-y-12 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(3rem * var(--tw-space-y-reverse));\n}\n.space-y-2 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\n}\n.space-y-20 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(5rem * var(--tw-space-y-reverse));\n}\n.space-y-24 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(6rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(6rem * var(--tw-space-y-reverse));\n}\n.space-y-3 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\n}\n.space-y-4 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n}\n.space-y-5 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));\n}\n.space-y-6 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\n}\n.space-y-8 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\n}\n.space-x-reverse > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 1;\n}\n.divide-y > :not([hidden]) ~ :not([hidden]){\n  --tw-divide-y-reverse: 0;\n  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));\n  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));\n}\n.divide-gray-200 > :not([hidden]) ~ :not([hidden]){\n  --tw-divide-opacity: 1;\n  border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));\n}\n.divide-slate-200 > :not([hidden]) ~ :not([hidden]){\n  --tw-divide-opacity: 1;\n  border-color: rgb(226 232 240 / var(--tw-divide-opacity, 1));\n}\n.self-center{\n  align-self: center;\n}\n.overflow-auto{\n  overflow: auto;\n}\n.overflow-hidden{\n  overflow: hidden;\n}\n.overflow-x-auto{\n  overflow-x: auto;\n}\n.overflow-y-auto{\n  overflow-y: auto;\n}\n.truncate{\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.whitespace-nowrap{\n  white-space: nowrap;\n}\n.whitespace-pre-line{\n  white-space: pre-line;\n}\n.break-all{\n  word-break: break-all;\n}\n.rounded{\n  border-radius: 0.25rem;\n}\n.rounded-2xl{\n  border-radius: 1rem;\n}\n.rounded-3xl{\n  border-radius: 1.5rem;\n}\n.rounded-full{\n  border-radius: 9999px;\n}\n.rounded-lg{\n  border-radius: 0.5rem;\n}\n.rounded-md{\n  border-radius: 0.375rem;\n}\n.rounded-none{\n  border-radius: 0px;\n}\n.rounded-sm{\n  border-radius: 0.125rem;\n}\n.rounded-xl{\n  border-radius: 0.75rem;\n}\n.rounded-l-lg{\n  border-top-left-radius: 0.5rem;\n  border-bottom-left-radius: 0.5rem;\n}\n.rounded-l-none{\n  border-top-left-radius: 0px;\n  border-bottom-left-radius: 0px;\n}\n.rounded-r-lg{\n  border-top-right-radius: 0.5rem;\n  border-bottom-right-radius: 0.5rem;\n}\n.rounded-r-none{\n  border-top-right-radius: 0px;\n  border-bottom-right-radius: 0px;\n}\n.rounded-t{\n  border-top-left-radius: 0.25rem;\n  border-top-right-radius: 0.25rem;\n}\n.rounded-t-2xl{\n  border-top-left-radius: 1rem;\n  border-top-right-radius: 1rem;\n}\n.rounded-t-full{\n  border-top-left-radius: 9999px;\n  border-top-right-radius: 9999px;\n}\n.rounded-t-lg{\n  border-top-left-radius: 0.5rem;\n  border-top-right-radius: 0.5rem;\n}\n.rounded-t-md{\n  border-top-left-radius: 0.375rem;\n  border-top-right-radius: 0.375rem;\n}\n.rounded-t-xl{\n  border-top-left-radius: 0.75rem;\n  border-top-right-radius: 0.75rem;\n}\n.rounded-bl-md{\n  border-bottom-left-radius: 0.375rem;\n}\n.rounded-tl-lg{\n  border-top-left-radius: 0.5rem;\n}\n.rounded-tr-lg{\n  border-top-right-radius: 0.5rem;\n}\n.border{\n  border-width: 1px;\n}\n.border-0{\n  border-width: 0px;\n}\n.border-2{\n  border-width: 2px;\n}\n.border-4{\n  border-width: 4px;\n}\n.border-x{\n  border-left-width: 1px;\n  border-right-width: 1px;\n}\n.border-x-0{\n  border-left-width: 0px;\n  border-right-width: 0px;\n}\n.border-b{\n  border-bottom-width: 1px;\n}\n.border-b-2{\n  border-bottom-width: 2px;\n}\n.border-l-2{\n  border-left-width: 2px;\n}\n.border-l-4{\n  border-left-width: 4px;\n}\n.border-r{\n  border-right-width: 1px;\n}\n.border-r-2{\n  border-right-width: 2px;\n}\n.border-t{\n  border-top-width: 1px;\n}\n.border-t-2{\n  border-top-width: 2px;\n}\n.border-dashed{\n  border-style: dashed;\n}\n.border-amber-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(253 230 138 / var(--tw-border-opacity, 1));\n}\n.border-blue-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\n}\n.border-blue-400{\n  --tw-border-opacity: 1;\n  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));\n}\n.border-blue-500{\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n}\n.border-blue-600{\n  --tw-border-opacity: 1;\n  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));\n}\n.border-current{\n  border-color: currentColor;\n}\n.border-error-500{\n  --tw-border-opacity: 1;\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\n}\n.border-gray-100{\n  --tw-border-opacity: 1;\n  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));\n}\n.border-gray-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\n}\n.border-gray-300{\n  --tw-border-opacity: 1;\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\n}\n.border-green-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\n}\n.border-green-400{\n  --tw-border-opacity: 1;\n  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));\n}\n.border-green-500{\n  --tw-border-opacity: 1;\n  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));\n}\n.border-neutral-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(229 229 229 / var(--tw-border-opacity, 1));\n}\n.border-neutral-300{\n  --tw-border-opacity: 1;\n  border-color: rgb(212 212 212 / var(--tw-border-opacity, 1));\n}\n.border-orange-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));\n}\n.border-orange-300{\n  --tw-border-opacity: 1;\n  border-color: rgb(253 186 116 / var(--tw-border-opacity, 1));\n}\n.border-primary-100{\n  --tw-border-opacity: 1;\n  border-color: rgb(243 232 255 / var(--tw-border-opacity, 1));\n}\n.border-primary-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));\n}\n.border-primary-200\\/30{\n  border-color: rgb(233 213 255 / 0.3);\n}\n.border-primary-200\\/50{\n  border-color: rgb(233 213 255 / 0.5);\n}\n.border-primary-500{\n  --tw-border-opacity: 1;\n  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));\n}\n.border-primary-600{\n  --tw-border-opacity: 1;\n  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));\n}\n.border-red-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\n}\n.border-red-300{\n  --tw-border-opacity: 1;\n  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));\n}\n.border-red-400{\n  --tw-border-opacity: 1;\n  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));\n}\n.border-red-500{\n  --tw-border-opacity: 1;\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\n}\n.border-slate-100{\n  --tw-border-opacity: 1;\n  border-color: rgb(241 245 249 / var(--tw-border-opacity, 1));\n}\n.border-slate-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));\n}\n.border-slate-200\\/50{\n  border-color: rgb(226 232 240 / 0.5);\n}\n.border-slate-200\\/60{\n  border-color: rgb(226 232 240 / 0.6);\n}\n.border-slate-200\\/80{\n  border-color: rgb(226 232 240 / 0.8);\n}\n.border-slate-300{\n  --tw-border-opacity: 1;\n  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));\n}\n.border-slate-600{\n  --tw-border-opacity: 1;\n  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));\n}\n.border-slate-700{\n  --tw-border-opacity: 1;\n  border-color: rgb(51 65 85 / var(--tw-border-opacity, 1));\n}\n.border-slate-800{\n  --tw-border-opacity: 1;\n  border-color: rgb(30 41 59 / var(--tw-border-opacity, 1));\n}\n.border-slate-800\\/30{\n  border-color: rgb(30 41 59 / 0.3);\n}\n.border-slate-800\\/50{\n  border-color: rgb(30 41 59 / 0.5);\n}\n.border-slate-800\\/80{\n  border-color: rgb(30 41 59 / 0.8);\n}\n.border-success-500{\n  --tw-border-opacity: 1;\n  border-color: rgb(16 185 129 / var(--tw-border-opacity, 1));\n}\n.border-transparent{\n  border-color: transparent;\n}\n.border-white{\n  --tw-border-opacity: 1;\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\n.border-white\\/20{\n  border-color: rgb(255 255 255 / 0.2);\n}\n.border-yellow-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));\n}\n.border-yellow-500{\n  --tw-border-opacity: 1;\n  border-color: rgb(234 179 8 / var(--tw-border-opacity, 1));\n}\n.border-l-transparent{\n  border-left-color: transparent;\n}\n.border-t-transparent{\n  border-top-color: transparent;\n}\n.bg-accent-300{\n  --tw-bg-opacity: 1;\n  background-color: rgb(253 186 116 / var(--tw-bg-opacity, 1));\n}\n.bg-accent-300\\/20{\n  background-color: rgb(253 186 116 / 0.2);\n}\n.bg-accent-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));\n}\n.bg-accent-500\\/10{\n  background-color: rgb(249 115 22 / 0.1);\n}\n.bg-accent-500\\/20{\n  background-color: rgb(249 115 22 / 0.2);\n}\n.bg-accent-500\\/30{\n  background-color: rgb(249 115 22 / 0.3);\n}\n.bg-accent-500\\/90{\n  background-color: rgb(249 115 22 / 0.9);\n}\n.bg-amber-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));\n}\n.bg-amber-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));\n}\n.bg-amber-700{\n  --tw-bg-opacity: 1;\n  background-color: rgb(180 83 9 / var(--tw-bg-opacity, 1));\n}\n.bg-amber-900\\/20{\n  background-color: rgb(120 53 15 / 0.2);\n}\n.bg-black{\n  --tw-bg-opacity: 1;\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\n}\n.bg-black\\/10{\n  background-color: rgb(0 0 0 / 0.1);\n}\n.bg-black\\/20{\n  background-color: rgb(0 0 0 / 0.2);\n}\n.bg-black\\/30{\n  background-color: rgb(0 0 0 / 0.3);\n}\n.bg-black\\/50{\n  background-color: rgb(0 0 0 / 0.5);\n}\n.bg-black\\/60{\n  background-color: rgb(0 0 0 / 0.6);\n}\n.bg-black\\/70{\n  background-color: rgb(0 0 0 / 0.7);\n}\n.bg-black\\/90{\n  background-color: rgb(0 0 0 / 0.9);\n}\n.bg-blue-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-200{\n  --tw-bg-opacity: 1;\n  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-500\\/90{\n  background-color: rgb(59 130 246 / 0.9);\n}\n.bg-blue-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-700{\n  --tw-bg-opacity: 1;\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-900\\/20{\n  background-color: rgb(30 58 138 / 0.2);\n}\n.bg-blue-900\\/30{\n  background-color: rgb(30 58 138 / 0.3);\n}\n.bg-cyan-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(207 250 254 / var(--tw-bg-opacity, 1));\n}\n.bg-cyan-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(6 182 212 / var(--tw-bg-opacity, 1));\n}\n.bg-error-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-200{\n  --tw-bg-opacity: 1;\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\n}\n.bg-green-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\n}\n.bg-green-200{\n  --tw-bg-opacity: 1;\n  background-color: rgb(187 247 208 / var(--tw-bg-opacity, 1));\n}\n.bg-green-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\n}\n.bg-green-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\n}\n.bg-green-500\\/90{\n  background-color: rgb(34 197 94 / 0.9);\n}\n.bg-green-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\n}\n.bg-green-900\\/20{\n  background-color: rgb(20 83 45 / 0.2);\n}\n.bg-indigo-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));\n}\n.bg-neutral-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));\n}\n.bg-orange-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));\n}\n.bg-orange-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));\n}\n.bg-orange-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));\n}\n.bg-primary-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));\n}\n.bg-primary-200{\n  --tw-bg-opacity: 1;\n  background-color: rgb(233 213 255 / var(--tw-bg-opacity, 1));\n}\n.bg-primary-300{\n  --tw-bg-opacity: 1;\n  background-color: rgb(216 180 254 / var(--tw-bg-opacity, 1));\n}\n.bg-primary-300\\/20{\n  background-color: rgb(216 180 254 / 0.2);\n}\n.bg-primary-400{\n  --tw-bg-opacity: 1;\n  background-color: rgb(192 132 252 / var(--tw-bg-opacity, 1));\n}\n.bg-primary-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));\n}\n.bg-primary-50\\/50{\n  background-color: rgb(250 245 255 / 0.5);\n}\n.bg-primary-50\\/90{\n  background-color: rgb(250 245 255 / 0.9);\n}\n.bg-primary-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\n}\n.bg-primary-500\\/10{\n  background-color: rgb(168 85 247 / 0.1);\n}\n.bg-primary-500\\/20{\n  background-color: rgb(168 85 247 / 0.2);\n}\n.bg-primary-500\\/30{\n  background-color: rgb(168 85 247 / 0.3);\n}\n.bg-primary-500\\/5{\n  background-color: rgb(168 85 247 / 0.05);\n}\n.bg-primary-500\\/90{\n  background-color: rgb(168 85 247 / 0.9);\n}\n.bg-primary-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));\n}\n.bg-primary-900{\n  --tw-bg-opacity: 1;\n  background-color: rgb(88 28 135 / var(--tw-bg-opacity, 1));\n}\n.bg-primary-900\\/20{\n  background-color: rgb(88 28 135 / 0.2);\n}\n.bg-primary-900\\/30{\n  background-color: rgb(88 28 135 / 0.3);\n}\n.bg-purple-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));\n}\n.bg-purple-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));\n}\n.bg-purple-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\n}\n.bg-red-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\n}\n.bg-red-200{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));\n}\n.bg-red-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\n}\n.bg-red-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\n}\n.bg-red-500\\/80{\n  background-color: rgb(239 68 68 / 0.8);\n}\n.bg-red-500\\/90{\n  background-color: rgb(239 68 68 / 0.9);\n}\n.bg-red-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\n}\n.bg-red-900\\/20{\n  background-color: rgb(127 29 29 / 0.2);\n}\n.bg-secondary-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(207 250 254 / var(--tw-bg-opacity, 1));\n}\n.bg-secondary-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));\n}\n.bg-secondary-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(6 182 212 / var(--tw-bg-opacity, 1));\n}\n.bg-secondary-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(8 145 178 / var(--tw-bg-opacity, 1));\n}\n.bg-slate-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));\n}\n.bg-slate-100\\/90{\n  background-color: rgb(241 245 249 / 0.9);\n}\n.bg-slate-200{\n  --tw-bg-opacity: 1;\n  background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));\n}\n.bg-slate-300{\n  --tw-bg-opacity: 1;\n  background-color: rgb(203 213 225 / var(--tw-bg-opacity, 1));\n}\n.bg-slate-400{\n  --tw-bg-opacity: 1;\n  background-color: rgb(148 163 184 / var(--tw-bg-opacity, 1));\n}\n.bg-slate-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));\n}\n.bg-slate-50\\/50{\n  background-color: rgb(248 250 252 / 0.5);\n}\n.bg-slate-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));\n}\n.bg-slate-700{\n  --tw-bg-opacity: 1;\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\n}\n.bg-slate-700\\/30{\n  background-color: rgb(51 65 85 / 0.3);\n}\n.bg-slate-700\\/50{\n  background-color: rgb(51 65 85 / 0.5);\n}\n.bg-slate-800{\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\n}\n.bg-slate-800\\/5{\n  background-color: rgb(30 41 59 / 0.05);\n}\n.bg-slate-800\\/50{\n  background-color: rgb(30 41 59 / 0.5);\n}\n.bg-slate-800\\/80{\n  background-color: rgb(30 41 59 / 0.8);\n}\n.bg-slate-800\\/95{\n  background-color: rgb(30 41 59 / 0.95);\n}\n.bg-slate-900{\n  --tw-bg-opacity: 1;\n  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));\n}\n.bg-slate-900\\/90{\n  background-color: rgb(15 23 42 / 0.9);\n}\n.bg-slate-950{\n  --tw-bg-opacity: 1;\n  background-color: rgb(2 6 23 / var(--tw-bg-opacity, 1));\n}\n.bg-success-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(236 253 245 / var(--tw-bg-opacity, 1));\n}\n.bg-transparent{\n  background-color: transparent;\n}\n.bg-warning-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));\n}\n.bg-white{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\n.bg-white\\/10{\n  background-color: rgb(255 255 255 / 0.1);\n}\n.bg-white\\/20{\n  background-color: rgb(255 255 255 / 0.2);\n}\n.bg-white\\/40{\n  background-color: rgb(255 255 255 / 0.4);\n}\n.bg-white\\/5{\n  background-color: rgb(255 255 255 / 0.05);\n}\n.bg-white\\/50{\n  background-color: rgb(255 255 255 / 0.5);\n}\n.bg-white\\/60{\n  background-color: rgb(255 255 255 / 0.6);\n}\n.bg-white\\/80{\n  background-color: rgb(255 255 255 / 0.8);\n}\n.bg-white\\/90{\n  background-color: rgb(255 255 255 / 0.9);\n}\n.bg-white\\/95{\n  background-color: rgb(255 255 255 / 0.95);\n}\n.bg-yellow-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\n}\n.bg-yellow-200{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 240 138 / var(--tw-bg-opacity, 1));\n}\n.bg-yellow-400{\n  --tw-bg-opacity: 1;\n  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));\n}\n.bg-yellow-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));\n}\n.bg-yellow-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));\n}\n.bg-yellow-900\\/30{\n  background-color: rgb(113 63 18 / 0.3);\n}\n.bg-opacity-0{\n  --tw-bg-opacity: 0;\n}\n.bg-opacity-50{\n  --tw-bg-opacity: 0.5;\n}\n.bg-opacity-75{\n  --tw-bg-opacity: 0.75;\n}\n.bg-opacity-90{\n  --tw-bg-opacity: 0.9;\n}\n.bg-opacity-95{\n  --tw-bg-opacity: 0.95;\n}\n.bg-\\[linear-gradient\\(to_right\\2c \\#80808012_1px\\2c transparent_1px\\)\\2c linear-gradient\\(to_bottom\\2c \\#80808012_1px\\2c transparent_1px\\)\\]{\n  background-image: linear-gradient(to right,#80808012 1px,transparent 1px),linear-gradient(to bottom,#80808012 1px,transparent 1px);\n}\n.bg-\\[url\\(\\'\\/images\\/pattern-dots\\.svg\\'\\)\\]{\n  background-image: url('/images/pattern-dots.svg');\n}\n.bg-gradient-to-b{\n  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));\n}\n.bg-gradient-to-br{\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\n}\n.bg-gradient-to-r{\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n}\n.bg-gradient-to-t{\n  background-image: linear-gradient(to top, var(--tw-gradient-stops));\n}\n.from-accent-300{\n  --tw-gradient-from: #FDBA74 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(253 186 116 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-accent-500{\n  --tw-gradient-from: #F97316 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-accent-500\\/0{\n  --tw-gradient-from: rgb(249 115 22 / 0) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-accent-500\\/40{\n  --tw-gradient-from: rgb(249 115 22 / 0.4) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-accent-600{\n  --tw-gradient-from: #EA580C var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(234 88 12 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-amber-400{\n  --tw-gradient-from: #fbbf24 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(251 191 36 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-black\\/60{\n  --tw-gradient-from: rgb(0 0 0 / 0.6) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-black\\/70{\n  --tw-gradient-from: rgb(0 0 0 / 0.7) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-blue-50{\n  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-blue-500{\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-gray-50{\n  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-green-50{\n  --tw-gradient-from: #f0fdf4 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(240 253 244 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-green-500{\n  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-orange-50{\n  --tw-gradient-from: #fff7ed var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 247 237 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-orange-500{\n  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-pink-100{\n  --tw-gradient-from: #fce7f3 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(252 231 243 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary-100{\n  --tw-gradient-from: #F3E8FF var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(243 232 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary-200{\n  --tw-gradient-from: #E9D5FF var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(233 213 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary-50{\n  --tw-gradient-from: #FAF5FF var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(250 245 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary-50\\/30{\n  --tw-gradient-from: rgb(250 245 255 / 0.3) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(250 245 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary-50\\/80{\n  --tw-gradient-from: rgb(250 245 255 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(250 245 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary-500{\n  --tw-gradient-from: #A855F7 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary-500\\/0{\n  --tw-gradient-from: rgb(168 85 247 / 0) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary-500\\/10{\n  --tw-gradient-from: rgb(168 85 247 / 0.1) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary-500\\/20{\n  --tw-gradient-from: rgb(168 85 247 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary-600{\n  --tw-gradient-from: #9333EA var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary-800{\n  --tw-gradient-from: #6B21A8 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(107 33 168 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary-900{\n  --tw-gradient-from: #581C87 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary-900\\/80{\n  --tw-gradient-from: rgb(88 28 135 / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary-900\\/90{\n  --tw-gradient-from: rgb(88 28 135 / 0.9) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-purple-50{\n  --tw-gradient-from: #faf5ff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(250 245 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-purple-500{\n  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-red-50{\n  --tw-gradient-from: #fef2f2 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(254 242 242 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-red-500{\n  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-secondary-500{\n  --tw-gradient-from: #06B6D4 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(6 182 212 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-slate-400{\n  --tw-gradient-from: #94A3B8 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(148 163 184 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-slate-50{\n  --tw-gradient-from: #F8FAFC var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(248 250 252 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-slate-500{\n  --tw-gradient-from: #64748B var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(100 116 139 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-slate-800{\n  --tw-gradient-from: #1E293B var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(30 41 59 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-slate-900{\n  --tw-gradient-from: #0F172A var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(15 23 42 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-transparent{\n  --tw-gradient-from: transparent var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-white{\n  --tw-gradient-from: #fff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-white\\/0{\n  --tw-gradient-from: rgb(255 255 255 / 0) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-yellow-50{\n  --tw-gradient-from: #fefce8 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(254 252 232 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.via-accent-500{\n  --tw-gradient-to: rgb(249 115 22 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #F97316 var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-accent-500\\/20{\n  --tw-gradient-to: rgb(249 115 22 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(249 115 22 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-accent-500\\/80{\n  --tw-gradient-to: rgb(249 115 22 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(249 115 22 / 0.8) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-black\\/0{\n  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-black\\/30{\n  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-black\\/50{\n  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0.5) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-primary-100{\n  --tw-gradient-to: rgb(243 232 255 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #F3E8FF var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-primary-50\\/30{\n  --tw-gradient-to: rgb(250 245 255 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(250 245 255 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-primary-500\\/0{\n  --tw-gradient-to: rgb(168 85 247 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(168 85 247 / 0) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-primary-600{\n  --tw-gradient-to: rgb(147 51 234 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #9333EA var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-primary-700{\n  --tw-gradient-to: rgb(126 34 206 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #7E22CE var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-primary-800{\n  --tw-gradient-to: rgb(107 33 168 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #6B21A8 var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-primary-900\\/40{\n  --tw-gradient-to: rgb(88 28 135 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(88 28 135 / 0.4) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-slate-50\\/50{\n  --tw-gradient-to: rgb(248 250 252 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(248 250 252 / 0.5) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-transparent{\n  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-white{\n  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-white\\/20{\n  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-white\\/30{\n  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.to-accent-100{\n  --tw-gradient-to: #FFEDD5 var(--tw-gradient-to-position);\n}\n.to-accent-300{\n  --tw-gradient-to: #FDBA74 var(--tw-gradient-to-position);\n}\n.to-accent-400{\n  --tw-gradient-to: #FB923C var(--tw-gradient-to-position);\n}\n.to-accent-50{\n  --tw-gradient-to: #FFF7ED var(--tw-gradient-to-position);\n}\n.to-accent-50\\/30{\n  --tw-gradient-to: rgb(255 247 237 / 0.3) var(--tw-gradient-to-position);\n}\n.to-accent-500{\n  --tw-gradient-to: #F97316 var(--tw-gradient-to-position);\n}\n.to-accent-500\\/0{\n  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);\n}\n.to-accent-500\\/10{\n  --tw-gradient-to: rgb(249 115 22 / 0.1) var(--tw-gradient-to-position);\n}\n.to-accent-500\\/20{\n  --tw-gradient-to: rgb(249 115 22 / 0.2) var(--tw-gradient-to-position);\n}\n.to-accent-600{\n  --tw-gradient-to: #EA580C var(--tw-gradient-to-position);\n}\n.to-accent-700{\n  --tw-gradient-to: #C2410C var(--tw-gradient-to-position);\n}\n.to-amber-50{\n  --tw-gradient-to: #fffbeb var(--tw-gradient-to-position);\n}\n.to-amber-500{\n  --tw-gradient-to: #f59e0b var(--tw-gradient-to-position);\n}\n.to-black\\/0{\n  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);\n}\n.to-black\\/10{\n  --tw-gradient-to: rgb(0 0 0 / 0.1) var(--tw-gradient-to-position);\n}\n.to-black\\/60{\n  --tw-gradient-to: rgb(0 0 0 / 0.6) var(--tw-gradient-to-position);\n}\n.to-blue-100{\n  --tw-gradient-to: #dbeafe var(--tw-gradient-to-position);\n}\n.to-blue-100\\/50{\n  --tw-gradient-to: rgb(219 234 254 / 0.5) var(--tw-gradient-to-position);\n}\n.to-blue-50{\n  --tw-gradient-to: #eff6ff var(--tw-gradient-to-position);\n}\n.to-cyan-50{\n  --tw-gradient-to: #ecfeff var(--tw-gradient-to-position);\n}\n.to-emerald-50{\n  --tw-gradient-to: #ecfdf5 var(--tw-gradient-to-position);\n}\n.to-emerald-600{\n  --tw-gradient-to: #059669 var(--tw-gradient-to-position);\n}\n.to-gray-50{\n  --tw-gradient-to: #f9fafb var(--tw-gradient-to-position);\n}\n.to-green-100\\/50{\n  --tw-gradient-to: rgb(220 252 231 / 0.5) var(--tw-gradient-to-position);\n}\n.to-green-500{\n  --tw-gradient-to: #22c55e var(--tw-gradient-to-position);\n}\n.to-indigo-50{\n  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);\n}\n.to-indigo-600{\n  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);\n}\n.to-orange-50{\n  --tw-gradient-to: #fff7ed var(--tw-gradient-to-position);\n}\n.to-orange-500{\n  --tw-gradient-to: #f97316 var(--tw-gradient-to-position);\n}\n.to-pink-600{\n  --tw-gradient-to: #db2777 var(--tw-gradient-to-position);\n}\n.to-primary-100{\n  --tw-gradient-to: #F3E8FF var(--tw-gradient-to-position);\n}\n.to-primary-100\\/50{\n  --tw-gradient-to: rgb(243 232 255 / 0.5) var(--tw-gradient-to-position);\n}\n.to-primary-100\\/60{\n  --tw-gradient-to: rgb(243 232 255 / 0.6) var(--tw-gradient-to-position);\n}\n.to-primary-200{\n  --tw-gradient-to: #E9D5FF var(--tw-gradient-to-position);\n}\n.to-primary-300{\n  --tw-gradient-to: #D8B4FE var(--tw-gradient-to-position);\n}\n.to-primary-50{\n  --tw-gradient-to: #FAF5FF var(--tw-gradient-to-position);\n}\n.to-primary-500{\n  --tw-gradient-to: #A855F7 var(--tw-gradient-to-position);\n}\n.to-primary-500\\/0{\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\n}\n.to-primary-500\\/40{\n  --tw-gradient-to: rgb(168 85 247 / 0.4) var(--tw-gradient-to-position);\n}\n.to-primary-600{\n  --tw-gradient-to: #9333EA var(--tw-gradient-to-position);\n}\n.to-primary-700{\n  --tw-gradient-to: #7E22CE var(--tw-gradient-to-position);\n}\n.to-primary-800{\n  --tw-gradient-to: #6B21A8 var(--tw-gradient-to-position);\n}\n.to-purple-600{\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\n}\n.to-red-100{\n  --tw-gradient-to: #fee2e2 var(--tw-gradient-to-position);\n}\n.to-red-100\\/50{\n  --tw-gradient-to: rgb(254 226 226 / 0.5) var(--tw-gradient-to-position);\n}\n.to-rose-50{\n  --tw-gradient-to: #fff1f2 var(--tw-gradient-to-position);\n}\n.to-secondary-500{\n  --tw-gradient-to: #06B6D4 var(--tw-gradient-to-position);\n}\n.to-secondary-600{\n  --tw-gradient-to: #0891B2 var(--tw-gradient-to-position);\n}\n.to-secondary-800{\n  --tw-gradient-to: #155E75 var(--tw-gradient-to-position);\n}\n.to-secondary-900{\n  --tw-gradient-to: #164E63 var(--tw-gradient-to-position);\n}\n.to-secondary-900\\/80{\n  --tw-gradient-to: rgb(22 78 99 / 0.8) var(--tw-gradient-to-position);\n}\n.to-slate-100{\n  --tw-gradient-to: #F1F5F9 var(--tw-gradient-to-position);\n}\n.to-slate-50{\n  --tw-gradient-to: #F8FAFC var(--tw-gradient-to-position);\n}\n.to-slate-50\\/50{\n  --tw-gradient-to: rgb(248 250 252 / 0.5) var(--tw-gradient-to-position);\n}\n.to-slate-500{\n  --tw-gradient-to: #64748B var(--tw-gradient-to-position);\n}\n.to-slate-600{\n  --tw-gradient-to: #475569 var(--tw-gradient-to-position);\n}\n.to-slate-700{\n  --tw-gradient-to: #334155 var(--tw-gradient-to-position);\n}\n.to-slate-800{\n  --tw-gradient-to: #1E293B var(--tw-gradient-to-position);\n}\n.to-slate-900{\n  --tw-gradient-to: #0F172A var(--tw-gradient-to-position);\n}\n.to-transparent{\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\n}\n.to-violet-50{\n  --tw-gradient-to: #f5f3ff var(--tw-gradient-to-position);\n}\n.to-white{\n  --tw-gradient-to: #fff var(--tw-gradient-to-position);\n}\n.to-white\\/0{\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n}\n.to-yellow-100\\/50{\n  --tw-gradient-to: rgb(254 249 195 / 0.5) var(--tw-gradient-to-position);\n}\n.bg-\\[size\\:24px_24px\\]{\n  background-size: 24px 24px;\n}\n.bg-cover{\n  background-size: cover;\n}\n.bg-clip-text{\n  -webkit-background-clip: text;\n          background-clip: text;\n}\n.bg-center{\n  background-position: center;\n}\n.bg-repeat{\n  background-repeat: repeat;\n}\n.fill-current{\n  fill: currentColor;\n}\n.fill-primary-500{\n  fill: #A855F7;\n}\n.fill-yellow-400{\n  fill: #facc15;\n}\n.fill-yellow-500{\n  fill: #eab308;\n}\n.object-contain{\n  -o-object-fit: contain;\n     object-fit: contain;\n}\n.object-cover{\n  -o-object-fit: cover;\n     object-fit: cover;\n}\n.object-fill{\n  -o-object-fit: fill;\n     object-fit: fill;\n}\n.object-none{\n  -o-object-fit: none;\n     object-fit: none;\n}\n.object-center{\n  -o-object-position: center;\n     object-position: center;\n}\n.p-0{\n  padding: 0px;\n}\n.p-1{\n  padding: 0.25rem;\n}\n.p-1\\.5{\n  padding: 0.375rem;\n}\n.p-12{\n  padding: 3rem;\n}\n.p-2{\n  padding: 0.5rem;\n}\n.p-2\\.5{\n  padding: 0.625rem;\n}\n.p-3{\n  padding: 0.75rem;\n}\n.p-4{\n  padding: 1rem;\n}\n.p-5{\n  padding: 1.25rem;\n}\n.p-6{\n  padding: 1.5rem;\n}\n.p-8{\n  padding: 2rem;\n}\n.px-1{\n  padding-left: 0.25rem;\n  padding-right: 0.25rem;\n}\n.px-1\\.5{\n  padding-left: 0.375rem;\n  padding-right: 0.375rem;\n}\n.px-10{\n  padding-left: 2.5rem;\n  padding-right: 2.5rem;\n}\n.px-2{\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\n.px-2\\.5{\n  padding-left: 0.625rem;\n  padding-right: 0.625rem;\n}\n.px-3{\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\n.px-4{\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\n.px-6{\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n}\n.px-8{\n  padding-left: 2rem;\n  padding-right: 2rem;\n}\n.py-0\\.5{\n  padding-top: 0.125rem;\n  padding-bottom: 0.125rem;\n}\n.py-1{\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n}\n.py-1\\.5{\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n}\n.py-10{\n  padding-top: 2.5rem;\n  padding-bottom: 2.5rem;\n}\n.py-12{\n  padding-top: 3rem;\n  padding-bottom: 3rem;\n}\n.py-16{\n  padding-top: 4rem;\n  padding-bottom: 4rem;\n}\n.py-2{\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\n.py-2\\.5{\n  padding-top: 0.625rem;\n  padding-bottom: 0.625rem;\n}\n.py-20{\n  padding-top: 5rem;\n  padding-bottom: 5rem;\n}\n.py-24{\n  padding-top: 6rem;\n  padding-bottom: 6rem;\n}\n.py-3{\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n}\n.py-4{\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\n.py-6{\n  padding-top: 1.5rem;\n  padding-bottom: 1.5rem;\n}\n.py-8{\n  padding-top: 2rem;\n  padding-bottom: 2rem;\n}\n.pb-0{\n  padding-bottom: 0px;\n}\n.pb-16{\n  padding-bottom: 4rem;\n}\n.pb-2{\n  padding-bottom: 0.5rem;\n}\n.pb-24{\n  padding-bottom: 6rem;\n}\n.pb-3{\n  padding-bottom: 0.75rem;\n}\n.pb-4{\n  padding-bottom: 1rem;\n}\n.pb-6{\n  padding-bottom: 1.5rem;\n}\n.pe-1{\n  padding-inline-end: 0.25rem;\n}\n.pe-2{\n  padding-inline-end: 0.5rem;\n}\n.pe-3{\n  padding-inline-end: 0.75rem;\n}\n.pe-4{\n  padding-inline-end: 1rem;\n}\n.pe-6{\n  padding-inline-end: 1.5rem;\n}\n.pe-8{\n  padding-inline-end: 2rem;\n}\n.pl-0{\n  padding-left: 0px;\n}\n.pl-10{\n  padding-left: 2.5rem;\n}\n.pl-11{\n  padding-left: 2.75rem;\n}\n.pl-12{\n  padding-left: 3rem;\n}\n.pl-2{\n  padding-left: 0.5rem;\n}\n.pl-3{\n  padding-left: 0.75rem;\n}\n.pl-4{\n  padding-left: 1rem;\n}\n.pl-6{\n  padding-left: 1.5rem;\n}\n.pl-7{\n  padding-left: 1.75rem;\n}\n.pl-9{\n  padding-left: 2.25rem;\n}\n.pr-1{\n  padding-right: 0.25rem;\n}\n.pr-10{\n  padding-right: 2.5rem;\n}\n.pr-11{\n  padding-right: 2.75rem;\n}\n.pr-12{\n  padding-right: 3rem;\n}\n.pr-2{\n  padding-right: 0.5rem;\n}\n.pr-3{\n  padding-right: 0.75rem;\n}\n.pr-4{\n  padding-right: 1rem;\n}\n.pr-7{\n  padding-right: 1.75rem;\n}\n.pr-9{\n  padding-right: 2.25rem;\n}\n.ps-1{\n  padding-inline-start: 0.25rem;\n}\n.ps-2{\n  padding-inline-start: 0.5rem;\n}\n.ps-3{\n  padding-inline-start: 0.75rem;\n}\n.ps-4{\n  padding-inline-start: 1rem;\n}\n.ps-6{\n  padding-inline-start: 1.5rem;\n}\n.ps-8{\n  padding-inline-start: 2rem;\n}\n.pt-0{\n  padding-top: 0px;\n}\n.pt-16{\n  padding-top: 4rem;\n}\n.pt-2{\n  padding-top: 0.5rem;\n}\n.pt-20{\n  padding-top: 5rem;\n}\n.pt-24{\n  padding-top: 6rem;\n}\n.pt-3{\n  padding-top: 0.75rem;\n}\n.pt-4{\n  padding-top: 1rem;\n}\n.pt-5{\n  padding-top: 1.25rem;\n}\n.pt-6{\n  padding-top: 1.5rem;\n}\n.pt-8{\n  padding-top: 2rem;\n}\n.text-left{\n  text-align: left;\n}\n.text-center{\n  text-align: center;\n}\n.text-right{\n  text-align: right;\n}\n.font-arabic{\n  font-family: var(--font-tajawal), ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n}\n.font-mono{\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n}\n.font-sans{\n  font-family: var(--font-inter), ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n}\n.text-2xl{\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\n.text-3xl{\n  font-size: 1.875rem;\n  line-height: 2.25rem;\n}\n.text-4xl{\n  font-size: 2.25rem;\n  line-height: 2.5rem;\n}\n.text-5xl{\n  font-size: 3rem;\n  line-height: 1;\n}\n.text-6xl{\n  font-size: 3.75rem;\n  line-height: 1;\n}\n.text-\\[10px\\]{\n  font-size: 10px;\n}\n.text-base{\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\n.text-lg{\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\n.text-sm{\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.text-xl{\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\n.text-xs{\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\n.font-bold{\n  font-weight: 700;\n}\n.font-extrabold{\n  font-weight: 800;\n}\n.font-light{\n  font-weight: 300;\n}\n.font-medium{\n  font-weight: 500;\n}\n.font-normal{\n  font-weight: 400;\n}\n.font-semibold{\n  font-weight: 600;\n}\n.uppercase{\n  text-transform: uppercase;\n}\n.lowercase{\n  text-transform: lowercase;\n}\n.capitalize{\n  text-transform: capitalize;\n}\n.italic{\n  font-style: italic;\n}\n.leading-8{\n  line-height: 2rem;\n}\n.leading-none{\n  line-height: 1;\n}\n.leading-relaxed{\n  line-height: 1.625;\n}\n.leading-tight{\n  line-height: 1.25;\n}\n.tracking-tight{\n  letter-spacing: -0.025em;\n}\n.tracking-wide{\n  letter-spacing: 0.025em;\n}\n.tracking-wider{\n  letter-spacing: 0.05em;\n}\n.text-accent-300{\n  --tw-text-opacity: 1;\n  color: rgb(253 186 116 / var(--tw-text-opacity, 1));\n}\n.text-accent-400{\n  --tw-text-opacity: 1;\n  color: rgb(251 146 60 / var(--tw-text-opacity, 1));\n}\n.text-accent-500{\n  --tw-text-opacity: 1;\n  color: rgb(249 115 22 / var(--tw-text-opacity, 1));\n}\n.text-amber-300{\n  --tw-text-opacity: 1;\n  color: rgb(252 211 77 / var(--tw-text-opacity, 1));\n}\n.text-amber-600{\n  --tw-text-opacity: 1;\n  color: rgb(217 119 6 / var(--tw-text-opacity, 1));\n}\n.text-amber-700{\n  --tw-text-opacity: 1;\n  color: rgb(180 83 9 / var(--tw-text-opacity, 1));\n}\n.text-amber-800{\n  --tw-text-opacity: 1;\n  color: rgb(146 64 14 / var(--tw-text-opacity, 1));\n}\n.text-blue-300{\n  --tw-text-opacity: 1;\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\n}\n.text-blue-400{\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\n.text-blue-500{\n  --tw-text-opacity: 1;\n  color: rgb(59 130 246 / var(--tw-text-opacity, 1));\n}\n.text-blue-600{\n  --tw-text-opacity: 1;\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\n}\n.text-blue-700{\n  --tw-text-opacity: 1;\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\n}\n.text-blue-800{\n  --tw-text-opacity: 1;\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\n}\n.text-blue-900{\n  --tw-text-opacity: 1;\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\n}\n.text-cyan-500{\n  --tw-text-opacity: 1;\n  color: rgb(6 182 212 / var(--tw-text-opacity, 1));\n}\n.text-cyan-800{\n  --tw-text-opacity: 1;\n  color: rgb(21 94 117 / var(--tw-text-opacity, 1));\n}\n.text-error-500{\n  --tw-text-opacity: 1;\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\n}\n.text-gray-300{\n  --tw-text-opacity: 1;\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\n}\n.text-gray-400{\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\n.text-gray-500{\n  --tw-text-opacity: 1;\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\n}\n.text-gray-600{\n  --tw-text-opacity: 1;\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\n}\n.text-gray-700{\n  --tw-text-opacity: 1;\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\n}\n.text-gray-800{\n  --tw-text-opacity: 1;\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\n}\n.text-gray-900{\n  --tw-text-opacity: 1;\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\n}\n.text-green-300{\n  --tw-text-opacity: 1;\n  color: rgb(134 239 172 / var(--tw-text-opacity, 1));\n}\n.text-green-400{\n  --tw-text-opacity: 1;\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\n}\n.text-green-500{\n  --tw-text-opacity: 1;\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\n}\n.text-green-600{\n  --tw-text-opacity: 1;\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\n}\n.text-green-700{\n  --tw-text-opacity: 1;\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\n}\n.text-green-800{\n  --tw-text-opacity: 1;\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\n}\n.text-green-900{\n  --tw-text-opacity: 1;\n  color: rgb(20 83 45 / var(--tw-text-opacity, 1));\n}\n.text-indigo-800{\n  --tw-text-opacity: 1;\n  color: rgb(55 48 163 / var(--tw-text-opacity, 1));\n}\n.text-orange-500{\n  --tw-text-opacity: 1;\n  color: rgb(249 115 22 / var(--tw-text-opacity, 1));\n}\n.text-orange-600{\n  --tw-text-opacity: 1;\n  color: rgb(234 88 12 / var(--tw-text-opacity, 1));\n}\n.text-orange-700{\n  --tw-text-opacity: 1;\n  color: rgb(194 65 12 / var(--tw-text-opacity, 1));\n}\n.text-orange-800{\n  --tw-text-opacity: 1;\n  color: rgb(154 52 18 / var(--tw-text-opacity, 1));\n}\n.text-orange-900{\n  --tw-text-opacity: 1;\n  color: rgb(124 45 18 / var(--tw-text-opacity, 1));\n}\n.text-pink-500{\n  --tw-text-opacity: 1;\n  color: rgb(236 72 153 / var(--tw-text-opacity, 1));\n}\n.text-primary-100{\n  --tw-text-opacity: 1;\n  color: rgb(243 232 255 / var(--tw-text-opacity, 1));\n}\n.text-primary-200{\n  --tw-text-opacity: 1;\n  color: rgb(233 213 255 / var(--tw-text-opacity, 1));\n}\n.text-primary-300{\n  --tw-text-opacity: 1;\n  color: rgb(216 180 254 / var(--tw-text-opacity, 1));\n}\n.text-primary-400{\n  --tw-text-opacity: 1;\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\n}\n.text-primary-50{\n  --tw-text-opacity: 1;\n  color: rgb(250 245 255 / var(--tw-text-opacity, 1));\n}\n.text-primary-500{\n  --tw-text-opacity: 1;\n  color: rgb(168 85 247 / var(--tw-text-opacity, 1));\n}\n.text-primary-600{\n  --tw-text-opacity: 1;\n  color: rgb(147 51 234 / var(--tw-text-opacity, 1));\n}\n.text-primary-600\\/70{\n  color: rgb(147 51 234 / 0.7);\n}\n.text-primary-700{\n  --tw-text-opacity: 1;\n  color: rgb(126 34 206 / var(--tw-text-opacity, 1));\n}\n.text-primary-800{\n  --tw-text-opacity: 1;\n  color: rgb(107 33 168 / var(--tw-text-opacity, 1));\n}\n.text-purple-500{\n  --tw-text-opacity: 1;\n  color: rgb(168 85 247 / var(--tw-text-opacity, 1));\n}\n.text-purple-600{\n  --tw-text-opacity: 1;\n  color: rgb(147 51 234 / var(--tw-text-opacity, 1));\n}\n.text-purple-800{\n  --tw-text-opacity: 1;\n  color: rgb(107 33 168 / var(--tw-text-opacity, 1));\n}\n.text-red-300{\n  --tw-text-opacity: 1;\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\n}\n.text-red-400{\n  --tw-text-opacity: 1;\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\n}\n.text-red-500{\n  --tw-text-opacity: 1;\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\n}\n.text-red-600{\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\n.text-red-700{\n  --tw-text-opacity: 1;\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\n}\n.text-red-800{\n  --tw-text-opacity: 1;\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\n}\n.text-secondary-500{\n  --tw-text-opacity: 1;\n  color: rgb(6 182 212 / var(--tw-text-opacity, 1));\n}\n.text-secondary-700{\n  --tw-text-opacity: 1;\n  color: rgb(14 116 144 / var(--tw-text-opacity, 1));\n}\n.text-secondary-800{\n  --tw-text-opacity: 1;\n  color: rgb(21 94 117 / var(--tw-text-opacity, 1));\n}\n.text-sky-500{\n  --tw-text-opacity: 1;\n  color: rgb(14 165 233 / var(--tw-text-opacity, 1));\n}\n.text-slate-100{\n  --tw-text-opacity: 1;\n  color: rgb(241 245 249 / var(--tw-text-opacity, 1));\n}\n.text-slate-200{\n  --tw-text-opacity: 1;\n  color: rgb(226 232 240 / var(--tw-text-opacity, 1));\n}\n.text-slate-300{\n  --tw-text-opacity: 1;\n  color: rgb(203 213 225 / var(--tw-text-opacity, 1));\n}\n.text-slate-400{\n  --tw-text-opacity: 1;\n  color: rgb(148 163 184 / var(--tw-text-opacity, 1));\n}\n.text-slate-500{\n  --tw-text-opacity: 1;\n  color: rgb(100 116 139 / var(--tw-text-opacity, 1));\n}\n.text-slate-600{\n  --tw-text-opacity: 1;\n  color: rgb(71 85 105 / var(--tw-text-opacity, 1));\n}\n.text-slate-700{\n  --tw-text-opacity: 1;\n  color: rgb(51 65 85 / var(--tw-text-opacity, 1));\n}\n.text-slate-800{\n  --tw-text-opacity: 1;\n  color: rgb(30 41 59 / var(--tw-text-opacity, 1));\n}\n.text-slate-900{\n  --tw-text-opacity: 1;\n  color: rgb(15 23 42 / var(--tw-text-opacity, 1));\n}\n.text-success-500{\n  --tw-text-opacity: 1;\n  color: rgb(16 185 129 / var(--tw-text-opacity, 1));\n}\n.text-transparent{\n  color: transparent;\n}\n.text-white{\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.text-white\\/80{\n  color: rgb(255 255 255 / 0.8);\n}\n.text-white\\/90{\n  color: rgb(255 255 255 / 0.9);\n}\n.text-yellow-300{\n  --tw-text-opacity: 1;\n  color: rgb(253 224 71 / var(--tw-text-opacity, 1));\n}\n.text-yellow-400{\n  --tw-text-opacity: 1;\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\n}\n.text-yellow-500{\n  --tw-text-opacity: 1;\n  color: rgb(234 179 8 / var(--tw-text-opacity, 1));\n}\n.text-yellow-600{\n  --tw-text-opacity: 1;\n  color: rgb(202 138 4 / var(--tw-text-opacity, 1));\n}\n.text-yellow-700{\n  --tw-text-opacity: 1;\n  color: rgb(161 98 7 / var(--tw-text-opacity, 1));\n}\n.text-yellow-800{\n  --tw-text-opacity: 1;\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\n}\n.line-through{\n  text-decoration-line: line-through;\n}\n.decoration-slate-300{\n  text-decoration-color: #CBD5E1;\n}\n.decoration-2{\n  text-decoration-thickness: 2px;\n}\n.underline-offset-2{\n  text-underline-offset: 2px;\n}\n.underline-offset-4{\n  text-underline-offset: 4px;\n}\n.placeholder-slate-500::-moz-placeholder{\n  --tw-placeholder-opacity: 1;\n  color: rgb(100 116 139 / var(--tw-placeholder-opacity, 1));\n}\n.placeholder-slate-500::placeholder{\n  --tw-placeholder-opacity: 1;\n  color: rgb(100 116 139 / var(--tw-placeholder-opacity, 1));\n}\n.accent-primary-500{\n  accent-color: #A855F7;\n}\n.opacity-0{\n  opacity: 0;\n}\n.opacity-10{\n  opacity: 0.1;\n}\n.opacity-100{\n  opacity: 1;\n}\n.opacity-20{\n  opacity: 0.2;\n}\n.opacity-25{\n  opacity: 0.25;\n}\n.opacity-30{\n  opacity: 0.3;\n}\n.opacity-50{\n  opacity: 0.5;\n}\n.opacity-60{\n  opacity: 0.6;\n}\n.opacity-70{\n  opacity: 0.7;\n}\n.opacity-75{\n  opacity: 0.75;\n}\n.opacity-80{\n  opacity: 0.8;\n}\n.opacity-90{\n  opacity: 0.9;\n}\n.mix-blend-multiply{\n  mix-blend-mode: multiply;\n}\n.shadow{\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-2xl{\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-lg{\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-md{\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-none{\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-sm{\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-xl{\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-accent-500\\/20{\n  --tw-shadow-color: rgb(249 115 22 / 0.2);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-blue-500\\/10{\n  --tw-shadow-color: rgb(59 130 246 / 0.1);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-primary-100{\n  --tw-shadow-color: #F3E8FF;\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-primary-500\\/10{\n  --tw-shadow-color: rgb(168 85 247 / 0.1);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-primary-500\\/20{\n  --tw-shadow-color: rgb(168 85 247 / 0.2);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-primary-500\\/25{\n  --tw-shadow-color: rgb(168 85 247 / 0.25);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-red-500\\/25{\n  --tw-shadow-color: rgb(239 68 68 / 0.25);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-red-500\\/30{\n  --tw-shadow-color: rgb(239 68 68 / 0.3);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-slate-200\\/20{\n  --tw-shadow-color: rgb(226 232 240 / 0.2);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-slate-300\\/30{\n  --tw-shadow-color: rgb(203 213 225 / 0.3);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-slate-900\\/10{\n  --tw-shadow-color: rgb(15 23 42 / 0.1);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-slate-900\\/30{\n  --tw-shadow-color: rgb(15 23 42 / 0.3);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-slate-900\\/50{\n  --tw-shadow-color: rgb(15 23 42 / 0.5);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.outline{\n  outline-style: solid;\n}\n.ring-2{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-4{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-primary-200{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(233 213 255 / var(--tw-ring-opacity, 1));\n}\n.ring-primary-500{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));\n}\n.ring-offset-white{\n  --tw-ring-offset-color: #fff;\n}\n.blur{\n  --tw-blur: blur(8px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.blur-3xl{\n  --tw-blur: blur(64px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.blur-\\[2px\\]{\n  --tw-blur: blur(2px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.blur-lg{\n  --tw-blur: blur(16px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.blur-md{\n  --tw-blur: blur(12px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.blur-sm{\n  --tw-blur: blur(4px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.blur-xl{\n  --tw-blur: blur(24px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.brightness-\\[0\\.8\\]{\n  --tw-brightness: brightness(0.8);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.contrast-\\[1\\.1\\]{\n  --tw-contrast: contrast(1.1);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.drop-shadow-md{\n  --tw-drop-shadow: drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.filter{\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.backdrop-blur-lg{\n  --tw-backdrop-blur: blur(16px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.backdrop-blur-md{\n  --tw-backdrop-blur: blur(12px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.backdrop-blur-sm{\n  --tw-backdrop-blur: blur(4px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.transition{\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-all{\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-colors{\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-opacity{\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-shadow{\n  transition-property: box-shadow;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-transform{\n  transition-property: transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.duration-150{\n  transition-duration: 150ms;\n}\n.duration-200{\n  transition-duration: 200ms;\n}\n.duration-300{\n  transition-duration: 300ms;\n}\n.duration-500{\n  transition-duration: 500ms;\n}\n.duration-700{\n  transition-duration: 700ms;\n}\n.ease-in-out{\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\n.ease-out{\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\n.will-change-transform{\n  will-change: transform;\n}\n.\\[animation-delay\\:0\\.2s\\]{\n  animation-delay: 0.2s;\n}\n.\\[animation-delay\\:0\\.4s\\]{\n  animation-delay: 0.4s;\n}\n.\\[mask-image\\:linear-gradient\\(to_bottom\\2c transparent\\2c black\\)\\]{\n  -webkit-mask-image: linear-gradient(to bottom,transparent,black);\n          mask-image: linear-gradient(to bottom,transparent,black);\n}\n\n@keyframes scroll {\n  0% {\n    transform: translateY(0);\n  }\n  100% {\n    transform: translateY(100%);\n  }\n}\n\n.animate-scroll {\n  animation: scroll 2s infinite;\n}\n\n@keyframes float {\n  0% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-10px);\n  }\n  100% {\n    transform: translateY(0px);\n  }\n}\n\n.animate-float {\n  animation: float 3s ease-in-out infinite;\n}\n\n.backdrop-blur-sm {\n  -webkit-backdrop-filter: blur(4px);\n          backdrop-filter: blur(4px);\n}\n\n.backdrop-blur-md {\n  -webkit-backdrop-filter: blur(8px);\n          backdrop-filter: blur(8px);\n}\n\n.backdrop-blur-lg {\n  -webkit-backdrop-filter: blur(16px);\n          backdrop-filter: blur(16px);\n}\n\n/* تحريكات للنوافذ المنبثقة والنماذج */\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n.animate-fadeIn {\n  animation: fadeIn 0.3s ease-out forwards;\n}\n\n@keyframes scaleIn {\n  from {\n    opacity: 0;\n    transform: scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n\n.animate-scaleIn {\n  animation: scaleIn 0.3s ease-out forwards;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.animate-slideIn {\n  animation: slideIn 0.3s ease-out forwards;\n}\n\n@keyframes slideInRight {\n  from {\n    opacity: 0;\n    transform: translateX(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n.animate-slideInRight {\n  animation: slideInRight 0.3s ease-out forwards;\n}\n\n@keyframes slideInLeft {\n  from {\n    opacity: 0;\n    transform: translateX(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n.animate-slideInLeft {\n  animation: slideInLeft 0.3s ease-out forwards;\n}\n\n/* RTL Support - Enhanced */\n[dir=\"rtl\"] {\n  text-align: right;\n}\n\n/* Custom font classes */\n.font-arabic {\n  font-family: var(--font-tajawal), 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n  font-feature-settings: \"liga\" 1, \"kern\" 1;\n  text-rendering: optimizeLegibility;\n}\n\n/* RTL Spacing fixes */\n[dir=\"rtl\"] .space-x-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 1;\n  margin-right: 0.5rem;\n  margin-left: 0;\n}\n\n[dir=\"rtl\"] .space-x-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 1;\n  margin-right: 0.75rem;\n  margin-left: 0;\n}\n\n[dir=\"rtl\"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 1;\n  margin-right: 1rem;\n  margin-left: 0;\n}\n\n/* RTL Margin fixes */\n[dir=\"rtl\"] .ml-2 { margin-left: 0; margin-right: 0.5rem; }\n[dir=\"rtl\"] .ml-3 { margin-left: 0; margin-right: 0.75rem; }\n[dir=\"rtl\"] .ml-4 { margin-left: 0; margin-right: 1rem; }\n[dir=\"rtl\"] .ml-6 { margin-left: 0; margin-right: 1.5rem; }\n[dir=\"rtl\"] .ml-8 { margin-left: 0; margin-right: 2rem; }\n[dir=\"rtl\"] .ml-auto { margin-left: 0; margin-right: auto; }\n\n[dir=\"rtl\"] .mr-2 { margin-right: 0; margin-left: 0.5rem; }\n[dir=\"rtl\"] .mr-3 { margin-right: 0; margin-left: 0.75rem; }\n[dir=\"rtl\"] .mr-4 { margin-right: 0; margin-left: 1rem; }\n[dir=\"rtl\"] .mr-6 { margin-right: 0; margin-left: 1.5rem; }\n[dir=\"rtl\"] .mr-8 { margin-right: 0; margin-left: 2rem; }\n[dir=\"rtl\"] .mr-auto { margin-right: 0; margin-left: auto; }\n\n/* RTL Padding fixes */\n[dir=\"rtl\"] .pl-2 { padding-left: 0; padding-right: 0.5rem; }\n[dir=\"rtl\"] .pl-3 { padding-left: 0; padding-right: 0.75rem; }\n[dir=\"rtl\"] .pl-4 { padding-left: 0; padding-right: 1rem; }\n[dir=\"rtl\"] .pl-6 { padding-left: 0; padding-right: 1.5rem; }\n[dir=\"rtl\"] .pl-8 { padding-left: 0; padding-right: 2rem; }\n\n[dir=\"rtl\"] .pr-2 { padding-right: 0; padding-left: 0.5rem; }\n[dir=\"rtl\"] .pr-3 { padding-right: 0; padding-left: 0.75rem; }\n[dir=\"rtl\"] .pr-4 { padding-right: 0; padding-left: 1rem; }\n[dir=\"rtl\"] .pr-6 { padding-right: 0; padding-left: 1.5rem; }\n[dir=\"rtl\"] .pr-8 { padding-right: 0; padding-left: 2rem; }\n\n/* RTL Text alignment */\n[dir=\"rtl\"] .text-left { text-align: right; }\n[dir=\"rtl\"] .text-right { text-align: left; }\n\n/* RTL Flexbox */\n[dir=\"rtl\"] .justify-start { justify-content: flex-end; }\n[dir=\"rtl\"] .justify-end { justify-content: flex-start; }\n\n/* RTL Border radius */\n[dir=\"rtl\"] .rounded-l-lg {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n  border-top-right-radius: 0.5rem;\n  border-bottom-right-radius: 0.5rem;\n}\n\n[dir=\"rtl\"] .rounded-r-lg {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n  border-top-left-radius: 0.5rem;\n  border-bottom-left-radius: 0.5rem;\n}\n\n/* RTL Typography improvements */\n[dir=\"rtl\"] h1, [dir=\"rtl\"] h2, [dir=\"rtl\"] h3, [dir=\"rtl\"] h4, [dir=\"rtl\"] h5, [dir=\"rtl\"] h6 {\n  font-weight: 600;\n  line-height: 1.4;\n}\n\n[dir=\"rtl\"] p {\n  line-height: 1.7;\n}\n\n/* تحسينات إضافية للأجهزة المحمولة */\n@media (max-width: 767px) {\n  .mobile-stack{\n    flex-direction: column;\n  }\n\n  .mobile-full-width{\n    width: 100%;\n  }\n\n  .mobile-center{\n    text-align: center;\n  }\n\n  .mobile-hidden{\n    display: none;\n  }\n\n  .mobile-visible{\n    display: block;\n  }\n\n  .mobile-touch-scroll{\n    overflow: auto;\n    -webkit-overflow-scrolling: touch;\n  }\n\n  .mobile-no-scroll{\n    overflow: hidden;\n  }\n\n  .mobile-spacing{\n    margin-bottom: 1.5rem;\n  }\n\n  /* تحسين حجم الخط للقراءة على الأجهزة المحمولة */\n  .mobile-text-sm{\n    font-size: 0.875rem;\n    line-height: 1.25rem;\n  }\n\n  .mobile-text-base{\n    font-size: 1rem;\n    line-height: 1.5rem;\n  }\n\n  .mobile-text-lg{\n    font-size: 1.125rem;\n    line-height: 1.75rem;\n  }\n}\n\n/* تأثيرات الحركة */\n.hover-glow{\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 300ms;\n}\n\n.hover-glow:hover{\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  box-shadow: 0 0 15px rgba(var(--primary-500), 0.5);\n}\n\n.dark .hover-glow:hover {\n  box-shadow: 0 0 15px rgba(var(--primary-400), 0.5);\n}\n\n/* تأثير التحويم للصور */\n.img-hover-zoom{\n  overflow: hidden;\n}\n\n.img-hover-zoom img{\n  transition-property: transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 500ms;\n}\n\n.img-hover-zoom:hover img{\n  --tw-scale-x: 1.1;\n  --tw-scale-y: 1.1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n/* أنماط التباين العالي لتحسين إمكانية الوصول */\n.high-contrast {\n  --primary-500: 249 115 22;\n  --primary-600: 234 88 12;\n  --accent-500: 168 85 247;\n  --accent-600: 147 51 234;\n  --secondary-500: 6 182 212;\n  --secondary-600: 8 145 178;\n\n  /* زيادة تباين النصوص */\n  --slate-500: 30 41 59;\n  --slate-600: 15 23 42;\n  --slate-700: 15 23 42;\n  --slate-800: 15 23 42;\n  --slate-900: 2 6 23;\n}\n\n.high-contrast.dark {\n  --primary-500: 251 146 60;\n  --primary-600: 249 115 22;\n  --accent-500: 192 132 252;\n  --accent-600: 168 85 247;\n  --secondary-500: 34 211 238;\n  --secondary-600: 6 182 212;\n\n  /* زيادة تباين النصوص في الوضع المظلم */\n  --slate-100: 255 255 255;\n  --slate-200: 241 245 249;\n  --slate-300: 226 232 240;\n  --slate-400: 226 232 240;\n  --slate-500: 226 232 240;\n}\n\n/* تحسينات إمكانية الوصول للروابط */\na:focus-visible{\n  outline-width: 2px;\n  outline-offset: 2px;\n  outline-color: #A855F7;\n}\n\n/* تحسين التركيز على العناصر التفاعلية */\nbutton:focus-visible,\ninput:focus-visible,\nselect:focus-visible,\ntextarea:focus-visible{\n  outline-width: 2px;\n  outline-offset: 2px;\n  outline-color: #A855F7;\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));\n  --tw-ring-offset-width: 2px;\n}\n.file\\:border-0::file-selector-button{\n  border-width: 0px;\n}\n.file\\:bg-transparent::file-selector-button{\n  background-color: transparent;\n}\n.file\\:font-medium::file-selector-button{\n  font-weight: 500;\n}\n.placeholder\\:text-slate-400::-moz-placeholder{\n  --tw-text-opacity: 1;\n  color: rgb(148 163 184 / var(--tw-text-opacity, 1));\n}\n.placeholder\\:text-slate-400::placeholder{\n  --tw-text-opacity: 1;\n  color: rgb(148 163 184 / var(--tw-text-opacity, 1));\n}\n.placeholder\\:text-slate-500::-moz-placeholder{\n  --tw-text-opacity: 1;\n  color: rgb(100 116 139 / var(--tw-text-opacity, 1));\n}\n.placeholder\\:text-slate-500::placeholder{\n  --tw-text-opacity: 1;\n  color: rgb(100 116 139 / var(--tw-text-opacity, 1));\n}\n.after\\:absolute::after{\n  content: var(--tw-content);\n  position: absolute;\n}\n.after\\:bottom-0::after{\n  content: var(--tw-content);\n  bottom: 0px;\n}\n.after\\:left-1\\/2::after{\n  content: var(--tw-content);\n  left: 50%;\n}\n.after\\:left-3::after{\n  content: var(--tw-content);\n  left: 0.75rem;\n}\n.after\\:right-1\\/2::after{\n  content: var(--tw-content);\n  right: 50%;\n}\n.after\\:right-3::after{\n  content: var(--tw-content);\n  right: 0.75rem;\n}\n.after\\:h-0\\.5::after{\n  content: var(--tw-content);\n  height: 0.125rem;\n}\n.after\\:rounded-full::after{\n  content: var(--tw-content);\n  border-radius: 9999px;\n}\n.after\\:bg-gradient-to-r::after{\n  content: var(--tw-content);\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n}\n.after\\:from-primary-500::after{\n  content: var(--tw-content);\n  --tw-gradient-from: #A855F7 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.after\\:to-accent-500::after{\n  content: var(--tw-content);\n  --tw-gradient-to: #F97316 var(--tw-gradient-to-position);\n}\n.after\\:transition-all::after{\n  content: var(--tw-content);\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.after\\:duration-500::after{\n  content: var(--tw-content);\n  transition-duration: 500ms;\n}\n.last\\:border-b-0:last-child{\n  border-bottom-width: 0px;\n}\n.hover\\:-translate-x-1:hover{\n  --tw-translate-x: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.hover\\:-translate-y-1:hover{\n  --tw-translate-y: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.hover\\:-translate-y-2:hover{\n  --tw-translate-y: -0.5rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.hover\\:translate-x-1:hover{\n  --tw-translate-x: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.hover\\:translate-y-\\[-2px\\]:hover{\n  --tw-translate-y: -2px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.hover\\:scale-105:hover{\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.hover\\:scale-110:hover{\n  --tw-scale-x: 1.1;\n  --tw-scale-y: 1.1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.hover\\:border-blue-300:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-blue-500:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-gray-300:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-pink-500:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(236 72 153 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-primary-200:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-primary-300:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(216 180 254 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-primary-500:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-primary-600:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-red-300:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-red-400:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-slate-200:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-slate-300:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-slate-500:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-slate-600:hover{\n  --tw-border-opacity: 1;\n  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-l-primary-500:hover{\n  --tw-border-opacity: 1;\n  border-left-color: rgb(168 85 247 / var(--tw-border-opacity, 1));\n}\n.hover\\:bg-blue-50:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-blue-600:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-blue-700:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-gray-100:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-gray-200:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-gray-50:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-gray-700:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-green-700:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-orange-50:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-pink-600:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(219 39 119 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-primary-100:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-primary-200:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(233 213 255 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-primary-50:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-primary-600:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-primary-700:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-red-50:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-red-500:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-red-600:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-red-900\\/20:hover{\n  background-color: rgb(127 29 29 / 0.2);\n}\n.hover\\:bg-secondary-200:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(165 243 252 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-secondary-700:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(14 116 144 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-slate-100:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-slate-100\\/80:hover{\n  background-color: rgb(241 245 249 / 0.8);\n}\n.hover\\:bg-slate-200:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-slate-200\\/20:hover{\n  background-color: rgb(226 232 240 / 0.2);\n}\n.hover\\:bg-slate-50:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-slate-50\\/80:hover{\n  background-color: rgb(248 250 252 / 0.8);\n}\n.hover\\:bg-slate-600:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-slate-700:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-slate-800:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-slate-900\\/50:hover{\n  background-color: rgb(15 23 42 / 0.5);\n}\n.hover\\:bg-white:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-white\\/10:hover{\n  background-color: rgb(255 255 255 / 0.1);\n}\n.hover\\:bg-white\\/20:hover{\n  background-color: rgb(255 255 255 / 0.2);\n}\n.hover\\:bg-white\\/30:hover{\n  background-color: rgb(255 255 255 / 0.3);\n}\n.hover\\:bg-white\\/60:hover{\n  background-color: rgb(255 255 255 / 0.6);\n}\n.hover\\:bg-gradient-to-br:hover{\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\n}\n.hover\\:from-accent-600:hover{\n  --tw-gradient-from: #EA580C var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(234 88 12 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.hover\\:from-blue-50:hover{\n  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.hover\\:from-orange-100:hover{\n  --tw-gradient-from: #ffedd5 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 237 213 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.hover\\:from-primary-50:hover{\n  --tw-gradient-from: #FAF5FF var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(250 245 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.hover\\:from-primary-600:hover{\n  --tw-gradient-from: #9333EA var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.hover\\:from-purple-600:hover{\n  --tw-gradient-from: #9333ea var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.hover\\:from-secondary-600:hover{\n  --tw-gradient-from: #0891B2 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(8 145 178 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.hover\\:via-primary-700:hover{\n  --tw-gradient-to: rgb(126 34 206 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #7E22CE var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.hover\\:to-accent-700:hover{\n  --tw-gradient-to: #C2410C var(--tw-gradient-to-position);\n}\n.hover\\:to-amber-100:hover{\n  --tw-gradient-to: #fef3c7 var(--tw-gradient-to-position);\n}\n.hover\\:to-blue-50:hover{\n  --tw-gradient-to: #eff6ff var(--tw-gradient-to-position);\n}\n.hover\\:to-indigo-50:hover{\n  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);\n}\n.hover\\:to-primary-100:hover{\n  --tw-gradient-to: #F3E8FF var(--tw-gradient-to-position);\n}\n.hover\\:to-primary-700:hover{\n  --tw-gradient-to: #7E22CE var(--tw-gradient-to-position);\n}\n.hover\\:to-primary-800:hover{\n  --tw-gradient-to: #6B21A8 var(--tw-gradient-to-position);\n}\n.hover\\:to-purple-700:hover{\n  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);\n}\n.hover\\:to-secondary-700:hover{\n  --tw-gradient-to: #0E7490 var(--tw-gradient-to-position);\n}\n.hover\\:text-accent-400:hover{\n  --tw-text-opacity: 1;\n  color: rgb(251 146 60 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-blue-400:hover{\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-blue-600:hover{\n  --tw-text-opacity: 1;\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-blue-700:hover{\n  --tw-text-opacity: 1;\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-blue-800:hover{\n  --tw-text-opacity: 1;\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-gray-700:hover{\n  --tw-text-opacity: 1;\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-green-700:hover{\n  --tw-text-opacity: 1;\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-green-800:hover{\n  --tw-text-opacity: 1;\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-primary-300:hover{\n  --tw-text-opacity: 1;\n  color: rgb(216 180 254 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-primary-400:hover{\n  --tw-text-opacity: 1;\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-primary-500:hover{\n  --tw-text-opacity: 1;\n  color: rgb(168 85 247 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-primary-600:hover{\n  --tw-text-opacity: 1;\n  color: rgb(147 51 234 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-primary-700:hover{\n  --tw-text-opacity: 1;\n  color: rgb(126 34 206 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-primary-800:hover{\n  --tw-text-opacity: 1;\n  color: rgb(107 33 168 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-red-300:hover{\n  --tw-text-opacity: 1;\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-red-500:hover{\n  --tw-text-opacity: 1;\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-red-600:hover{\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-red-700:hover{\n  --tw-text-opacity: 1;\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-sky-600:hover{\n  --tw-text-opacity: 1;\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-slate-300:hover{\n  --tw-text-opacity: 1;\n  color: rgb(203 213 225 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-slate-600:hover{\n  --tw-text-opacity: 1;\n  color: rgb(71 85 105 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-slate-700:hover{\n  --tw-text-opacity: 1;\n  color: rgb(51 65 85 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-slate-800:hover{\n  --tw-text-opacity: 1;\n  color: rgb(30 41 59 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-slate-900:hover{\n  --tw-text-opacity: 1;\n  color: rgb(15 23 42 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-white:hover{\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.hover\\:underline:hover{\n  text-decoration-line: underline;\n}\n.hover\\:opacity-100:hover{\n  opacity: 1;\n}\n.hover\\:shadow:hover{\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.hover\\:shadow-2xl:hover{\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.hover\\:shadow-lg:hover{\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.hover\\:shadow-md:hover{\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.hover\\:shadow-xl:hover{\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.hover\\:shadow-accent-500\\/30:hover{\n  --tw-shadow-color: rgb(249 115 22 / 0.3);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.hover\\:shadow-blue-500\\/20:hover{\n  --tw-shadow-color: rgb(59 130 246 / 0.2);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.hover\\:shadow-orange-500\\/25:hover{\n  --tw-shadow-color: rgb(249 115 22 / 0.25);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.hover\\:shadow-pink-500\\/20:hover{\n  --tw-shadow-color: rgb(236 72 153 / 0.2);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.hover\\:shadow-primary-500\\/20:hover{\n  --tw-shadow-color: rgb(168 85 247 / 0.2);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.hover\\:shadow-purple-500\\/30:hover{\n  --tw-shadow-color: rgb(168 85 247 / 0.3);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.hover\\:after\\:left-3:hover::after{\n  content: var(--tw-content);\n  left: 0.75rem;\n}\n.hover\\:after\\:right-3:hover::after{\n  content: var(--tw-content);\n  right: 0.75rem;\n}\n.focus\\:not-sr-only:focus{\n  position: static;\n  width: auto;\n  height: auto;\n  padding: 0;\n  margin: 0;\n  overflow: visible;\n  clip: auto;\n  white-space: normal;\n}\n.focus\\:absolute:focus{\n  position: absolute;\n}\n.focus\\:left-4:focus{\n  left: 1rem;\n}\n.focus\\:left-6:focus{\n  left: 1.5rem;\n}\n.focus\\:top-4:focus{\n  top: 1rem;\n}\n.focus\\:top-7:focus{\n  top: 1.75rem;\n}\n.focus\\:z-50:focus{\n  z-index: 50;\n}\n.focus\\:h-auto:focus{\n  height: auto;\n}\n.focus\\:w-auto:focus{\n  width: auto;\n}\n.focus\\:overflow-visible:focus{\n  overflow: visible;\n}\n.focus\\:border:focus{\n  border-width: 1px;\n}\n.focus\\:border-black:focus{\n  --tw-border-opacity: 1;\n  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));\n}\n.focus\\:border-error-500:focus{\n  --tw-border-opacity: 1;\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\n}\n.focus\\:border-primary-500:focus{\n  --tw-border-opacity: 1;\n  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));\n}\n.focus\\:border-success-500:focus{\n  --tw-border-opacity: 1;\n  border-color: rgb(16 185 129 / var(--tw-border-opacity, 1));\n}\n.focus\\:border-transparent:focus{\n  border-color: transparent;\n}\n.focus\\:bg-white:focus{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\n.focus\\:p-2:focus{\n  padding: 0.5rem;\n}\n.focus\\:text-black:focus{\n  --tw-text-opacity: 1;\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\n}\n.focus\\:outline-none:focus{\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.focus\\:ring-2:focus{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.focus\\:ring-blue-500:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\n}\n.focus\\:ring-error-500:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));\n}\n.focus\\:ring-gray-500:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(107 114 128 / var(--tw-ring-opacity, 1));\n}\n.focus\\:ring-green-500:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));\n}\n.focus\\:ring-primary-200:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(233 213 255 / var(--tw-ring-opacity, 1));\n}\n.focus\\:ring-primary-200\\/50:focus{\n  --tw-ring-color: rgb(233 213 255 / 0.5);\n}\n.focus\\:ring-primary-500:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));\n}\n.focus\\:ring-primary-500\\/20:focus{\n  --tw-ring-color: rgb(168 85 247 / 0.2);\n}\n.focus\\:ring-primary-500\\/30:focus{\n  --tw-ring-color: rgb(168 85 247 / 0.3);\n}\n.focus\\:ring-primary-500\\/50:focus{\n  --tw-ring-color: rgb(168 85 247 / 0.5);\n}\n.focus\\:ring-red-500:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));\n}\n.focus\\:ring-success-500:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(16 185 129 / var(--tw-ring-opacity, 1));\n}\n.focus\\:ring-yellow-500:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(234 179 8 / var(--tw-ring-opacity, 1));\n}\n.focus\\:ring-offset-2:focus{\n  --tw-ring-offset-width: 2px;\n}\n.focus-visible\\:outline-none:focus-visible{\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.focus-visible\\:ring-2:focus-visible{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.focus-visible\\:ring-accent-500:focus-visible{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity, 1));\n}\n.focus-visible\\:ring-error-500:focus-visible{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));\n}\n.focus-visible\\:ring-primary-500:focus-visible{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));\n}\n.focus-visible\\:ring-red-500:focus-visible{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));\n}\n.focus-visible\\:ring-secondary-500:focus-visible{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(6 182 212 / var(--tw-ring-opacity, 1));\n}\n.focus-visible\\:ring-slate-500:focus-visible{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(100 116 139 / var(--tw-ring-opacity, 1));\n}\n.focus-visible\\:ring-white:focus-visible{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));\n}\n.focus-visible\\:ring-offset-2:focus-visible{\n  --tw-ring-offset-width: 2px;\n}\n.active\\:translate-y-0:active{\n  --tw-translate-y: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.active\\:scale-95:active{\n  --tw-scale-x: .95;\n  --tw-scale-y: .95;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.active\\:bg-slate-100:active{\n  --tw-bg-opacity: 1;\n  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));\n}\n.active\\:shadow-none:active{\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.disabled\\:pointer-events-none:disabled{\n  pointer-events: none;\n}\n.disabled\\:cursor-not-allowed:disabled{\n  cursor: not-allowed;\n}\n.disabled\\:opacity-50:disabled{\n  opacity: 0.5;\n}\n.group:hover .group-hover\\:visible{\n  visibility: visible;\n}\n.group:hover .group-hover\\:left-2{\n  left: 0.5rem;\n}\n.group:hover .group-hover\\:right-2{\n  right: 0.5rem;\n}\n.group:hover .group-hover\\:w-full{\n  width: 100%;\n}\n.group:hover .group-hover\\:-translate-y-1{\n  --tw-translate-y: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group:hover .group-hover\\:translate-x-1{\n  --tw-translate-x: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group:hover .group-hover\\:translate-x-2{\n  --tw-translate-x: 0.5rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group:hover .group-hover\\:translate-x-full{\n  --tw-translate-x: 100%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group:hover .group-hover\\:translate-y-\\[-8px\\]{\n  --tw-translate-y: -8px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group:hover .group-hover\\:rotate-12{\n  --tw-rotate: 12deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group:hover .group-hover\\:rotate-6{\n  --tw-rotate: 6deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group\\/feature:hover .group-hover\\/feature\\:scale-110{\n  --tw-scale-x: 1.1;\n  --tw-scale-y: 1.1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group:hover .group-hover\\:scale-105{\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group:hover .group-hover\\:scale-110{\n  --tw-scale-x: 1.1;\n  --tw-scale-y: 1.1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group:hover .group-hover\\:scale-125{\n  --tw-scale-x: 1.25;\n  --tw-scale-y: 1.25;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group:hover .group-hover\\:scale-\\[1\\.01\\]{\n  --tw-scale-x: 1.01;\n  --tw-scale-y: 1.01;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group:hover .group-hover\\:scale-x-100{\n  --tw-scale-x: 1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group:hover .group-hover\\:border-accent-500{\n  --tw-border-opacity: 1;\n  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));\n}\n.group:hover .group-hover\\:border-blue-500{\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n}\n.group:hover .group-hover\\:border-primary-300{\n  --tw-border-opacity: 1;\n  border-color: rgb(216 180 254 / var(--tw-border-opacity, 1));\n}\n.group:hover .group-hover\\:border-primary-500{\n  --tw-border-opacity: 1;\n  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));\n}\n.group:hover .group-hover\\:bg-accent-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));\n}\n.group:hover .group-hover\\:bg-accent-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));\n}\n.group:hover .group-hover\\:bg-blue-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\n.group:hover .group-hover\\:bg-primary-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));\n}\n.group:hover .group-hover\\:bg-primary-200{\n  --tw-bg-opacity: 1;\n  background-color: rgb(233 213 255 / var(--tw-bg-opacity, 1));\n}\n.group:hover .group-hover\\:bg-primary-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\n}\n.group:hover .group-hover\\:bg-primary-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));\n}\n.group:hover .group-hover\\:bg-opacity-20{\n  --tw-bg-opacity: 0.2;\n}\n.group:hover .group-hover\\:from-accent-500\\/20{\n  --tw-gradient-from: rgb(249 115 22 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.group:hover .group-hover\\:from-primary-200{\n  --tw-gradient-from: #E9D5FF var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(233 213 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.group:hover .group-hover\\:from-primary-500\\/20{\n  --tw-gradient-from: rgb(168 85 247 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.group:hover .group-hover\\:from-white\\/5{\n  --tw-gradient-from: rgb(255 255 255 / 0.05) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.group:hover .group-hover\\:via-primary-500\\/10{\n  --tw-gradient-to: rgb(168 85 247 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(168 85 247 / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.group:hover .group-hover\\:to-accent-500\\/20{\n  --tw-gradient-to: rgb(249 115 22 / 0.2) var(--tw-gradient-to-position);\n}\n.group:hover .group-hover\\:to-primary-300{\n  --tw-gradient-to: #D8B4FE var(--tw-gradient-to-position);\n}\n.group:hover .group-hover\\:to-primary-500\\/20{\n  --tw-gradient-to: rgb(168 85 247 / 0.2) var(--tw-gradient-to-position);\n}\n.group:hover .group-hover\\:to-white\\/10{\n  --tw-gradient-to: rgb(255 255 255 / 0.1) var(--tw-gradient-to-position);\n}\n.group\\/feature:hover .group-hover\\/feature\\:text-primary-700{\n  --tw-text-opacity: 1;\n  color: rgb(126 34 206 / var(--tw-text-opacity, 1));\n}\n.group\\/title:hover .group-hover\\/title\\:text-primary-600{\n  --tw-text-opacity: 1;\n  color: rgb(147 51 234 / var(--tw-text-opacity, 1));\n}\n.group:hover .group-hover\\:text-primary-500{\n  --tw-text-opacity: 1;\n  color: rgb(168 85 247 / var(--tw-text-opacity, 1));\n}\n.group:hover .group-hover\\:text-primary-600{\n  --tw-text-opacity: 1;\n  color: rgb(147 51 234 / var(--tw-text-opacity, 1));\n}\n.group:hover .group-hover\\:text-primary-700{\n  --tw-text-opacity: 1;\n  color: rgb(126 34 206 / var(--tw-text-opacity, 1));\n}\n.group:hover .group-hover\\:text-slate-700{\n  --tw-text-opacity: 1;\n  color: rgb(51 65 85 / var(--tw-text-opacity, 1));\n}\n.group:hover .group-hover\\:text-white{\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.group:hover .group-hover\\:opacity-100{\n  opacity: 1;\n}\n.group:hover .group-hover\\:opacity-40{\n  opacity: 0.4;\n}\n.group:hover .group-hover\\:shadow-lg{\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.group:hover .group-hover\\:shadow-md{\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.group:hover .group-hover\\:shadow-xl{\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.group:hover .group-hover\\:shadow-primary-500\\/10{\n  --tw-shadow-color: rgb(168 85 247 / 0.1);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.group:hover .group-hover\\:shadow-primary-500\\/20{\n  --tw-shadow-color: rgb(168 85 247 / 0.2);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.group:hover .group-hover\\:blur-0{\n  --tw-blur: blur(0);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.peer:disabled ~ .peer-disabled\\:cursor-not-allowed{\n  cursor: not-allowed;\n}\n.peer:disabled ~ .peer-disabled\\:opacity-70{\n  opacity: 0.7;\n}\n.dark\\:divide-slate-600:is(.dark *) > :not([hidden]) ~ :not([hidden]){\n  --tw-divide-opacity: 1;\n  border-color: rgb(71 85 105 / var(--tw-divide-opacity, 1));\n}\n.dark\\:divide-slate-700:is(.dark *) > :not([hidden]) ~ :not([hidden]){\n  --tw-divide-opacity: 1;\n  border-color: rgb(51 65 85 / var(--tw-divide-opacity, 1));\n}\n.dark\\:border-amber-800:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(146 64 14 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-blue-700:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-blue-800:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-blue-800\\/50:is(.dark *){\n  border-color: rgb(30 64 175 / 0.5);\n}\n.dark\\:border-green-700:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-green-800:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(22 101 52 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-orange-700:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(194 65 12 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-orange-800:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(154 52 18 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-primary-400:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-primary-600:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-primary-700:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(126 34 206 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-primary-700\\/30:is(.dark *){\n  border-color: rgb(126 34 206 / 0.3);\n}\n.dark\\:border-primary-700\\/50:is(.dark *){\n  border-color: rgb(126 34 206 / 0.5);\n}\n.dark\\:border-primary-800:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(107 33 168 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-primary-800\\/50:is(.dark *){\n  border-color: rgb(107 33 168 / 0.5);\n}\n.dark\\:border-red-700:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-red-800:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-slate-600:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-slate-600\\/50:is(.dark *){\n  border-color: rgb(71 85 105 / 0.5);\n}\n.dark\\:border-slate-600\\/80:is(.dark *){\n  border-color: rgb(71 85 105 / 0.8);\n}\n.dark\\:border-slate-700:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(51 65 85 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-slate-700\\/50:is(.dark *){\n  border-color: rgb(51 65 85 / 0.5);\n}\n.dark\\:border-slate-700\\/60:is(.dark *){\n  border-color: rgb(51 65 85 / 0.6);\n}\n.dark\\:border-slate-800:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(30 41 59 / var(--tw-border-opacity, 1));\n}\n.dark\\:border-yellow-700:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(161 98 7 / var(--tw-border-opacity, 1));\n}\n.dark\\:bg-accent-500\\/10:is(.dark *){\n  background-color: rgb(249 115 22 / 0.1);\n}\n.dark\\:bg-amber-900\\/20:is(.dark *){\n  background-color: rgb(120 53 15 / 0.2);\n}\n.dark\\:bg-blue-800:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-blue-900:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-blue-900\\/20:is(.dark *){\n  background-color: rgb(30 58 138 / 0.2);\n}\n.dark\\:bg-blue-900\\/30:is(.dark *){\n  background-color: rgb(30 58 138 / 0.3);\n}\n.dark\\:bg-cyan-900\\/30:is(.dark *){\n  background-color: rgb(22 78 99 / 0.3);\n}\n.dark\\:bg-gray-700:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-gray-800:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-gray-900:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-gray-900\\/30:is(.dark *){\n  background-color: rgb(17 24 39 / 0.3);\n}\n.dark\\:bg-green-800:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(22 101 52 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-green-900:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(20 83 45 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-green-900\\/20:is(.dark *){\n  background-color: rgb(20 83 45 / 0.2);\n}\n.dark\\:bg-green-900\\/30:is(.dark *){\n  background-color: rgb(20 83 45 / 0.3);\n}\n.dark\\:bg-indigo-900\\/30:is(.dark *){\n  background-color: rgb(49 46 129 / 0.3);\n}\n.dark\\:bg-orange-900:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(124 45 18 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-orange-900\\/20:is(.dark *){\n  background-color: rgb(124 45 18 / 0.2);\n}\n.dark\\:bg-orange-900\\/30:is(.dark *){\n  background-color: rgb(124 45 18 / 0.3);\n}\n.dark\\:bg-primary-400:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(192 132 252 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-primary-500\\/10:is(.dark *){\n  background-color: rgb(168 85 247 / 0.1);\n}\n.dark\\:bg-primary-500\\/20:is(.dark *){\n  background-color: rgb(168 85 247 / 0.2);\n}\n.dark\\:bg-primary-500\\/30:is(.dark *){\n  background-color: rgb(168 85 247 / 0.3);\n}\n.dark\\:bg-primary-800\\/50:is(.dark *){\n  background-color: rgb(107 33 168 / 0.5);\n}\n.dark\\:bg-primary-900:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(88 28 135 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-primary-900\\/20:is(.dark *){\n  background-color: rgb(88 28 135 / 0.2);\n}\n.dark\\:bg-primary-900\\/30:is(.dark *){\n  background-color: rgb(88 28 135 / 0.3);\n}\n.dark\\:bg-primary-900\\/40:is(.dark *){\n  background-color: rgb(88 28 135 / 0.4);\n}\n.dark\\:bg-primary-900\\/50:is(.dark *){\n  background-color: rgb(88 28 135 / 0.5);\n}\n.dark\\:bg-purple-900:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(88 28 135 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-purple-900\\/20:is(.dark *){\n  background-color: rgb(88 28 135 / 0.2);\n}\n.dark\\:bg-purple-900\\/30:is(.dark *){\n  background-color: rgb(88 28 135 / 0.3);\n}\n.dark\\:bg-red-600:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-red-800:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(153 27 27 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-red-900:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(127 29 29 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-red-900\\/20:is(.dark *){\n  background-color: rgb(127 29 29 / 0.2);\n}\n.dark\\:bg-red-900\\/30:is(.dark *){\n  background-color: rgb(127 29 29 / 0.3);\n}\n.dark\\:bg-secondary-900:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(22 78 99 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-secondary-900\\/30:is(.dark *){\n  background-color: rgb(22 78 99 / 0.3);\n}\n.dark\\:bg-slate-600:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-slate-700:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-slate-700\\/30:is(.dark *){\n  background-color: rgb(51 65 85 / 0.3);\n}\n.dark\\:bg-slate-700\\/50:is(.dark *){\n  background-color: rgb(51 65 85 / 0.5);\n}\n.dark\\:bg-slate-700\\/80:is(.dark *){\n  background-color: rgb(51 65 85 / 0.8);\n}\n.dark\\:bg-slate-800:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-slate-800\\/30:is(.dark *){\n  background-color: rgb(30 41 59 / 0.3);\n}\n.dark\\:bg-slate-800\\/50:is(.dark *){\n  background-color: rgb(30 41 59 / 0.5);\n}\n.dark\\:bg-slate-800\\/60:is(.dark *){\n  background-color: rgb(30 41 59 / 0.6);\n}\n.dark\\:bg-slate-800\\/80:is(.dark *){\n  background-color: rgb(30 41 59 / 0.8);\n}\n.dark\\:bg-slate-800\\/90:is(.dark *){\n  background-color: rgb(30 41 59 / 0.9);\n}\n.dark\\:bg-slate-800\\/95:is(.dark *){\n  background-color: rgb(30 41 59 / 0.95);\n}\n.dark\\:bg-slate-900:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-slate-900\\/30:is(.dark *){\n  background-color: rgb(15 23 42 / 0.3);\n}\n.dark\\:bg-slate-900\\/50:is(.dark *){\n  background-color: rgb(15 23 42 / 0.5);\n}\n.dark\\:bg-slate-900\\/80:is(.dark *){\n  background-color: rgb(15 23 42 / 0.8);\n}\n.dark\\:bg-slate-900\\/90:is(.dark *){\n  background-color: rgb(15 23 42 / 0.9);\n}\n.dark\\:bg-slate-950:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(2 6 23 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-success-900:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(6 78 59 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-success-900\\/30:is(.dark *){\n  background-color: rgb(6 78 59 / 0.3);\n}\n.dark\\:bg-warning-900\\/30:is(.dark *){\n  background-color: rgb(120 53 15 / 0.3);\n}\n.dark\\:bg-white\\/10:is(.dark *){\n  background-color: rgb(255 255 255 / 0.1);\n}\n.dark\\:bg-yellow-800:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(133 77 14 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-yellow-900:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(113 63 18 / var(--tw-bg-opacity, 1));\n}\n.dark\\:bg-yellow-900\\/30:is(.dark *){\n  background-color: rgb(113 63 18 / 0.3);\n}\n.dark\\:from-blue-900\\/20:is(.dark *){\n  --tw-gradient-from: rgb(30 58 138 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-blue-900\\/30:is(.dark *){\n  --tw-gradient-from: rgb(30 58 138 / 0.3) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-green-900\\/20:is(.dark *){\n  --tw-gradient-from: rgb(20 83 45 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(20 83 45 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-orange-900\\/20:is(.dark *){\n  --tw-gradient-from: rgb(124 45 18 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(124 45 18 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-pink-900\\/20:is(.dark *){\n  --tw-gradient-from: rgb(131 24 67 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(131 24 67 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-primary-400:is(.dark *){\n  --tw-gradient-from: #C084FC var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(192 132 252 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-primary-600:is(.dark *){\n  --tw-gradient-from: #9333EA var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-primary-700:is(.dark *){\n  --tw-gradient-from: #7E22CE var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(126 34 206 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-primary-900:is(.dark *){\n  --tw-gradient-from: #581C87 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-primary-900\\/10:is(.dark *){\n  --tw-gradient-from: rgb(88 28 135 / 0.1) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-primary-900\\/20:is(.dark *){\n  --tw-gradient-from: rgb(88 28 135 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-primary-900\\/30:is(.dark *){\n  --tw-gradient-from: rgb(88 28 135 / 0.3) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-primary-900\\/40:is(.dark *){\n  --tw-gradient-from: rgb(88 28 135 / 0.4) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-primary-900\\/50:is(.dark *){\n  --tw-gradient-from: rgb(88 28 135 / 0.5) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-primary-900\\/60:is(.dark *){\n  --tw-gradient-from: rgb(88 28 135 / 0.6) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-red-900\\/20:is(.dark *){\n  --tw-gradient-from: rgb(127 29 29 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(127 29 29 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-slate-700:is(.dark *){\n  --tw-gradient-from: #334155 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(51 65 85 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-slate-800:is(.dark *){\n  --tw-gradient-from: #1E293B var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(30 41 59 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-slate-800\\/50:is(.dark *){\n  --tw-gradient-from: rgb(30 41 59 / 0.5) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(30 41 59 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-slate-900:is(.dark *){\n  --tw-gradient-from: #0F172A var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(15 23 42 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-slate-950:is(.dark *){\n  --tw-gradient-from: #020617 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(2 6 23 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-white:is(.dark *){\n  --tw-gradient-from: #fff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:from-yellow-900\\/20:is(.dark *){\n  --tw-gradient-from: rgb(113 63 18 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(113 63 18 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:via-slate-800:is(.dark *){\n  --tw-gradient-to: rgb(30 41 59 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #1E293B var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.dark\\:via-slate-800\\/80:is(.dark *){\n  --tw-gradient-to: rgb(30 41 59 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(30 41 59 / 0.8) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.dark\\:via-slate-900:is(.dark *){\n  --tw-gradient-to: rgb(15 23 42 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #0F172A var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.dark\\:via-slate-900\\/80:is(.dark *){\n  --tw-gradient-to: rgb(15 23 42 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(15 23 42 / 0.8) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.dark\\:to-accent-400:is(.dark *){\n  --tw-gradient-to: #FB923C var(--tw-gradient-to-position);\n}\n.dark\\:to-accent-900\\/10:is(.dark *){\n  --tw-gradient-to: rgb(124 45 18 / 0.1) var(--tw-gradient-to-position);\n}\n.dark\\:to-accent-900\\/20:is(.dark *){\n  --tw-gradient-to: rgb(124 45 18 / 0.2) var(--tw-gradient-to-position);\n}\n.dark\\:to-accent-900\\/50:is(.dark *){\n  --tw-gradient-to: rgb(124 45 18 / 0.5) var(--tw-gradient-to-position);\n}\n.dark\\:to-amber-900\\/20:is(.dark *){\n  --tw-gradient-to: rgb(120 53 15 / 0.2) var(--tw-gradient-to-position);\n}\n.dark\\:to-blue-800\\/30:is(.dark *){\n  --tw-gradient-to: rgb(30 64 175 / 0.3) var(--tw-gradient-to-position);\n}\n.dark\\:to-blue-900\\/20:is(.dark *){\n  --tw-gradient-to: rgb(30 58 138 / 0.2) var(--tw-gradient-to-position);\n}\n.dark\\:to-blue-950:is(.dark *){\n  --tw-gradient-to: #172554 var(--tw-gradient-to-position);\n}\n.dark\\:to-emerald-900\\/20:is(.dark *){\n  --tw-gradient-to: rgb(6 78 59 / 0.2) var(--tw-gradient-to-position);\n}\n.dark\\:to-green-800\\/30:is(.dark *){\n  --tw-gradient-to: rgb(22 101 52 / 0.3) var(--tw-gradient-to-position);\n}\n.dark\\:to-primary-600:is(.dark *){\n  --tw-gradient-to: #9333EA var(--tw-gradient-to-position);\n}\n.dark\\:to-primary-700:is(.dark *){\n  --tw-gradient-to: #7E22CE var(--tw-gradient-to-position);\n}\n.dark\\:to-primary-800:is(.dark *){\n  --tw-gradient-to: #6B21A8 var(--tw-gradient-to-position);\n}\n.dark\\:to-primary-800\\/30:is(.dark *){\n  --tw-gradient-to: rgb(107 33 168 / 0.3) var(--tw-gradient-to-position);\n}\n.dark\\:to-primary-800\\/60:is(.dark *){\n  --tw-gradient-to: rgb(107 33 168 / 0.6) var(--tw-gradient-to-position);\n}\n.dark\\:to-primary-900\\/20:is(.dark *){\n  --tw-gradient-to: rgb(88 28 135 / 0.2) var(--tw-gradient-to-position);\n}\n.dark\\:to-red-800\\/30:is(.dark *){\n  --tw-gradient-to: rgb(153 27 27 / 0.3) var(--tw-gradient-to-position);\n}\n.dark\\:to-red-900\\/20:is(.dark *){\n  --tw-gradient-to: rgb(127 29 29 / 0.2) var(--tw-gradient-to-position);\n}\n.dark\\:to-rose-900\\/20:is(.dark *){\n  --tw-gradient-to: rgb(136 19 55 / 0.2) var(--tw-gradient-to-position);\n}\n.dark\\:to-slate-200:is(.dark *){\n  --tw-gradient-to: #E2E8F0 var(--tw-gradient-to-position);\n}\n.dark\\:to-slate-700:is(.dark *){\n  --tw-gradient-to: #334155 var(--tw-gradient-to-position);\n}\n.dark\\:to-slate-800:is(.dark *){\n  --tw-gradient-to: #1E293B var(--tw-gradient-to-position);\n}\n.dark\\:to-slate-800\\/50:is(.dark *){\n  --tw-gradient-to: rgb(30 41 59 / 0.5) var(--tw-gradient-to-position);\n}\n.dark\\:to-slate-900:is(.dark *){\n  --tw-gradient-to: #0F172A var(--tw-gradient-to-position);\n}\n.dark\\:to-slate-900\\/20:is(.dark *){\n  --tw-gradient-to: rgb(15 23 42 / 0.2) var(--tw-gradient-to-position);\n}\n.dark\\:to-slate-900\\/50:is(.dark *){\n  --tw-gradient-to: rgb(15 23 42 / 0.5) var(--tw-gradient-to-position);\n}\n.dark\\:to-yellow-800\\/30:is(.dark *){\n  --tw-gradient-to: rgb(133 77 14 / 0.3) var(--tw-gradient-to-position);\n}\n.dark\\:text-accent-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(251 146 60 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-amber-200:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(253 230 138 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-amber-300:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(252 211 77 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-blue-100:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(219 234 254 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-blue-200:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(191 219 254 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-blue-300:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-blue-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-blue-500:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(59 130 246 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-cyan-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(34 211 238 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-gray-100:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(243 244 246 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-gray-300:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-gray-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-green-100:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(220 252 231 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-green-200:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(187 247 208 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-green-300:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(134 239 172 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-green-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-green-500:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-indigo-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(129 140 248 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-orange-100:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(255 237 213 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-orange-300:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(253 186 116 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-orange-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(251 146 60 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-pink-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(244 114 182 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-primary-300:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(216 180 254 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-primary-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-primary-400\\/70:is(.dark *){\n  color: rgb(192 132 252 / 0.7);\n}\n.dark\\:text-primary-700:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(126 34 206 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-purple-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-red-200:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(254 202 202 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-red-300:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-red-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-secondary-300:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(103 232 249 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-sky-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(56 189 248 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-slate-100:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(241 245 249 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-slate-200:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(226 232 240 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-slate-300:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(203 213 225 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-slate-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(148 163 184 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-slate-500:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(100 116 139 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-slate-600:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(71 85 105 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-success-500:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(16 185 129 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-white:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-yellow-200:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(254 240 138 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-yellow-300:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(253 224 71 / var(--tw-text-opacity, 1));\n}\n.dark\\:text-yellow-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\n}\n.dark\\:decoration-slate-600:is(.dark *){\n  text-decoration-color: #475569;\n}\n.dark\\:accent-primary-400:is(.dark *){\n  accent-color: #C084FC;\n}\n.dark\\:shadow-primary-900\\/20:is(.dark *){\n  --tw-shadow-color: rgb(88 28 135 / 0.2);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.dark\\:ring-primary-800:is(.dark *){\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(107 33 168 / var(--tw-ring-opacity, 1));\n}\n.dark\\:ring-offset-slate-800:is(.dark *){\n  --tw-ring-offset-color: #1E293B;\n}\n.dark\\:placeholder\\:text-slate-500:is(.dark *)::-moz-placeholder{\n  --tw-text-opacity: 1;\n  color: rgb(100 116 139 / var(--tw-text-opacity, 1));\n}\n.dark\\:placeholder\\:text-slate-500:is(.dark *)::placeholder{\n  --tw-text-opacity: 1;\n  color: rgb(100 116 139 / var(--tw-text-opacity, 1));\n}\n.dark\\:hover\\:border-blue-700:hover:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));\n}\n.dark\\:hover\\:border-primary-400:hover:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));\n}\n.dark\\:hover\\:border-primary-500:hover:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));\n}\n.dark\\:hover\\:border-primary-600:hover:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));\n}\n.dark\\:hover\\:border-primary-800\\/70:hover:is(.dark *){\n  border-color: rgb(107 33 168 / 0.7);\n}\n.dark\\:hover\\:border-red-700:hover:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));\n}\n.dark\\:hover\\:border-slate-600:hover:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));\n}\n.dark\\:hover\\:bg-blue-900\\/20:hover:is(.dark *){\n  background-color: rgb(30 58 138 / 0.2);\n}\n.dark\\:hover\\:bg-orange-900\\/20:hover:is(.dark *){\n  background-color: rgb(124 45 18 / 0.2);\n}\n.dark\\:hover\\:bg-primary-800\\/50:hover:is(.dark *){\n  background-color: rgb(107 33 168 / 0.5);\n}\n.dark\\:hover\\:bg-primary-900\\/20:hover:is(.dark *){\n  background-color: rgb(88 28 135 / 0.2);\n}\n.dark\\:hover\\:bg-primary-900\\/30:hover:is(.dark *){\n  background-color: rgb(88 28 135 / 0.3);\n}\n.dark\\:hover\\:bg-primary-900\\/50:hover:is(.dark *){\n  background-color: rgb(88 28 135 / 0.5);\n}\n.dark\\:hover\\:bg-red-700:hover:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));\n}\n.dark\\:hover\\:bg-red-900\\/20:hover:is(.dark *){\n  background-color: rgb(127 29 29 / 0.2);\n}\n.dark\\:hover\\:bg-secondary-900\\/50:hover:is(.dark *){\n  background-color: rgb(22 78 99 / 0.5);\n}\n.dark\\:hover\\:bg-slate-600:hover:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));\n}\n.dark\\:hover\\:bg-slate-700:hover:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\n}\n.dark\\:hover\\:bg-slate-700\\/50:hover:is(.dark *){\n  background-color: rgb(51 65 85 / 0.5);\n}\n.dark\\:hover\\:bg-slate-700\\/70:hover:is(.dark *){\n  background-color: rgb(51 65 85 / 0.7);\n}\n.dark\\:hover\\:bg-slate-800:hover:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\n}\n.dark\\:hover\\:bg-slate-800\\/30:hover:is(.dark *){\n  background-color: rgb(30 41 59 / 0.3);\n}\n.dark\\:hover\\:bg-slate-800\\/50:hover:is(.dark *){\n  background-color: rgb(30 41 59 / 0.5);\n}\n.dark\\:hover\\:bg-success-900\\/50:hover:is(.dark *){\n  background-color: rgb(6 78 59 / 0.5);\n}\n.dark\\:hover\\:bg-warning-900\\/50:hover:is(.dark *){\n  background-color: rgb(120 53 15 / 0.5);\n}\n.dark\\:hover\\:from-orange-900\\/30:hover:is(.dark *){\n  --tw-gradient-from: rgb(124 45 18 / 0.3) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(124 45 18 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.dark\\:hover\\:to-amber-900\\/30:hover:is(.dark *){\n  --tw-gradient-to: rgb(120 53 15 / 0.3) var(--tw-gradient-to-position);\n}\n.dark\\:hover\\:to-blue-950:hover:is(.dark *){\n  --tw-gradient-to: #172554 var(--tw-gradient-to-position);\n}\n.dark\\:hover\\:text-blue-300:hover:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\n}\n.dark\\:hover\\:text-blue-400:hover:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\n.dark\\:hover\\:text-green-300:hover:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(134 239 172 / var(--tw-text-opacity, 1));\n}\n.dark\\:hover\\:text-primary-300:hover:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(216 180 254 / var(--tw-text-opacity, 1));\n}\n.dark\\:hover\\:text-primary-400:hover:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\n}\n.dark\\:hover\\:text-red-300:hover:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\n}\n.dark\\:hover\\:text-red-400:hover:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\n}\n.dark\\:hover\\:text-sky-300:hover:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(125 211 252 / var(--tw-text-opacity, 1));\n}\n.dark\\:hover\\:text-slate-200:hover:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(226 232 240 / var(--tw-text-opacity, 1));\n}\n.dark\\:hover\\:text-slate-300:hover:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(203 213 225 / var(--tw-text-opacity, 1));\n}\n.dark\\:hover\\:text-white:hover:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.dark\\:hover\\:shadow-purple-400\\/20:hover:is(.dark *){\n  --tw-shadow-color: rgb(192 132 252 / 0.2);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.dark\\:focus\\:ring-primary-600:focus:is(.dark *){\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(147 51 234 / var(--tw-ring-opacity, 1));\n}\n.dark\\:focus\\:ring-primary-800:focus:is(.dark *){\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(107 33 168 / var(--tw-ring-opacity, 1));\n}\n.dark\\:focus\\:ring-primary-800\\/50:focus:is(.dark *){\n  --tw-ring-color: rgb(107 33 168 / 0.5);\n}\n.dark\\:focus\\:ring-offset-slate-800:focus:is(.dark *){\n  --tw-ring-offset-color: #1E293B;\n}\n.dark\\:focus-visible\\:ring-primary-400:focus-visible:is(.dark *){\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(192 132 252 / var(--tw-ring-opacity, 1));\n}\n.dark\\:active\\:bg-slate-800:active:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\n}\n.group:hover .dark\\:group-hover\\:bg-slate-600:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));\n}\n.group:hover .dark\\:group-hover\\:from-primary-800:is(.dark *){\n  --tw-gradient-from: #6B21A8 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(107 33 168 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.group:hover .dark\\:group-hover\\:to-primary-700:is(.dark *){\n  --tw-gradient-to: #7E22CE var(--tw-gradient-to-position);\n}\n.group\\/feature:hover .dark\\:group-hover\\/feature\\:text-primary-300:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(216 180 254 / var(--tw-text-opacity, 1));\n}\n.group\\/title:hover .dark\\:group-hover\\/title\\:text-primary-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\n}\n.group:hover .dark\\:group-hover\\:text-primary-400:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\n}\n.group:hover .dark\\:group-hover\\:text-slate-300:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(203 213 225 / var(--tw-text-opacity, 1));\n}\n@media (min-width: 640px){\n\n  .sm\\:col-span-2{\n    grid-column: span 2 / span 2;\n  }\n\n  .sm\\:mb-0{\n    margin-bottom: 0px;\n  }\n\n  .sm\\:mt-0{\n    margin-top: 0px;\n  }\n\n  .sm\\:block{\n    display: block;\n  }\n\n  .sm\\:inline{\n    display: inline;\n  }\n\n  .sm\\:flex{\n    display: flex;\n  }\n\n  .sm\\:w-48{\n    width: 12rem;\n  }\n\n  .sm\\:w-auto{\n    width: auto;\n  }\n\n  .sm\\:max-w-3xl{\n    max-width: 48rem;\n  }\n\n  .sm\\:max-w-none{\n    max-width: none;\n  }\n\n  .sm\\:grid-cols-2{\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .sm\\:grid-cols-3{\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .sm\\:flex-row{\n    flex-direction: row;\n  }\n\n  .sm\\:items-center{\n    align-items: center;\n  }\n\n  .sm\\:justify-start{\n    justify-content: flex-start;\n  }\n\n  .sm\\:justify-end{\n    justify-content: flex-end;\n  }\n\n  .sm\\:justify-between{\n    justify-content: space-between;\n  }\n\n  .sm\\:space-x-2 > :not([hidden]) ~ :not([hidden]){\n    --tw-space-x-reverse: 0;\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .sm\\:px-6{\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\n\n  .sm\\:px-8{\n    padding-left: 2rem;\n    padding-right: 2rem;\n  }\n\n  .sm\\:text-left{\n    text-align: left;\n  }\n\n  .sm\\:text-right{\n    text-align: right;\n  }\n}\n@media (min-width: 768px){\n\n  .md\\:col-span-1{\n    grid-column: span 1 / span 1;\n  }\n\n  .md\\:col-span-2{\n    grid-column: span 2 / span 2;\n  }\n\n  .md\\:col-span-3{\n    grid-column: span 3 / span 3;\n  }\n\n  .md\\:mb-0{\n    margin-bottom: 0px;\n  }\n\n  .md\\:mb-12{\n    margin-bottom: 3rem;\n  }\n\n  .md\\:ml-64{\n    margin-left: 16rem;\n  }\n\n  .md\\:mt-0{\n    margin-top: 0px;\n  }\n\n  .md\\:block{\n    display: block;\n  }\n\n  .md\\:flex{\n    display: flex;\n  }\n\n  .md\\:hidden{\n    display: none;\n  }\n\n  .md\\:aspect-auto{\n    aspect-ratio: auto;\n  }\n\n  .md\\:h-full{\n    height: 100%;\n  }\n\n  .md\\:max-h-\\[2000px\\]{\n    max-height: 2000px;\n  }\n\n  .md\\:w-1\\/3{\n    width: 33.333333%;\n  }\n\n  .md\\:w-2\\/3{\n    width: 66.666667%;\n  }\n\n  .md\\:w-48{\n    width: 12rem;\n  }\n\n  .md\\:w-64{\n    width: 16rem;\n  }\n\n  .md\\:w-auto{\n    width: auto;\n  }\n\n  .md\\:min-w-\\[400px\\]{\n    min-width: 400px;\n  }\n\n  .md\\:max-w-md{\n    max-width: 28rem;\n  }\n\n  .md\\:translate-x-0{\n    --tw-translate-x: 0px;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  }\n\n  .md\\:grid-cols-2{\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-3{\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-4{\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-6{\n    grid-template-columns: repeat(6, minmax(0, 1fr));\n  }\n\n  .md\\:flex-row{\n    flex-direction: row;\n  }\n\n  .md\\:items-center{\n    align-items: center;\n  }\n\n  .md\\:justify-start{\n    justify-content: flex-start;\n  }\n\n  .md\\:justify-between{\n    justify-content: space-between;\n  }\n\n  .md\\:gap-8{\n    gap: 2rem;\n  }\n\n  .md\\:p-12{\n    padding: 3rem;\n  }\n\n  .md\\:p-6{\n    padding: 1.5rem;\n  }\n\n  .md\\:p-8{\n    padding: 2rem;\n  }\n\n  .md\\:px-6{\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\n\n  .md\\:py-16{\n    padding-top: 4rem;\n    padding-bottom: 4rem;\n  }\n\n  .md\\:py-20{\n    padding-top: 5rem;\n    padding-bottom: 5rem;\n  }\n\n  .md\\:py-24{\n    padding-top: 6rem;\n    padding-bottom: 6rem;\n  }\n\n  .md\\:py-28{\n    padding-top: 7rem;\n    padding-bottom: 7rem;\n  }\n\n  .md\\:pb-20{\n    padding-bottom: 5rem;\n  }\n\n  .md\\:pt-16{\n    padding-top: 4rem;\n  }\n\n  .md\\:pt-28{\n    padding-top: 7rem;\n  }\n\n  .md\\:pt-4{\n    padding-top: 1rem;\n  }\n\n  .md\\:text-left{\n    text-align: left;\n  }\n\n  .md\\:text-2xl{\n    font-size: 1.5rem;\n    line-height: 2rem;\n  }\n\n  .md\\:text-3xl{\n    font-size: 1.875rem;\n    line-height: 2.25rem;\n  }\n\n  .md\\:text-4xl{\n    font-size: 2.25rem;\n    line-height: 2.5rem;\n  }\n\n  .md\\:text-5xl{\n    font-size: 3rem;\n    line-height: 1;\n  }\n\n  .md\\:text-6xl{\n    font-size: 3.75rem;\n    line-height: 1;\n  }\n\n  .md\\:text-base{\n    font-size: 1rem;\n    line-height: 1.5rem;\n  }\n\n  .md\\:text-lg{\n    font-size: 1.125rem;\n    line-height: 1.75rem;\n  }\n\n  .md\\:text-xl{\n    font-size: 1.25rem;\n    line-height: 1.75rem;\n  }\n\n  .md\\:opacity-100{\n    opacity: 1;\n  }\n}\n@media (min-width: 1024px){\n\n  .lg\\:col-span-1{\n    grid-column: span 1 / span 1;\n  }\n\n  .lg\\:col-span-2{\n    grid-column: span 2 / span 2;\n  }\n\n  .lg\\:col-span-3{\n    grid-column: span 3 / span 3;\n  }\n\n  .lg\\:block{\n    display: block;\n  }\n\n  .lg\\:flex{\n    display: flex;\n  }\n\n  .lg\\:hidden{\n    display: none;\n  }\n\n  .lg\\:h-96{\n    height: 24rem;\n  }\n\n  .lg\\:w-1\\/2{\n    width: 50%;\n  }\n\n  .lg\\:w-1\\/3{\n    width: 33.333333%;\n  }\n\n  .lg\\:w-2\\/3{\n    width: 66.666667%;\n  }\n\n  .lg\\:w-32{\n    width: 8rem;\n  }\n\n  .lg\\:w-80{\n    width: 20rem;\n  }\n\n  .lg\\:w-auto{\n    width: auto;\n  }\n\n  .lg\\:grid-cols-2{\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-3{\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-4{\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-6{\n    grid-template-columns: repeat(6, minmax(0, 1fr));\n  }\n\n  .lg\\:flex-row{\n    flex-direction: row;\n  }\n\n  .lg\\:items-start{\n    align-items: flex-start;\n  }\n\n  .lg\\:items-center{\n    align-items: center;\n  }\n\n  .lg\\:justify-between{\n    justify-content: space-between;\n  }\n\n  .lg\\:gap-10{\n    gap: 2.5rem;\n  }\n\n  .lg\\:gap-8{\n    gap: 2rem;\n  }\n\n  .lg\\:px-12{\n    padding-left: 3rem;\n    padding-right: 3rem;\n  }\n\n  .lg\\:px-8{\n    padding-left: 2rem;\n    padding-right: 2rem;\n  }\n\n  .lg\\:text-2xl{\n    font-size: 1.5rem;\n    line-height: 2rem;\n  }\n\n  .lg\\:text-3xl{\n    font-size: 1.875rem;\n    line-height: 2.25rem;\n  }\n\n  .lg\\:text-4xl{\n    font-size: 2.25rem;\n    line-height: 2.5rem;\n  }\n\n  .lg\\:text-6xl{\n    font-size: 3.75rem;\n    line-height: 1;\n  }\n\n  .lg\\:text-7xl{\n    font-size: 4.5rem;\n    line-height: 1;\n  }\n}\n@media (min-width: 1280px){\n\n  .xl\\:grid-cols-3{\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .xl\\:grid-cols-4{\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n}\n.rtl\\:space-x-reverse:where([dir=\"rtl\"], [dir=\"rtl\"] *) > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 1;\n}"], "names": [], "mappings": "AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmHA;;;;;AASA;;;;AAeA;;;;;;;;;;;;AAkBA;;;;;AAWA;;;;;;AAUA;;;;AASA;;;;;AAcA;;;;;AASA;;;;AAYA;;;;;;;AAcA;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;AAUA;;;;;;AAYA;;;;;;;;;;;;;AAqBA;;;;AAUA;;;;;;AAaA;;;;AAQA;;;;AAQA;;;;AAQA;;;;AAUA;;;;;AASA;;;;AASA;;;;;AASA;;;;AAQA;;;;AAgBA;;;;;AAKA;;;;AAIA;;;;;;AAYA;;;;AAQA;;;;AASA;;;;;AAAA;;;;;AAeA;;;;AASA;;;;AAUA;;;;;AAgBA;;;;;AAQA;;;;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDA;;;;AAIA;;;;;;;;;;AAUA;;;;;;;AASA;EACI;;;;EAIA;;;;EAIA;;;;;AAKJ;;;;;AAKA;;;;;AAKA;EAEE;;;;;;AAMF;EAEE;;;;;;AAMF;;;;;AAKA;EAEE;;;;;;AAMF;EAEE;;;;;;AAMF;;;;;AAKA;EAEE;;;;;;AAMF;;;;;AAKA;EAEE;;;;;;AAMF;;;;;AAKA;EAEE;;;;;;AAMF;;;;;AAKA;EAEE;;;;;;AAMF;;;;;;;;AAQA;;;;;AAKA;;;;;AAKA;;;;;AAIA;;;;AAGA;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;;AAMA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAKA;;;;;;;AAOA;;;;AAIA;;;;;;;;AAOA;EAEE;;;;;;AAKF;EAEE;;;;;;AAOF;;;;;AAIA;EAEE;;;;;;AAKF;EAEE;;;;;;AAKF;;;;;;;;;;;;;;;;AAeA;;;;;;AAKA;;;;;;;;;;;;;;;;AAeA;;;;;;AAKA;;;;;;;;;;AASA;;;;;;AAKA;;;;;;;;;;AASA;;;;;;AAKA;;;;;;;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;;;;;;;AAYA;;;;AAGA;;;;;;;;;;;;;;;;;AAiBA;;;;AAGA;;;;;;;;;;AAUA;;;;AAGA;;;;;;;;;;;;AAYA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAAA;;;;;AAQA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAOA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAKA;;;;;;;;;;AASA;;;;AAIA;;;;;;;;;;;;;;AAYA;;;;AAIA;;;;AAKA;;;;AAKA;;;;AAMA;;;;;;;;;;AASA;;;;AAIA;;;;;;;;;;;;AAWA;;;;AAIA;;;;;;;;;;;;AAWA;;;;AAIA;;;;;;;;;;;;AAWA;;;;AAIA;;;;;;;;;;;;AAWA;;;;AAKA;;;;AAKA;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AAEA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AAGA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AAEA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AACA;;;;;AAGA;;;;AACA;;;;AAGA;;;;AACA;;;;AAGA;;;;AAOA;;;;AAQA;;;;;AAKA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;AAOF;;;;;;AAMA;;;;;;;AAOA;;;;AAKA;;;;AAIA;;;;;;AAMA;;;;;;AAOA;;;;;;;;;;;;;;AAgBA;;;;;;;;;;;;;;AAiBA;;;;;;AAOA;;;;;;;;;;;;AAcA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAAA;;;;;AAQA;;;;;AAAA;;;;;AAQA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;;AAMA;;;;;AAIA;;;;;;;AAMA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAUA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;;;;;;AAUA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;;AAQA;;;;;AAIA;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;AAIF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;AAIF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;AAKF;EAEE;;;;EAIA;;;;;AAIF", "debugId": null}}]}