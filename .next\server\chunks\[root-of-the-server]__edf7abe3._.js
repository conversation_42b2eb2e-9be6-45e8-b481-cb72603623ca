module.exports = {

"[project]/.next-internal/server/app/api/auth/[...nextauth]/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/better-sqlite3 [external] (better-sqlite3, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("better-sqlite3", () => require("better-sqlite3"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[project]/src/lib/sqlite.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * مكتبة SQLite للتعامل مع قاعدة البيانات المحلية
 * تستبدل هذه المكتبة استخدام Supabase في المشروع
 */ __turbopack_context__.s({
    "sqlite": (()=>sqlite)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm/v4.js [app-route] (ecmascript) <export default as v4>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)"); // Added for password hashing
;
;
const IS_SERVER = "undefined" === 'undefined';
const SALT_ROUNDS = 10; // For bcrypt hashing
let db = null;
if ("TURBOPACK compile-time truthy", 1) {
    try {
        const BetterSqlite3 = __turbopack_context__.r("[externals]/better-sqlite3 [external] (better-sqlite3, cjs)");
        const path = __turbopack_context__.r("[externals]/path [external] (path, cjs)");
        const fs = __turbopack_context__.r("[externals]/fs [external] (fs, cjs)");
        const dbDir = path.join(process.cwd(), 'server', 'db');
        const dbPath = path.join(dbDir, 'ecommerce.sqlite');
        if (!fs.existsSync(dbDir)) {
            fs.mkdirSync(dbDir, {
                recursive: true
            });
            console.log(`[DB_SERVER] Created database directory: ${dbDir}`);
        }
        console.log(`[DB_SERVER] Attempting to connect to SQLite database at: ${dbPath}`);
        db = new BetterSqlite3(dbPath, {}); // Verbose logging can be noisy
        console.log('[DB_SERVER] Successfully connected to SQLite database.');
        // Ensure tables are created (idempotent)
        db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        first_name TEXT,
        last_name TEXT,
        role TEXT DEFAULT 'user' NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        avatar_url TEXT,
        phone_number TEXT,
        addresses TEXT, -- Store as JSON string
        email_verified INTEGER DEFAULT 0, -- 0 for false, 1 for true
        last_login TEXT
      );
    `);
        console.log('[DB_SERVER] Users table checked/created.');
        db.exec(`
      CREATE TABLE IF NOT EXISTS products (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        slug TEXT UNIQUE NOT NULL,
        description TEXT,
        price REAL NOT NULL,
        compare_at_price REAL,
        images TEXT,
        category TEXT,
        tags TEXT,
        stock INTEGER DEFAULT 0,
        featured INTEGER DEFAULT 0,
        specifications TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        rating REAL,
        review_count INTEGER,
        related_products TEXT
      );
    `);
        console.log('[DB_SERVER] Products table checked/created.');
        // Create blog_posts table
        db.exec(`
      CREATE TABLE IF NOT EXISTS blog_posts (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        title_ar TEXT,
        slug TEXT UNIQUE NOT NULL,
        excerpt TEXT NOT NULL,
        excerpt_ar TEXT,
        content TEXT NOT NULL,
        content_ar TEXT,
        author TEXT NOT NULL,
        author_title TEXT,
        author_image TEXT,
        cover_image TEXT,
        category TEXT NOT NULL,
        tags TEXT,
        keywords TEXT,
        published_at TEXT NOT NULL,
        read_time TEXT,
        featured INTEGER DEFAULT 0,
        status TEXT DEFAULT 'draft',
        meta_title TEXT,
        meta_description TEXT,
        views INTEGER DEFAULT 0,
        likes INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT
      );
    `);
        console.log('[DB_SERVER] Blog posts table checked/created.');
        // Add other table creations here as needed (services, etc.)
        console.log('[DB_SERVER] Database schema checked/initialized.');
    } catch (error) {
        console.error('[DB_SERVER] Critical error during database initialization:', error);
        db = null;
    }
} else {
    "TURBOPACK unreachable";
}
// --- Server-side Helper Functions ---
async function _server_hashPassword(password) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(password, SALT_ROUNDS);
}
async function _server_comparePassword(password, hash) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(password, hash);
}
function _server_getUserByEmailWithPasswordHash(email) {
    if (!IS_SERVER || !db) return null;
    try {
        const stmt = db.prepare('SELECT * FROM users WHERE email = ?');
        const row = stmt.get(email.toLowerCase());
        return row ? mapUserFromDbRow(row) : null;
    } catch (error) {
        console.error('[DB_SERVER] Error in _server_getUserByEmailWithPasswordHash:', error);
        return null;
    }
}
function _server_getUserById(id) {
    if (!IS_SERVER || !db) return null;
    try {
        const stmt = db.prepare('SELECT * FROM users WHERE id = ?');
        const row = stmt.get(id);
        return row ? mapUserFromDbRow(row) : null;
    } catch (error) {
        console.error('[DB_SERVER] Error in _server_getUserById:', error);
        return null;
    }
}
function _server_createUser(userData) {
    if (!IS_SERVER || !db) return null;
    try {
        const now = new Date().toISOString();
        // Prepare addresses as JSON string if provided
        const addressesJson = userData.addresses && userData.addresses.length > 0 ? JSON.stringify(userData.addresses) : null;
        // Generate a new ID if not provided
        const userId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])();
        const stmt = db.prepare(`
      INSERT INTO users (
        id, email, password_hash, first_name, last_name, role,
        created_at, updated_at, avatar_url, phone_number,
        addresses, email_verified, last_login
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
        stmt.run(userId, userData.email.toLowerCase(), userData.password_hash, userData.firstName || '', userData.lastName || '', userData.role || 'user', now, now, userData.avatarUrl || null, userData.phoneNumber || null, addressesJson, userData.emailVerified ? 1 : 0, userData.lastLogin || null);
        return _server_getUserById(userId);
    } catch (error) {
        console.error('[DB_SERVER] Error in _server_createUser:', error);
        return null;
    }
}
function _server_getProducts() {
    if (!IS_SERVER || !db) return [];
    try {
        const stmt = db.prepare('SELECT * FROM products ORDER BY created_at DESC');
        const rows = stmt.all();
        return rows.map(mapProductFromDbRow);
    } catch (error) {
        console.error('[DB_SERVER] Error in _server_getProducts:', error);
        return [];
    }
}
function _server_createProduct(productData) {
    if (!IS_SERVER || !db) return null;
    const newId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])();
    const now = new Date().toISOString();
    try {
        const stmt = db.prepare(`
      INSERT INTO products (id, name, slug, description, price, compare_at_price, images, category, tags, stock, featured, specifications, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
        stmt.run(newId, productData.name, productData.slug, productData.description, productData.price, productData.compareAtPrice, JSON.stringify(productData.images || []), productData.category, JSON.stringify(productData.tags || []), productData.stock, productData.featured ? 1 : 0, JSON.stringify(productData.specifications || {}), now);
        // To get the full product, we'd ideally query it back, but for now let's construct it
        return {
            ...productData,
            id: newId,
            createdAt: now,
            reviews: [],
            rating: 0,
            reviewCount: 0
        }; // Cast as Product, assuming defaults for reviews/rating
    } catch (error) {
        console.error('[DB_SERVER] Error in _server_createProduct:', error);
        return null;
    }
}
// --- Helper function to safely parse JSON (used for localStorage) ---
function safeJsonParse(jsonString, defaultValue) {
    if (jsonString == null) return defaultValue;
    try {
        return JSON.parse(jsonString);
    } catch (e) {
        // console.error('Failed to parse JSON string:', e, '\nString was:', jsonString);
        return defaultValue;
    }
}
// --- localStorage keys ---
const USER_STORAGE_KEY = 'sqlite_users';
const PRODUCT_STORAGE_KEY = 'sqlite_products';
const BLOG_POSTS_STORAGE_KEY = 'sqlite_blog_posts';
// ... other keys
// --- Default data for client-side localStorage initialization ---
const DEFAULT_LOCAL_USERS = [
    {
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'Local',
        role: 'admin'
    },
    {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'Local',
        role: 'user'
    }
];
// ... DEFAULT_LOCAL_PRODUCTS (ensure it matches Product type, omitting server-generated fields)
const DEFAULT_LOCAL_PRODUCTS = [
    {
        name: 'Default Local Product 1',
        slug: 'default-local-product-1',
        description: 'This is a default product for localStorage.',
        price: 19.99,
        compareAtPrice: 29.99,
        images: [],
        category: 'Local Category',
        tags: [
            'default',
            'localstorage'
        ],
        stock: 100,
        featured: true,
        specifications: {
            material: 'local_plastic'
        }
    }
];
// --- Helper function to map database row to User type (includes password_hash for internal use) ---
function mapUserFromDbRow(row) {
    if (!row) return row;
    // Parse addresses from JSON string if present
    let addresses = [];
    try {
        if (row.addresses) {
            addresses = JSON.parse(row.addresses);
        }
    } catch (e) {
        console.error('[DB] Error parsing user addresses:', e);
    }
    // Map all fields from the database row to the User type
    const user = {
        id: row.id,
        email: row.email,
        firstName: row.first_name || '',
        lastName: row.last_name || '',
        role: row.role || 'user',
        createdAt: row.created_at,
        updatedAt: row.updated_at || null,
        avatarUrl: row.avatar_url || null,
        phoneNumber: row.phone_number || null,
        addresses: addresses,
        emailVerified: Boolean(row.email_verified),
        lastLogin: row.last_login || null
    };
    // Include password_hash for internal authentication
    if (row.password_hash) {
        user.password_hash = row.password_hash;
    }
    return user;
}
// --- Helper function to map database row to Product type ---
function mapProductFromDbRow(row) {
    if (!row) return null;
    return {
        id: row.id,
        name: row.name,
        slug: row.slug,
        description: row.description,
        price: row.price ? parseFloat(row.price) : 0,
        compareAtPrice: row.compare_at_price ? parseFloat(row.compare_at_price) : undefined,
        images: row.images ? safeJsonParse(row.images, []) : [],
        category: row.category,
        tags: row.tags ? safeJsonParse(row.tags, []) : [],
        stock: row.stock ? parseInt(row.stock, 10) : 0,
        featured: Boolean(row.featured),
        specifications: row.specifications ? safeJsonParse(row.specifications, {}) : {},
        createdAt: row.created_at,
        updatedAt: row.updated_at,
        reviews: [],
        rating: row.rating ? parseFloat(row.rating) : undefined,
        reviewCount: row.review_count ? parseInt(row.review_count, 10) : undefined,
        relatedProducts: row.related_products ? safeJsonParse(row.related_products, []) : []
    };
}
// --- Helper function to map database row to BlogPost type ---
function mapBlogPostFromDbRow(row) {
    if (!row) return null;
    return {
        id: row.id,
        title: row.title,
        title_ar: row.title_ar,
        slug: row.slug,
        excerpt: row.excerpt,
        excerpt_ar: row.excerpt_ar,
        content: row.content,
        content_ar: row.content_ar,
        author: row.author,
        authorTitle: row.author_title,
        authorImage: row.author_image,
        coverImage: row.cover_image,
        category: row.category,
        tags: row.tags ? safeJsonParse(row.tags, []) : [],
        keywords: row.keywords ? safeJsonParse(row.keywords, []) : [],
        publishedAt: row.published_at,
        readTime: row.read_time,
        featured: Boolean(row.featured),
        status: row.status || 'draft',
        metaTitle: row.meta_title,
        metaDescription: row.meta_description,
        views: row.views ? parseInt(row.views, 10) : 0,
        likes: row.likes ? parseInt(row.likes, 10) : 0,
        createdAt: row.created_at,
        updatedAt: row.updated_at
    };
}
class MockSQLiteDatabase {
    db_conn;
    users = [];
    products = [];
    blogPosts = [];
    // ... other local stores
    userKey = USER_STORAGE_KEY;
    productKey = PRODUCT_STORAGE_KEY;
    blogPostsKey = BLOG_POSTS_STORAGE_KEY;
    // ... other keys
    constructor(server_db_connection){
        this.db_conn = server_db_connection; // This is the actual 'db' object from server scope
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }
    loadFromLocalStorage(key, defaultValue) {
        if ("TURBOPACK compile-time truthy", 1) return defaultValue; // Should not happen with current constructor logic
        "TURBOPACK unreachable";
    }
    saveToLocalStorage(key, data) {
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    }
    _initializeDefaultLocalData() {
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    }
    // --- User Methods ---
    async getUsers() {
        if ("TURBOPACK compile-time truthy", 1) {
            if (!this.db_conn) return [];
            try {
                const stmt = this.db_conn.prepare('SELECT * FROM users');
                const rows = stmt.all();
                // Exclude password_hash from results sent to client/general use
                return rows.map(mapUserFromDbRow).map((userWithHash)=>{
                    const { password_hash, ...userWithoutHash } = userWithHash;
                    return userWithoutHash; // Ensure the final object is also User
                });
            } catch (error) {
                console.error('[DB_SERVER] Error in getUsers:', error);
                return [];
            }
        }
        // تأكد من أن this.users هو مصفوفة
        if (!Array.isArray(this.users)) {
            console.warn('[MockDB_Client] this.users is not an array, initializing empty array');
            this.users = [];
            this.saveToLocalStorage(this.userKey, this.users);
        }
        return [
            ...this.users
        ];
    }
    async getUserByEmail(email) {
        if ("TURBOPACK compile-time truthy", 1) {
            const userWithHash = _server_getUserByEmailWithPasswordHash(email.toLowerCase());
            if (userWithHash) {
                const { password_hash, ...userWithoutHash } = userWithHash;
                return userWithoutHash;
            }
            return null;
        }
        "TURBOPACK unreachable";
        const user = undefined;
    }
    async getUserById(id) {
        if ("TURBOPACK compile-time truthy", 1) {
            const userWithHash = _server_getUserById(id);
            if (userWithHash) {
                const { password_hash, ...userWithoutHash } = userWithHash;
                return userWithoutHash;
            }
            return null;
        }
        "TURBOPACK unreachable";
        const user = undefined;
    }
    async createUser(userData) {
        if ("TURBOPACK compile-time truthy", 1) {
            if (!this.db_conn) {
                console.error('[DB_SERVER] Database connection not available');
                return null;
            }
            if (!userData.email || !userData.password) {
                console.error('[DB_SERVER] Email and password required for user creation');
                return null;
            }
            try {
                // Check if user already exists
                const existingUser = _server_getUserByEmailWithPasswordHash(userData.email.toLowerCase());
                if (existingUser) {
                    console.error(`[DB_SERVER] User with email ${userData.email} already exists`);
                    return null;
                }
                // Create a new user with basic required fields
                const now = new Date().toISOString();
                const newUser = {
                    id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                    email: userData.email.toLowerCase(),
                    firstName: userData.firstName || '',
                    lastName: userData.lastName || '',
                    role: userData.role || 'user',
                    createdAt: now,
                    updatedAt: now,
                    avatarUrl: userData.avatarUrl || undefined,
                    phoneNumber: userData.phoneNumber || undefined,
                    addresses: userData.addresses || [],
                    emailVerified: userData.emailVerified || false,
                    lastLogin: undefined
                };
                // Hash the password
                const passwordHash = await _server_hashPassword(userData.password);
                // Create user in database including password hash
                // We need to remove fields that aren't in the expected type
                const { id, createdAt, updatedAt, ...userDataForCreate } = newUser;
                const user = _server_createUser({
                    ...userDataForCreate,
                    password_hash: passwordHash
                });
                if (!user) {
                    console.error('[DB_SERVER] Failed to create user in database');
                    return null;
                }
                // Don't return password hash to client
                const { password_hash, ...userWithoutHash } = user;
                return userWithoutHash;
            } catch (error) {
                console.error('[DB_SERVER] Error creating user:', error);
                return null;
            }
        }
        // Client-side mock
        try {
            // Check if email already exists in mock data
            const existingUser = this.users.find((u)=>u.email.toLowerCase() === userData.email.toLowerCase());
            if (existingUser) {
                console.error(`[MockDB_Client] User with email ${userData.email} already exists`);
                return null;
            }
            const now = new Date().toISOString();
            const newUser = {
                ...userData,
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
                email: userData.email.toLowerCase(),
                role: userData.role || 'user',
                createdAt: now,
                updatedAt: now,
                avatarUrl: userData.avatarUrl || undefined,
                phoneNumber: userData.phoneNumber || undefined,
                addresses: userData.addresses || [],
                emailVerified: userData.emailVerified || false,
                lastLogin: undefined
            };
            this.users.push(newUser);
            this.saveToLocalStorage(this.userKey, this.users);
            return {
                ...newUser
            };
        } catch (error) {
            console.error('[MockDB_Client] Error creating user:', error);
            return null;
        }
    }
    async authenticateUser(email, password) {
        if ("TURBOPACK compile-time truthy", 1) {
            if (!this.db_conn) {
                console.error('[DB_SERVER] Database connection not available');
                return null;
            }
            try {
                // First retrieve the user with password hash by email
                const user = _server_getUserByEmailWithPasswordHash(email.toLowerCase());
                if (!user || !user.password_hash) {
                    console.log('[DB_SERVER] User not found or missing password hash:', email);
                    return null;
                }
                // Compare provided password with stored hash
                const passwordMatch = await _server_comparePassword(password, user.password_hash);
                if (!passwordMatch) {
                    console.log('[DB_SERVER] Password mismatch for user:', email);
                    return null;
                }
                // Update last login time
                const now = new Date().toISOString();
                const updateStmt = this.db_conn.prepare('UPDATE users SET last_login = ?, email_verified = 1 WHERE id = ?');
                updateStmt.run(now, user.id);
                // Fetch the updated user record
                const updatedUser = _server_getUserById(user.id);
                if (!updatedUser) {
                    console.error('[DB_SERVER] Failed to fetch updated user after login');
                    return null;
                }
                // Don't return password hash to the client
                const { password_hash, ...userWithoutHash } = updatedUser;
                return userWithoutHash;
            } catch (error) {
                console.error('[DB_SERVER] Error during authentication:', error);
                return null;
            }
        }
        // Client-side mock authentication
        const user = this.users.find((u)=>u.email.toLowerCase() === email.toLowerCase());
        if (!user) {
            console.log('[MockDB_Client] User not found in mock database:', email);
            return null;
        }
        // In client mode, we simulate a successful login by updating last login
        const now = new Date().toISOString();
        const updatedUser = {
            ...user,
            lastLogin: now,
            emailVerified: true
        };
        // Update the user in local storage
        const userIndex = this.users.findIndex((u)=>u.id === user.id);
        if (userIndex !== -1) {
            this.users[userIndex] = updatedUser;
            this.saveToLocalStorage(this.userKey, this.users);
        }
        return updatedUser;
    }
    async updateUser(id, userData) {
        if ("TURBOPACK compile-time truthy", 1) {
            if (!this.db_conn) return null;
            const existingUser = _server_getUserById(id);
            if (!existingUser) return null;
            const fieldsToUpdate = {
                ...userData,
                updated_at: new Date().toISOString()
            };
            const setClauses = Object.keys(fieldsToUpdate).map((key)=>`${key.replace(/[A-Z]/g, (letter)=>`_${letter.toLowerCase()}`)} = ?`).join(', ');
            const values = Object.values(fieldsToUpdate);
            if (values.length === 0) return existingUser; // No fields to update
            try {
                const stmt = this.db_conn.prepare(`UPDATE users SET ${setClauses} WHERE id = ?`);
                stmt.run(...values, id);
                const updatedUser = _server_getUserById(id);
                if (updatedUser) {
                    const { password_hash, ...userWithoutHash } = updatedUser;
                    return userWithoutHash;
                }
                return null;
            } catch (error) {
                console.error('[DB_SERVER] Error in updateUser:', error);
                return null;
            }
        }
        // Client-side mock
        const userIndex = this.users.findIndex((u)=>u.id === id);
        if (userIndex === -1) return null;
        this.users[userIndex] = {
            ...this.users[userIndex],
            ...userData,
            updatedAt: new Date().toISOString()
        };
        this.saveToLocalStorage(this.userKey, this.users);
        return {
            ...this.users[userIndex]
        };
    }
    // --- Product Methods ---
    async getProducts() {
        if ("TURBOPACK compile-time truthy", 1) {
            return _server_getProducts();
        }
        "TURBOPACK unreachable";
    }
    async createProduct(productData) {
        if ("TURBOPACK compile-time truthy", 1) {
            return _server_createProduct(productData);
        }
        "TURBOPACK unreachable";
        // Client-side mock
        const newId = undefined;
        const now = undefined;
        const newProduct = undefined;
    }
    // ... (Other methods like getProductById, updateProduct, deleteProduct should follow similar pattern)
    async getProductById(id) {
        if ("TURBOPACK compile-time truthy", 1) {
            if (!this.db_conn) return null;
            try {
                const stmt = this.db_conn.prepare('SELECT * FROM products WHERE id = ?');
                const row = stmt.get(id);
                return row ? mapProductFromDbRow(row) : null;
            } catch (error) {
                console.error('[DB_SERVER] Error in getProductById:', error);
                return null;
            }
        }
        const product = this.products.find((p)=>p.id === id);
        return product ? {
            ...product
        } : null;
    }
    // --- Blog Post Methods ---
    getBlogPosts() {
        if ("TURBOPACK compile-time truthy", 1) {
            if (!this.db_conn) return [];
            try {
                const stmt = this.db_conn.prepare('SELECT * FROM blog_posts ORDER BY created_at DESC');
                const rows = stmt.all();
                return rows.map(mapBlogPostFromDbRow);
            } catch (error) {
                console.error('[DB_SERVER] Error in getBlogPosts:', error);
                return [];
            }
        }
        // تأكد من أن this.blogPosts هو مصفوفة
        if (!Array.isArray(this.blogPosts)) {
            console.warn('[MockDB_Client] this.blogPosts is not an array, initializing empty array');
            this.blogPosts = [];
            this.saveToLocalStorage(this.blogPostsKey, this.blogPosts);
        }
        return [
            ...this.blogPosts
        ];
    }
    saveBlogPosts(posts) {
        if ("TURBOPACK compile-time truthy", 1) {
            // For server-side, we don't use this method as we use individual CRUD operations
            console.warn('[DB_SERVER] saveBlogPosts called on server - use individual CRUD operations instead');
            return;
        }
        "TURBOPACK unreachable";
    }
    getBlogPostById(id) {
        if ("TURBOPACK compile-time truthy", 1) {
            if (!this.db_conn) return null;
            try {
                const stmt = this.db_conn.prepare('SELECT * FROM blog_posts WHERE id = ?');
                const row = stmt.get(id);
                return row ? mapBlogPostFromDbRow(row) : null;
            } catch (error) {
                console.error('[DB_SERVER] Error in getBlogPostById:', error);
                return null;
            }
        }
        const post = this.blogPosts.find((p)=>p.id === id);
        return post ? {
            ...post
        } : null;
    }
    getBlogPostBySlug(slug) {
        if ("TURBOPACK compile-time truthy", 1) {
            if (!this.db_conn) return null;
            try {
                const stmt = this.db_conn.prepare('SELECT * FROM blog_posts WHERE slug = ?');
                const row = stmt.get(slug);
                return row ? mapBlogPostFromDbRow(row) : null;
            } catch (error) {
                console.error('[DB_SERVER] Error in getBlogPostBySlug:', error);
                return null;
            }
        }
        const post = this.blogPosts.find((p)=>p.slug === slug);
        return post ? {
            ...post
        } : null;
    }
    createBlogPost(postData) {
        if ("TURBOPACK compile-time truthy", 1) {
            if (!this.db_conn) return null;
            try {
                const now = new Date().toISOString();
                const newId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])();
                const stmt = this.db_conn.prepare(`
          INSERT INTO blog_posts (
            id, title, title_ar, slug, excerpt, excerpt_ar, content, content_ar,
            author, author_title, author_image, cover_image, category, tags, keywords,
            published_at, read_time, featured, status, meta_title, meta_description,
            views, likes, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
                stmt.run(newId, postData.title, postData.title_ar || null, postData.slug, postData.excerpt, postData.excerpt_ar || null, postData.content, postData.content_ar || null, postData.author, postData.authorTitle || null, postData.authorImage || null, postData.coverImage || null, postData.category, JSON.stringify(postData.tags || []), JSON.stringify(postData.keywords || []), postData.publishedAt, postData.readTime || null, postData.featured ? 1 : 0, postData.status || 'draft', postData.metaTitle || null, postData.metaDescription || null, postData.views || 0, postData.likes || 0, now, now);
                return this.getBlogPostById(newId);
            } catch (error) {
                console.error('[DB_SERVER] Error in createBlogPost:', error);
                return null;
            }
        }
        // Client-side mock
        const now = new Date().toISOString();
        const newPost = {
            ...postData,
            id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
            createdAt: now,
            updatedAt: now
        };
        this.blogPosts.push(newPost);
        this.saveToLocalStorage(this.blogPostsKey, this.blogPosts);
        return {
            ...newPost
        };
    }
    updateBlogPost(id, postData) {
        if ("TURBOPACK compile-time truthy", 1) {
            if (!this.db_conn) return null;
            try {
                const now = new Date().toISOString();
                const updateData = {
                    ...postData,
                    updated_at: now
                };
                // Build dynamic update query
                const fields = Object.keys(updateData).filter((key)=>key !== 'id');
                const setClauses = fields.map((key)=>{
                    // Convert camelCase to snake_case for database columns
                    const dbKey = key.replace(/[A-Z]/g, (letter)=>`_${letter.toLowerCase()}`);
                    return `${dbKey} = ?`;
                }).join(', ');
                const values = fields.map((key)=>{
                    const value = updateData[key];
                    if (key === 'tags' || key === 'keywords') {
                        return JSON.stringify(value || []);
                    }
                    if (key === 'featured') {
                        return value ? 1 : 0;
                    }
                    return value;
                });
                const stmt = this.db_conn.prepare(`UPDATE blog_posts SET ${setClauses} WHERE id = ?`);
                stmt.run(...values, id);
                return this.getBlogPostById(id);
            } catch (error) {
                console.error('[DB_SERVER] Error in updateBlogPost:', error);
                return null;
            }
        }
        // Client-side mock
        const postIndex = this.blogPosts.findIndex((p)=>p.id === id);
        if (postIndex === -1) return null;
        this.blogPosts[postIndex] = {
            ...this.blogPosts[postIndex],
            ...postData,
            updatedAt: new Date().toISOString()
        };
        this.saveToLocalStorage(this.blogPostsKey, this.blogPosts);
        return {
            ...this.blogPosts[postIndex]
        };
    }
    deleteBlogPost(id) {
        if ("TURBOPACK compile-time truthy", 1) {
            if (!this.db_conn) return false;
            try {
                const stmt = this.db_conn.prepare('DELETE FROM blog_posts WHERE id = ?');
                const result = stmt.run(id);
                return result.changes > 0;
            } catch (error) {
                console.error('[DB_SERVER] Error in deleteBlogPost:', error);
                return false;
            }
        }
        // Client-side mock
        const initialLength = this.blogPosts.length;
        this.blogPosts = this.blogPosts.filter((p)=>p.id !== id);
        if (this.blogPosts.length < initialLength) {
            this.saveToLocalStorage(this.blogPostsKey, this.blogPosts);
            return true;
        }
        return false;
    }
    // Example for reset (useful for testing or initial setup)
    async resetLocalUsers() {
        if ("TURBOPACK compile-time truthy", 1) {
            console.warn("[DB_SERVER] resetLocalUsers called on server. This will clear the users table.");
            if (!this.db_conn) return;
            try {
                this.db_conn.exec('DELETE FROM users');
                console.log("[DB_SERVER] All users deleted from database.");
            } catch (error) {
                console.error("[DB_SERVER] Error deleting users from database:", error);
            }
            return;
        }
        "TURBOPACK unreachable";
    }
}
const sqlite = new MockSQLiteDatabase(db);
}}),
"[project]/src/lib/auth/nextauth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * NextAuth.js Configuration
 * Provides enterprise-grade authentication with backward compatibility
 */ __turbopack_context__.s({
    "authOptions": (()=>authOptions),
    "createUserWithAuth": (()=>createUserWithAuth),
    "verifyCredentials": (()=>verifyCredentials)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$sqlite$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/sqlite.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
;
;
;
const authOptions = {
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            id: 'credentials',
            name: 'credentials',
            credentials: {
                email: {
                    label: 'Email',
                    type: 'email'
                },
                password: {
                    label: 'Password',
                    type: 'password'
                }
            },
            async authorize (credentials) {
                if (!credentials?.email || !credentials?.password) {
                    throw new Error('Email and password are required');
                }
                try {
                    // Get user from database
                    const users = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$sqlite$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sqlite"].getUsers();
                    const user = users.find((u)=>u.email.toLowerCase() === credentials.email.toLowerCase());
                    if (!user) {
                        throw new Error('Invalid email or password');
                    }
                    // For backward compatibility, check if user has password_hash
                    // If not, this might be a legacy user - handle accordingly
                    let isValidPassword = false;
                    if (user.password_hash) {
                        isValidPassword = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(credentials.password, user.password_hash);
                    } else {
                        // Legacy user without hashed password - this shouldn't happen in production
                        // but we handle it for development/migration scenarios
                        console.warn('User found without password hash - this should not happen in production');
                        return null;
                    }
                    if (!isValidPassword) {
                        throw new Error('Invalid email or password');
                    }
                    // Return user object that will be stored in JWT
                    return {
                        id: user.id,
                        email: user.email,
                        name: `${user.firstName || ''} ${user.lastName || ''}`.trim(),
                        firstName: user.firstName,
                        lastName: user.lastName,
                        role: user.role,
                        avatar: user.avatar,
                        phone: user.phone,
                        address: user.address,
                        preferences: user.preferences,
                        company: user.company
                    };
                } catch (error) {
                    console.error('Authentication error:', error);
                    throw new Error('Authentication failed');
                }
            }
        })
    ],
    session: {
        strategy: 'jwt',
        maxAge: 30 * 24 * 60 * 60
    },
    jwt: {
        maxAge: 30 * 24 * 60 * 60
    },
    callbacks: {
        async jwt ({ token, user, account }) {
            // Initial sign in
            if (account && user) {
                return {
                    ...token,
                    id: user.id,
                    role: user.role,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    avatar: user.avatar,
                    phone: user.phone,
                    address: user.address,
                    preferences: user.preferences,
                    company: user.company
                };
            }
            // Return previous token if the access token has not expired yet
            return token;
        },
        async session ({ session, token }) {
            // Send properties to the client
            if (token) {
                session.user = {
                    ...session.user,
                    id: token.id,
                    role: token.role,
                    firstName: token.firstName,
                    lastName: token.lastName,
                    avatar: token.avatar,
                    phone: token.phone,
                    address: token.address,
                    preferences: token.preferences,
                    company: token.company
                };
            }
            return session;
        },
        async redirect ({ url, baseUrl }) {
            // Allows relative callback URLs
            if (url.startsWith('/')) return `${baseUrl}${url}`;
            else if (new URL(url).origin === baseUrl) return url;
            return baseUrl;
        }
    },
    pages: {
        signIn: '/auth/signin',
        signUp: '/auth/signup',
        error: '/auth/error'
    },
    events: {
        async signIn ({ user, account, profile, isNewUser }) {
            console.log('User signed in:', user.email);
            // Update last login time
            try {
                const users = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$sqlite$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sqlite"].getUsers();
                const existingUser = users.find((u)=>u.id === user.id);
                if (existingUser) {
                    // Note: For now we'll skip updating last login in SQLite
                    // This would require implementing an updateUser method
                    console.log('User signed in:', user.email);
                }
            } catch (error) {
                console.error('Error updating last login:', error);
            }
        },
        async signOut ({ token }) {
            console.log('User signed out:', token?.email);
        }
    },
    debug: ("TURBOPACK compile-time value", "development") === 'development',
    secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-change-in-production'
};
async function createUserWithAuth(userData) {
    try {
        // Hash password
        const saltRounds = 12;
        const password_hash = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(userData.password, saltRounds);
        // Create user object
        const newUser = {
            id: `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            email: userData.email.toLowerCase(),
            password_hash,
            firstName: userData.firstName || '',
            lastName: userData.lastName || '',
            role: userData.role || 'user',
            phone: userData.phone,
            company: userData.company,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            preferences: {
                language: 'ar',
                theme: 'light',
                notifications: true,
                newsletter: false
            }
        };
        // Save to database
        const createdUser = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$sqlite$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sqlite"].createUser({
            ...userData,
            password: userData.password
        });
        if (!createdUser) {
            throw new Error('Failed to create user');
        }
        return createdUser;
    } catch (error) {
        console.error('Error creating user:', error);
        return null;
    }
}
async function verifyCredentials(email, password) {
    try {
        const users = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$sqlite$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sqlite"].getUsers();
        const user = users.find((u)=>u.email.toLowerCase() === email.toLowerCase());
        if (!user || !user.password_hash) {
            return null;
        }
        const isValid = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(password, user.password_hash);
        return isValid ? user : null;
    } catch (error) {
        console.error('Error verifying credentials:', error);
        return null;
    }
}
}}),
"[project]/src/app/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>handler),
    "POST": (()=>handler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$nextauth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth/nextauth.ts [app-route] (ecmascript)");
;
;
const handler = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$nextauth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__edf7abe3._.js.map