(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/OHEVZFKG.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@tanstack_query-devtools_build_85e82e87._.js",
  "static/chunks/node_modules_@tanstack_query-devtools_build_DevtoolsComponent_OHEVZFKG_815fc7bf.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/OHEVZFKG.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/ZXCTK5ZC.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@tanstack_query-devtools_build_42b99208._.js",
  "static/chunks/dd92d_modules_@tanstack_query-devtools_build_DevtoolsPanelComponent_ZXCTK5ZC_815fc7bf.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/ZXCTK5ZC.js [app-client] (ecmascript)");
    });
});
}}),
}]);