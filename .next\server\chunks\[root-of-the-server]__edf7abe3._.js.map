{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/lib/sqlite.ts"], "sourcesContent": ["/**\n * مكتبة SQLite للتعامل مع قاعدة البيانات المحلية\n * تستبدل هذه المكتبة استخدام Supabase في المشروع\n */\n\nimport { v4 as uuidv4 } from 'uuid';\nimport type { Product, User, Review, ProductionLine, Service, BlogPost } from '../types/index';\nimport bcrypt from 'bcryptjs'; // Added for password hashing\n\nconst IS_SERVER = typeof window === 'undefined';\nconst SALT_ROUNDS = 10; // For bcrypt hashing\n\nlet db: any = null;\n\nif (IS_SERVER) {\n  try {\n    const BetterSqlite3 = require('better-sqlite3');\n    const path = require('path');\n    const fs = require('fs');\n\n    const dbDir = path.join(process.cwd(), 'server', 'db');\n    const dbPath = path.join(dbDir, 'ecommerce.sqlite');\n\n    if (!fs.existsSync(dbDir)) {\n      fs.mkdirSync(dbDir, { recursive: true });\n      console.log(`[DB_SERVER] Created database directory: ${dbDir}`);\n    }\n\n    console.log(`[DB_SERVER] Attempting to connect to SQLite database at: ${dbPath}`);\n    db = new BetterSqlite3(dbPath, { /* verbose: console.log */ }); // Verbose logging can be noisy\n    console.log('[DB_SERVER] Successfully connected to SQLite database.');\n\n    // Ensure tables are created (idempotent)\n    db.exec(`\n      CREATE TABLE IF NOT EXISTS users (\n        id TEXT PRIMARY KEY,\n        email TEXT UNIQUE NOT NULL,\n        password_hash TEXT NOT NULL,\n        first_name TEXT,\n        last_name TEXT,\n        role TEXT DEFAULT 'user' NOT NULL,\n        created_at TEXT NOT NULL,\n        updated_at TEXT,\n        avatar_url TEXT,\n        phone_number TEXT,\n        addresses TEXT, -- Store as JSON string\n        email_verified INTEGER DEFAULT 0, -- 0 for false, 1 for true\n        last_login TEXT\n      );\n    `);\n    console.log('[DB_SERVER] Users table checked/created.');\n\n    db.exec(`\n      CREATE TABLE IF NOT EXISTS products (\n        id TEXT PRIMARY KEY,\n        name TEXT NOT NULL,\n        slug TEXT UNIQUE NOT NULL,\n        description TEXT,\n        price REAL NOT NULL,\n        compare_at_price REAL,\n        images TEXT,\n        category TEXT,\n        tags TEXT,\n        stock INTEGER DEFAULT 0,\n        featured INTEGER DEFAULT 0,\n        specifications TEXT,\n        created_at TEXT NOT NULL,\n        updated_at TEXT,\n        rating REAL,\n        review_count INTEGER,\n        related_products TEXT\n      );\n    `);\n    console.log('[DB_SERVER] Products table checked/created.');\n\n    // Create blog_posts table\n    db.exec(`\n      CREATE TABLE IF NOT EXISTS blog_posts (\n        id TEXT PRIMARY KEY,\n        title TEXT NOT NULL,\n        title_ar TEXT,\n        slug TEXT UNIQUE NOT NULL,\n        excerpt TEXT NOT NULL,\n        excerpt_ar TEXT,\n        content TEXT NOT NULL,\n        content_ar TEXT,\n        author TEXT NOT NULL,\n        author_title TEXT,\n        author_image TEXT,\n        cover_image TEXT,\n        category TEXT NOT NULL,\n        tags TEXT,\n        keywords TEXT,\n        published_at TEXT NOT NULL,\n        read_time TEXT,\n        featured INTEGER DEFAULT 0,\n        status TEXT DEFAULT 'draft',\n        meta_title TEXT,\n        meta_description TEXT,\n        views INTEGER DEFAULT 0,\n        likes INTEGER DEFAULT 0,\n        created_at TEXT NOT NULL,\n        updated_at TEXT\n      );\n    `);\n    console.log('[DB_SERVER] Blog posts table checked/created.');\n\n    // Add other table creations here as needed (services, etc.)\n\n    console.log('[DB_SERVER] Database schema checked/initialized.');\n\n  } catch (error) {\n    console.error('[DB_SERVER] Critical error during database initialization:', error);\n    db = null;\n  }\n} else {\n  console.log('[MockDB_Client] Running in client mode, SQLite DB not initialized.');\n}\n\n// --- Server-side Helper Functions ---\nasync function _server_hashPassword(password: string): Promise<string> {\n  if (!IS_SERVER) throw new Error('_server_hashPassword can only be called on the server.');\n  return bcrypt.hash(password, SALT_ROUNDS);\n}\n\nasync function _server_comparePassword(password: string, hash: string): Promise<boolean> {\n  if (!IS_SERVER) throw new Error('_server_comparePassword can only be called on the server.');\n  return bcrypt.compare(password, hash);\n}\n\nfunction _server_getUserByEmailWithPasswordHash(email: string): User | null {\n  if (!IS_SERVER || !db) return null;\n  try {\n    const stmt = db.prepare('SELECT * FROM users WHERE email = ?');\n    const row = stmt.get(email.toLowerCase());\n    return row ? mapUserFromDbRow(row) : null;\n  } catch (error) {\n    console.error('[DB_SERVER] Error in _server_getUserByEmailWithPasswordHash:', error);\n    return null;\n  }\n}\n\nfunction _server_getUserById(id: string): User | null {\n  if (!IS_SERVER || !db) return null;\n  try {\n    const stmt = db.prepare('SELECT * FROM users WHERE id = ?');\n    const row = stmt.get(id);\n    return row ? mapUserFromDbRow(row) : null;\n  } catch (error) {\n    console.error('[DB_SERVER] Error in _server_getUserById:', error);\n    return null;\n  }\n}\n\nfunction _server_createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt' | 'password_hash'> & { password_hash: string }): User | null {\n  if (!IS_SERVER || !db) return null;\n  try {\n    const now = new Date().toISOString();\n\n    // Prepare addresses as JSON string if provided\n    const addressesJson = userData.addresses && userData.addresses.length > 0\n      ? JSON.stringify(userData.addresses)\n      : null;\n\n    // Generate a new ID if not provided\n    const userId = uuidv4();\n\n    const stmt = db.prepare(`\n      INSERT INTO users (\n        id, email, password_hash, first_name, last_name, role,\n        created_at, updated_at, avatar_url, phone_number,\n        addresses, email_verified, last_login\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    `);\n\n    stmt.run(\n      userId,\n      userData.email.toLowerCase(),\n      userData.password_hash,\n      userData.firstName || '',\n      userData.lastName || '',\n      userData.role || 'user',\n      now,\n      now,\n      userData.avatarUrl || null,\n      userData.phoneNumber || null,\n      addressesJson,\n      userData.emailVerified ? 1 : 0,\n      userData.lastLogin || null\n    );\n\n    return _server_getUserById(userId);\n  } catch (error) {\n    console.error('[DB_SERVER] Error in _server_createUser:', error);\n    return null;\n  }\n}\n\nfunction _server_getProducts(): Product[] {\n  if (!IS_SERVER || !db) return [];\n  try {\n    const stmt = db.prepare('SELECT * FROM products ORDER BY created_at DESC');\n    const rows = stmt.all();\n    return rows.map(mapProductFromDbRow);\n  } catch (error) {\n    console.error('[DB_SERVER] Error in _server_getProducts:', error);\n    return [];\n  }\n}\n\nfunction _server_createProduct(productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt' | 'reviews' | 'rating' | 'reviewCount'>): Product | null {\n  if (!IS_SERVER || !db) return null;\n  const newId = uuidv4();\n  const now = new Date().toISOString();\n  try {\n    const stmt = db.prepare(`\n      INSERT INTO products (id, name, slug, description, price, compare_at_price, images, category, tags, stock, featured, specifications, created_at)\n      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    `);\n    stmt.run(\n      newId,\n      productData.name,\n      productData.slug,\n      productData.description,\n      productData.price,\n      productData.compareAtPrice,\n      JSON.stringify(productData.images || []),\n      productData.category,\n      JSON.stringify(productData.tags || []),\n      productData.stock,\n      productData.featured ? 1 : 0,\n      JSON.stringify(productData.specifications || {}),\n      now\n    );\n    // To get the full product, we'd ideally query it back, but for now let's construct it\n    return {\n        ...productData,\n        id: newId,\n        createdAt: now,\n        reviews: [],\n        rating: 0,\n        reviewCount: 0\n    } as Product; // Cast as Product, assuming defaults for reviews/rating\n  } catch (error) {\n    console.error('[DB_SERVER] Error in _server_createProduct:', error);\n    return null;\n  }\n}\n\n// --- Helper function to safely parse JSON (used for localStorage) ---\nfunction safeJsonParse(jsonString: string | null | undefined, defaultValue: any): any {\n  if (jsonString == null) return defaultValue;\n  try {\n    return JSON.parse(jsonString);\n  } catch (e) {\n    // console.error('Failed to parse JSON string:', e, '\\nString was:', jsonString);\n    return defaultValue;\n  }\n}\n\n// --- localStorage keys ---\nconst USER_STORAGE_KEY = 'sqlite_users';\nconst PRODUCT_STORAGE_KEY = 'sqlite_products';\nconst BLOG_POSTS_STORAGE_KEY = 'sqlite_blog_posts';\n// ... other keys\n\n// --- Default data for client-side localStorage initialization ---\nconst DEFAULT_LOCAL_USERS: Array<Omit<User, 'id' | 'createdAt' | 'password_hash'>> = [\n  { email: '<EMAIL>', firstName: 'Admin', lastName: 'Local', role: 'admin' },\n  { email: '<EMAIL>', firstName: 'Test', lastName: 'Local', role: 'user' },\n];\n\n// ... DEFAULT_LOCAL_PRODUCTS (ensure it matches Product type, omitting server-generated fields)\nconst DEFAULT_LOCAL_PRODUCTS: Array<Omit<Product, 'id' | 'createdAt' | 'updatedAt' | 'reviews' | 'rating' | 'reviewCount' | 'relatedProducts'>> = [\n  {\n    name: 'Default Local Product 1',\n    slug: 'default-local-product-1',\n    description: 'This is a default product for localStorage.',\n    price: 19.99,\n    compareAtPrice: 29.99,\n    images: [],\n    category: 'Local Category',\n    tags: ['default', 'localstorage'],\n    stock: 100,\n    featured: true,\n    specifications: { material: 'local_plastic' },\n  },\n];\n\n// --- Helper function to map database row to User type (includes password_hash for internal use) ---\nfunction mapUserFromDbRow(row: any): User {\n  if (!row) return row;\n\n  // Parse addresses from JSON string if present\n  let addresses = [];\n  try {\n    if (row.addresses) {\n      addresses = JSON.parse(row.addresses);\n    }\n  } catch (e) {\n    console.error('[DB] Error parsing user addresses:', e);\n  }\n\n  // Map all fields from the database row to the User type\n  const user: User = {\n    id: row.id,\n    email: row.email,\n    firstName: row.first_name || '',\n    lastName: row.last_name || '',\n    role: row.role || 'user',\n    createdAt: row.created_at,\n    updatedAt: row.updated_at || null,\n    avatarUrl: row.avatar_url || null,\n    phoneNumber: row.phone_number || null,\n    addresses: addresses,\n    emailVerified: Boolean(row.email_verified),\n    lastLogin: row.last_login || null\n  } as User;\n\n  // Include password_hash for internal authentication\n  if (row.password_hash) {\n    (user as any).password_hash = row.password_hash;\n  }\n\n  return user;\n}\n\n// --- Helper function to map database row to Product type ---\nfunction mapProductFromDbRow(row: any): Product {\n  if (!row) return null as any;\n  return {\n    id: row.id,\n    name: row.name,\n    slug: row.slug,\n    description: row.description,\n    price: row.price ? parseFloat(row.price) : 0,\n    compareAtPrice: row.compare_at_price ? parseFloat(row.compare_at_price) : undefined,\n    images: row.images ? safeJsonParse(row.images, []) : [],\n    category: row.category,\n    tags: row.tags ? safeJsonParse(row.tags, []) : [],\n    stock: row.stock ? parseInt(row.stock, 10) : 0,\n    featured: Boolean(row.featured),\n    specifications: row.specifications ? safeJsonParse(row.specifications, {}) : {},\n    createdAt: row.created_at,\n    updatedAt: row.updated_at,\n    reviews: [], // Placeholder, to be implemented\n    rating: row.rating ? parseFloat(row.rating) : undefined,\n    reviewCount: row.review_count ? parseInt(row.review_count, 10) : undefined,\n    relatedProducts: row.related_products ? safeJsonParse(row.related_products, []) : [],\n  };\n}\n\n// --- Helper function to map database row to BlogPost type ---\nfunction mapBlogPostFromDbRow(row: any): BlogPost {\n  if (!row) return null as any;\n  return {\n    id: row.id,\n    title: row.title,\n    title_ar: row.title_ar,\n    slug: row.slug,\n    excerpt: row.excerpt,\n    excerpt_ar: row.excerpt_ar,\n    content: row.content,\n    content_ar: row.content_ar,\n    author: row.author,\n    authorTitle: row.author_title,\n    authorImage: row.author_image,\n    coverImage: row.cover_image,\n    category: row.category,\n    tags: row.tags ? safeJsonParse(row.tags, []) : [],\n    keywords: row.keywords ? safeJsonParse(row.keywords, []) : [],\n    publishedAt: row.published_at,\n    readTime: row.read_time,\n    featured: Boolean(row.featured),\n    status: row.status || 'draft',\n    metaTitle: row.meta_title,\n    metaDescription: row.meta_description,\n    views: row.views ? parseInt(row.views, 10) : 0,\n    likes: row.likes ? parseInt(row.likes, 10) : 0,\n    createdAt: row.created_at,\n    updatedAt: row.updated_at,\n  };\n}\n\nclass MockSQLiteDatabase {\n  private db_conn: any;\n  private users: User[] = [];\n  private products: Product[] = [];\n  private blogPosts: BlogPost[] = [];\n  // ... other local stores\n\n  private readonly userKey = USER_STORAGE_KEY;\n  private readonly productKey = PRODUCT_STORAGE_KEY;\n  private readonly blogPostsKey = BLOG_POSTS_STORAGE_KEY;\n  // ... other keys\n\n  constructor(server_db_connection: any) {\n    this.db_conn = server_db_connection; // This is the actual 'db' object from server scope\n    if (!IS_SERVER) {\n      this.users = this.loadFromLocalStorage(this.userKey, []);\n      this.products = this.loadFromLocalStorage(this.productKey, []);\n      this.blogPosts = this.loadFromLocalStorage(this.blogPostsKey, []);\n      // ... load other stores\n      this._initializeDefaultLocalData();\n    }\n  }\n\n  private loadFromLocalStorage<T>(key: string, defaultValue: T[]): T[] {\n    if (IS_SERVER) return defaultValue; // Should not happen with current constructor logic\n    try {\n      const data = localStorage.getItem(key);\n      const parsedData = data ? safeJsonParse(data, defaultValue) : defaultValue;\n      // تأكد من أن البيانات المحملة هي مصفوفة\n      if (!Array.isArray(parsedData)) {\n        console.warn(`[MockDB_Client] Data loaded from ${key} is not an array, using default value`);\n        return defaultValue;\n      }\n      return parsedData;\n    } catch (error) {\n      console.error(`[MockDB_Client] Error loading data from localStorage key ${key}:`, error);\n      return defaultValue;\n    }\n  }\n\n  private saveToLocalStorage<T>(key: string, data: T[]): void {\n    if (IS_SERVER) return;\n    localStorage.setItem(key, JSON.stringify(data));\n  }\n\n  private _initializeDefaultLocalData(): void {\n    if (IS_SERVER) return;\n    if (this.users.length === 0) {\n      this.users = DEFAULT_LOCAL_USERS.map(u => ({\n        ...u,\n        id: uuidv4(),\n        createdAt: new Date().toISOString(),\n        // No password_hash for local mock users used directly for display/testing\n      } as User));\n      this.saveToLocalStorage(this.userKey, this.users);\n    }\n    if (this.products.length === 0) {\n      this.products = DEFAULT_LOCAL_PRODUCTS.map(p => ({\n        ...p,\n        id: uuidv4(),\n        createdAt: new Date().toISOString(),\n        reviews: [],\n        rating: 0,\n        reviewCount: 0,\n        relatedProducts: [],\n      } as Product));\n      this.saveToLocalStorage(this.productKey, this.products);\n    }\n  }\n\n  // --- User Methods ---\n  async getUsers(): Promise<User[]> {\n    if (IS_SERVER) {\n      if (!this.db_conn) return [];\n      try {\n        const stmt = this.db_conn.prepare('SELECT * FROM users');\n        const rows = stmt.all();\n        // Exclude password_hash from results sent to client/general use\n        return rows.map(mapUserFromDbRow).map((userWithHash: User) => {\n          const { password_hash, ...userWithoutHash } = userWithHash;\n          return userWithoutHash as User; // Ensure the final object is also User\n        });\n      } catch (error) {\n        console.error('[DB_SERVER] Error in getUsers:', error);\n        return [];\n      }\n    }\n    // تأكد من أن this.users هو مصفوفة\n    if (!Array.isArray(this.users)) {\n      console.warn('[MockDB_Client] this.users is not an array, initializing empty array');\n      this.users = [];\n      this.saveToLocalStorage(this.userKey, this.users);\n    }\n    return [...this.users];\n  }\n\n  async getUserByEmail(email: string): Promise<User | null> {\n    if (IS_SERVER) {\n      const userWithHash = _server_getUserByEmailWithPasswordHash(email.toLowerCase());\n      if (userWithHash) {\n        const { password_hash, ...userWithoutHash } = userWithHash;\n        return userWithoutHash as User;\n      }\n      return null;\n    }\n    // تأكد من أن this.users هو مصفوفة\n    if (!Array.isArray(this.users)) {\n      console.warn('[MockDB_Client] this.users is not an array in getUserByEmail, initializing empty array');\n      this.users = [];\n      this.saveToLocalStorage(this.userKey, this.users);\n      return null;\n    }\n    const user = this.users.find(u => u.email.toLowerCase() === email.toLowerCase());\n    return user ? { ...user } : null; // Return a copy\n  }\n\n  async getUserById(id: string): Promise<User | null> {\n    if (IS_SERVER) {\n        const userWithHash = _server_getUserById(id);\n        if (userWithHash) {\n            const { password_hash, ...userWithoutHash } = userWithHash;\n            return userWithoutHash as User;\n        }\n        return null;\n    }\n    // تأكد من أن this.users هو مصفوفة\n    if (!Array.isArray(this.users)) {\n      console.warn('[MockDB_Client] this.users is not an array in getUserById, initializing empty array');\n      this.users = [];\n      this.saveToLocalStorage(this.userKey, this.users);\n      return null;\n    }\n    const user = this.users.find(u => u.id === id);\n    return user ? { ...user } : null;\n  }\n\n  async createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt' | 'password_hash'> & { password?: string }): Promise<User | null> {\n    if (IS_SERVER) {\n      if (!this.db_conn) {\n        console.error('[DB_SERVER] Database connection not available');\n        return null;\n      }\n\n      if (!userData.email || !userData.password) {\n        console.error('[DB_SERVER] Email and password required for user creation');\n        return null;\n      }\n\n      try {\n        // Check if user already exists\n        const existingUser = _server_getUserByEmailWithPasswordHash(userData.email.toLowerCase());\n        if (existingUser) {\n          console.error(`[DB_SERVER] User with email ${userData.email} already exists`);\n          return null;\n        }\n\n        // Create a new user with basic required fields\n        const now = new Date().toISOString();\n        const newUser = {\n          id: uuidv4(),\n          email: userData.email.toLowerCase(),\n          firstName: userData.firstName || '',\n          lastName: userData.lastName || '',\n          role: userData.role || 'user',\n          createdAt: now,\n          updatedAt: now,\n          avatarUrl: userData.avatarUrl || undefined,\n          phoneNumber: userData.phoneNumber || undefined,\n          addresses: userData.addresses || [],\n          emailVerified: userData.emailVerified || false,\n          lastLogin: undefined,\n        };\n\n        // Hash the password\n        const passwordHash = await _server_hashPassword(userData.password);\n\n        // Create user in database including password hash\n        // We need to remove fields that aren't in the expected type\n        const { id, createdAt, updatedAt, ...userDataForCreate } = newUser;\n        const user = _server_createUser({ ...userDataForCreate, password_hash: passwordHash });\n\n        if (!user) {\n          console.error('[DB_SERVER] Failed to create user in database');\n          return null;\n        }\n\n        // Don't return password hash to client\n        const { password_hash, ...userWithoutHash } = user;\n        return userWithoutHash as User;\n      } catch (error) {\n        console.error('[DB_SERVER] Error creating user:', error);\n        return null;\n      }\n    }\n\n    // Client-side mock\n    try {\n      // Check if email already exists in mock data\n      const existingUser = this.users.find(u => u.email.toLowerCase() === userData.email.toLowerCase());\n      if (existingUser) {\n        console.error(`[MockDB_Client] User with email ${userData.email} already exists`);\n        return null;\n      }\n\n      const now = new Date().toISOString();\n      const newUser: User = {\n        ...userData,\n        id: uuidv4(),\n        email: userData.email.toLowerCase(),\n        role: userData.role || 'user',\n        createdAt: now,\n        updatedAt: now,\n        avatarUrl: userData.avatarUrl || undefined,\n        phoneNumber: userData.phoneNumber || undefined,\n        addresses: userData.addresses || [],\n        emailVerified: userData.emailVerified || false,\n        lastLogin: undefined\n      };\n\n      this.users.push(newUser);\n      this.saveToLocalStorage(this.userKey, this.users);\n      return { ...newUser };\n    } catch (error) {\n      console.error('[MockDB_Client] Error creating user:', error);\n      return null;\n    }\n  }\n\n  async authenticateUser(email: string, password: string): Promise<User | null> {\n    if (IS_SERVER) {\n      if (!this.db_conn) {\n        console.error('[DB_SERVER] Database connection not available');\n        return null;\n      }\n\n      try {\n        // First retrieve the user with password hash by email\n        const user = _server_getUserByEmailWithPasswordHash(email.toLowerCase());\n\n        if (!user || !user.password_hash) {\n          console.log('[DB_SERVER] User not found or missing password hash:', email);\n          return null;\n        }\n\n        // Compare provided password with stored hash\n        const passwordMatch = await _server_comparePassword(password, user.password_hash);\n        if (!passwordMatch) {\n          console.log('[DB_SERVER] Password mismatch for user:', email);\n          return null;\n        }\n\n        // Update last login time\n        const now = new Date().toISOString();\n        const updateStmt = this.db_conn.prepare('UPDATE users SET last_login = ?, email_verified = 1 WHERE id = ?');\n        updateStmt.run(now, user.id);\n\n        // Fetch the updated user record\n        const updatedUser = _server_getUserById(user.id);\n        if (!updatedUser) {\n          console.error('[DB_SERVER] Failed to fetch updated user after login');\n          return null;\n        }\n\n        // Don't return password hash to the client\n        const { password_hash, ...userWithoutHash } = updatedUser;\n        return userWithoutHash as User;\n      } catch (error) {\n        console.error('[DB_SERVER] Error during authentication:', error);\n        return null;\n      }\n    }\n\n    // Client-side mock authentication\n    const user = this.users.find(u => u.email.toLowerCase() === email.toLowerCase());\n    if (!user) {\n      console.log('[MockDB_Client] User not found in mock database:', email);\n      return null;\n    }\n\n    // In client mode, we simulate a successful login by updating last login\n    const now = new Date().toISOString();\n    const updatedUser = { ...user, lastLogin: now, emailVerified: true };\n\n    // Update the user in local storage\n    const userIndex = this.users.findIndex(u => u.id === user.id);\n    if (userIndex !== -1) {\n      this.users[userIndex] = updatedUser;\n      this.saveToLocalStorage(this.userKey, this.users);\n    }\n\n    return updatedUser;\n  }\n\n  async updateUser(id: string, userData: Partial<Omit<User, 'id' | 'createdAt' | 'password_hash'>>): Promise<User | null> {\n    if (IS_SERVER) {\n      if (!this.db_conn) return null;\n      const existingUser = _server_getUserById(id);\n      if (!existingUser) return null;\n\n      const fieldsToUpdate = { ...userData, updated_at: new Date().toISOString() };\n      const setClauses = Object.keys(fieldsToUpdate)\n        .map(key => `${key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)} = ?`)\n        .join(', ');\n      const values = Object.values(fieldsToUpdate);\n\n      if (values.length === 0) return existingUser; // No fields to update\n\n      try {\n        const stmt = this.db_conn.prepare(`UPDATE users SET ${setClauses} WHERE id = ?`);\n        stmt.run(...values, id);\n        const updatedUser = _server_getUserById(id);\n        if (updatedUser) {\n            const { password_hash, ...userWithoutHash } = updatedUser;\n            return userWithoutHash as User;\n        }\n        return null;\n      } catch (error) {\n        console.error('[DB_SERVER] Error in updateUser:', error);\n        return null;\n      }\n    }\n    // Client-side mock\n    const userIndex = this.users.findIndex(u => u.id === id);\n    if (userIndex === -1) return null;\n    this.users[userIndex] = { ...this.users[userIndex], ...userData, updatedAt: new Date().toISOString() } as User;\n    this.saveToLocalStorage(this.userKey, this.users);\n    return { ...this.users[userIndex] };\n  }\n\n  // --- Product Methods ---\n  async getProducts(): Promise<Product[]> {\n    if (IS_SERVER) {\n      return _server_getProducts();\n    }\n    return [...this.products];\n  }\n\n  async createProduct(productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt' | 'reviews' | 'rating' | 'reviewCount'>): Promise<Product | null> {\n    if (IS_SERVER) {\n      return _server_createProduct(productData);\n    }\n    // Client-side mock\n    const newId = uuidv4();\n    const now = new Date().toISOString();\n    const newProduct: Product = {\n      ...productData,\n      id: newId,\n      createdAt: now,\n      reviews: [], rating: 0, reviewCount: 0, // Default values\n    } as Product;\n    this.products.push(newProduct);\n    this.saveToLocalStorage(this.productKey, this.products);\n    return { ...newProduct };\n  }\n\n  // ... (Other methods like getProductById, updateProduct, deleteProduct should follow similar pattern)\n  async getProductById(id: string): Promise<Product | null> {\n    if (IS_SERVER) {\n      if (!this.db_conn) return null;\n      try {\n        const stmt = this.db_conn.prepare('SELECT * FROM products WHERE id = ?');\n        const row = stmt.get(id);\n        return row ? mapProductFromDbRow(row) : null;\n      } catch (error) {\n        console.error('[DB_SERVER] Error in getProductById:', error);\n        return null;\n      }\n    }\n    const product = this.products.find(p => p.id === id);\n    return product ? { ...product } : null;\n  }\n\n  // --- Blog Post Methods ---\n  getBlogPosts(): BlogPost[] {\n    if (IS_SERVER) {\n      if (!this.db_conn) return [];\n      try {\n        const stmt = this.db_conn.prepare('SELECT * FROM blog_posts ORDER BY created_at DESC');\n        const rows = stmt.all();\n        return rows.map(mapBlogPostFromDbRow);\n      } catch (error) {\n        console.error('[DB_SERVER] Error in getBlogPosts:', error);\n        return [];\n      }\n    }\n    // تأكد من أن this.blogPosts هو مصفوفة\n    if (!Array.isArray(this.blogPosts)) {\n      console.warn('[MockDB_Client] this.blogPosts is not an array, initializing empty array');\n      this.blogPosts = [];\n      this.saveToLocalStorage(this.blogPostsKey, this.blogPosts);\n    }\n    return [...this.blogPosts];\n  }\n\n  saveBlogPosts(posts: BlogPost[]): void {\n    if (IS_SERVER) {\n      // For server-side, we don't use this method as we use individual CRUD operations\n      console.warn('[DB_SERVER] saveBlogPosts called on server - use individual CRUD operations instead');\n      return;\n    }\n    this.blogPosts = posts;\n    this.saveToLocalStorage(this.blogPostsKey, this.blogPosts);\n  }\n\n  getBlogPostById(id: string): BlogPost | null {\n    if (IS_SERVER) {\n      if (!this.db_conn) return null;\n      try {\n        const stmt = this.db_conn.prepare('SELECT * FROM blog_posts WHERE id = ?');\n        const row = stmt.get(id);\n        return row ? mapBlogPostFromDbRow(row) : null;\n      } catch (error) {\n        console.error('[DB_SERVER] Error in getBlogPostById:', error);\n        return null;\n      }\n    }\n    const post = this.blogPosts.find(p => p.id === id);\n    return post ? { ...post } : null;\n  }\n\n  getBlogPostBySlug(slug: string): BlogPost | null {\n    if (IS_SERVER) {\n      if (!this.db_conn) return null;\n      try {\n        const stmt = this.db_conn.prepare('SELECT * FROM blog_posts WHERE slug = ?');\n        const row = stmt.get(slug);\n        return row ? mapBlogPostFromDbRow(row) : null;\n      } catch (error) {\n        console.error('[DB_SERVER] Error in getBlogPostBySlug:', error);\n        return null;\n      }\n    }\n    const post = this.blogPosts.find(p => p.slug === slug);\n    return post ? { ...post } : null;\n  }\n\n  createBlogPost(postData: Omit<BlogPost, 'id' | 'createdAt' | 'updatedAt'>): BlogPost | null {\n    if (IS_SERVER) {\n      if (!this.db_conn) return null;\n      try {\n        const now = new Date().toISOString();\n        const newId = uuidv4();\n\n        const stmt = this.db_conn.prepare(`\n          INSERT INTO blog_posts (\n            id, title, title_ar, slug, excerpt, excerpt_ar, content, content_ar,\n            author, author_title, author_image, cover_image, category, tags, keywords,\n            published_at, read_time, featured, status, meta_title, meta_description,\n            views, likes, created_at, updated_at\n          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n        `);\n\n        stmt.run(\n          newId,\n          postData.title,\n          postData.title_ar || null,\n          postData.slug,\n          postData.excerpt,\n          postData.excerpt_ar || null,\n          postData.content,\n          postData.content_ar || null,\n          postData.author,\n          postData.authorTitle || null,\n          postData.authorImage || null,\n          postData.coverImage || null,\n          postData.category,\n          JSON.stringify(postData.tags || []),\n          JSON.stringify(postData.keywords || []),\n          postData.publishedAt,\n          postData.readTime || null,\n          postData.featured ? 1 : 0,\n          postData.status || 'draft',\n          postData.metaTitle || null,\n          postData.metaDescription || null,\n          postData.views || 0,\n          postData.likes || 0,\n          now,\n          now\n        );\n\n        return this.getBlogPostById(newId);\n      } catch (error) {\n        console.error('[DB_SERVER] Error in createBlogPost:', error);\n        return null;\n      }\n    }\n\n    // Client-side mock\n    const now = new Date().toISOString();\n    const newPost: BlogPost = {\n      ...postData,\n      id: uuidv4(),\n      createdAt: now,\n      updatedAt: now,\n    };\n\n    this.blogPosts.push(newPost);\n    this.saveToLocalStorage(this.blogPostsKey, this.blogPosts);\n    return { ...newPost };\n  }\n\n  updateBlogPost(id: string, postData: Partial<BlogPost>): BlogPost | null {\n    if (IS_SERVER) {\n      if (!this.db_conn) return null;\n      try {\n        const now = new Date().toISOString();\n        const updateData = { ...postData, updated_at: now };\n\n        // Build dynamic update query\n        const fields = Object.keys(updateData).filter(key => key !== 'id');\n        const setClauses = fields.map(key => {\n          // Convert camelCase to snake_case for database columns\n          const dbKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);\n          return `${dbKey} = ?`;\n        }).join(', ');\n\n        const values = fields.map(key => {\n          const value = updateData[key as keyof BlogPost];\n          if (key === 'tags' || key === 'keywords') {\n            return JSON.stringify(value || []);\n          }\n          if (key === 'featured') {\n            return value ? 1 : 0;\n          }\n          return value;\n        });\n\n        const stmt = this.db_conn.prepare(`UPDATE blog_posts SET ${setClauses} WHERE id = ?`);\n        stmt.run(...values, id);\n\n        return this.getBlogPostById(id);\n      } catch (error) {\n        console.error('[DB_SERVER] Error in updateBlogPost:', error);\n        return null;\n      }\n    }\n\n    // Client-side mock\n    const postIndex = this.blogPosts.findIndex(p => p.id === id);\n    if (postIndex === -1) return null;\n\n    this.blogPosts[postIndex] = {\n      ...this.blogPosts[postIndex],\n      ...postData,\n      updatedAt: new Date().toISOString()\n    };\n\n    this.saveToLocalStorage(this.blogPostsKey, this.blogPosts);\n    return { ...this.blogPosts[postIndex] };\n  }\n\n  deleteBlogPost(id: string): boolean {\n    if (IS_SERVER) {\n      if (!this.db_conn) return false;\n      try {\n        const stmt = this.db_conn.prepare('DELETE FROM blog_posts WHERE id = ?');\n        const result = stmt.run(id);\n        return result.changes > 0;\n      } catch (error) {\n        console.error('[DB_SERVER] Error in deleteBlogPost:', error);\n        return false;\n      }\n    }\n\n    // Client-side mock\n    const initialLength = this.blogPosts.length;\n    this.blogPosts = this.blogPosts.filter(p => p.id !== id);\n\n    if (this.blogPosts.length < initialLength) {\n      this.saveToLocalStorage(this.blogPostsKey, this.blogPosts);\n      return true;\n    }\n\n    return false;\n  }\n\n  // Example for reset (useful for testing or initial setup)\n  async resetLocalUsers(): Promise<void> {\n    if (IS_SERVER) {\n        console.warn(\"[DB_SERVER] resetLocalUsers called on server. This will clear the users table.\");\n        if (!this.db_conn) return;\n        try {\n            this.db_conn.exec('DELETE FROM users');\n            console.log(\"[DB_SERVER] All users deleted from database.\");\n        } catch (error) {\n            console.error(\"[DB_SERVER] Error deleting users from database:\", error);\n        }\n        return;\n    }\n    this.users = [];\n    this.saveToLocalStorage(this.userKey, this.users);\n    this._initializeDefaultLocalData(); // Re-initialize with defaults if needed\n    console.log('[MockDB_Client] Local users reset and defaults re-initialized.');\n  }\n\n  // Add similar methods for services, productionLines, blogPosts as needed\n}\n\n// Export a single instance of the database client\n// The 'db' variable from the server scope is passed here.\n// On the client, 'db' will be null, and MockSQLiteDatabase will use localStorage.\nexport const sqlite = new MockSQLiteDatabase(db);\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AAEA,wOAA+B,6BAA6B;;;AAE5D,MAAM,YAAY,gBAAkB;AACpC,MAAM,cAAc,IAAI,qBAAqB;AAE7C,IAAI,KAAU;AAEd,wCAAe;IACb,IAAI;QACF,MAAM;QACN,MAAM;QACN,MAAM;QAEN,MAAM,QAAQ,KAAK,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU;QACjD,MAAM,SAAS,KAAK,IAAI,CAAC,OAAO;QAEhC,IAAI,CAAC,GAAG,UAAU,CAAC,QAAQ;YACzB,GAAG,SAAS,CAAC,OAAO;gBAAE,WAAW;YAAK;YACtC,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,OAAO;QAChE;QAEA,QAAQ,GAAG,CAAC,CAAC,yDAAyD,EAAE,QAAQ;QAChF,KAAK,IAAI,cAAc,QAAQ,CAA6B,IAAI,+BAA+B;QAC/F,QAAQ,GAAG,CAAC;QAEZ,yCAAyC;QACzC,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;IAgBT,CAAC;QACD,QAAQ,GAAG,CAAC;QAEZ,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;IAoBT,CAAC;QACD,QAAQ,GAAG,CAAC;QAEZ,0BAA0B;QAC1B,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA4BT,CAAC;QACD,QAAQ,GAAG,CAAC;QAEZ,4DAA4D;QAE5D,QAAQ,GAAG,CAAC;IAEd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8DAA8D;QAC5E,KAAK;IACP;AACF,OAAO;;AAEP;AAEA,uCAAuC;AACvC,eAAe,qBAAqB,QAAgB;IAClD,uCAAgB;;IAAyE;IACzF,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEA,eAAe,wBAAwB,QAAgB,EAAE,IAAY;IACnE,uCAAgB;;IAA4E;IAC5F,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAEA,SAAS,uCAAuC,KAAa;IAC3D,IAAI,CAAC,aAAa,CAAC,IAAI,OAAO;IAC9B,IAAI;QACF,MAAM,OAAO,GAAG,OAAO,CAAC;QACxB,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM,WAAW;QACtC,OAAO,MAAM,iBAAiB,OAAO;IACvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gEAAgE;QAC9E,OAAO;IACT;AACF;AAEA,SAAS,oBAAoB,EAAU;IACrC,IAAI,CAAC,aAAa,CAAC,IAAI,OAAO;IAC9B,IAAI;QACF,MAAM,OAAO,GAAG,OAAO,CAAC;QACxB,MAAM,MAAM,KAAK,GAAG,CAAC;QACrB,OAAO,MAAM,iBAAiB,OAAO;IACvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,OAAO;IACT;AACF;AAEA,SAAS,mBAAmB,QAAoG;IAC9H,IAAI,CAAC,aAAa,CAAC,IAAI,OAAO;IAC9B,IAAI;QACF,MAAM,MAAM,IAAI,OAAO,WAAW;QAElC,+CAA+C;QAC/C,MAAM,gBAAgB,SAAS,SAAS,IAAI,SAAS,SAAS,CAAC,MAAM,GAAG,IACpE,KAAK,SAAS,CAAC,SAAS,SAAS,IACjC;QAEJ,oCAAoC;QACpC,MAAM,SAAS,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD;QAEpB,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC;;;;;;IAMzB,CAAC;QAED,KAAK,GAAG,CACN,QACA,SAAS,KAAK,CAAC,WAAW,IAC1B,SAAS,aAAa,EACtB,SAAS,SAAS,IAAI,IACtB,SAAS,QAAQ,IAAI,IACrB,SAAS,IAAI,IAAI,QACjB,KACA,KACA,SAAS,SAAS,IAAI,MACtB,SAAS,WAAW,IAAI,MACxB,eACA,SAAS,aAAa,GAAG,IAAI,GAC7B,SAAS,SAAS,IAAI;QAGxB,OAAO,oBAAoB;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;IACT;AACF;AAEA,SAAS;IACP,IAAI,CAAC,aAAa,CAAC,IAAI,OAAO,EAAE;IAChC,IAAI;QACF,MAAM,OAAO,GAAG,OAAO,CAAC;QACxB,MAAM,OAAO,KAAK,GAAG;QACrB,OAAO,KAAK,GAAG,CAAC;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,OAAO,EAAE;IACX;AACF;AAEA,SAAS,sBAAsB,WAAmG;IAChI,IAAI,CAAC,aAAa,CAAC,IAAI,OAAO;IAC9B,MAAM,QAAQ,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD;IACnB,MAAM,MAAM,IAAI,OAAO,WAAW;IAClC,IAAI;QACF,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC;;;IAGzB,CAAC;QACD,KAAK,GAAG,CACN,OACA,YAAY,IAAI,EAChB,YAAY,IAAI,EAChB,YAAY,WAAW,EACvB,YAAY,KAAK,EACjB,YAAY,cAAc,EAC1B,KAAK,SAAS,CAAC,YAAY,MAAM,IAAI,EAAE,GACvC,YAAY,QAAQ,EACpB,KAAK,SAAS,CAAC,YAAY,IAAI,IAAI,EAAE,GACrC,YAAY,KAAK,EACjB,YAAY,QAAQ,GAAG,IAAI,GAC3B,KAAK,SAAS,CAAC,YAAY,cAAc,IAAI,CAAC,IAC9C;QAEF,sFAAsF;QACtF,OAAO;YACH,GAAG,WAAW;YACd,IAAI;YACJ,WAAW;YACX,SAAS,EAAE;YACX,QAAQ;YACR,aAAa;QACjB,GAAc,wDAAwD;IACxE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+CAA+C;QAC7D,OAAO;IACT;AACF;AAEA,uEAAuE;AACvE,SAAS,cAAc,UAAqC,EAAE,YAAiB;IAC7E,IAAI,cAAc,MAAM,OAAO;IAC/B,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,GAAG;QACV,iFAAiF;QACjF,OAAO;IACT;AACF;AAEA,4BAA4B;AAC5B,MAAM,mBAAmB;AACzB,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB;AAC/B,iBAAiB;AAEjB,mEAAmE;AACnE,MAAM,sBAA+E;IACnF;QAAE,OAAO;QAA2B,WAAW;QAAS,UAAU;QAAS,MAAM;IAAQ;IACzF;QAAE,OAAO;QAA0B,WAAW;QAAQ,UAAU;QAAS,MAAM;IAAO;CACvF;AAED,gGAAgG;AAChG,MAAM,yBAA4I;IAChJ;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,gBAAgB;QAChB,QAAQ,EAAE;QACV,UAAU;QACV,MAAM;YAAC;YAAW;SAAe;QACjC,OAAO;QACP,UAAU;QACV,gBAAgB;YAAE,UAAU;QAAgB;IAC9C;CACD;AAED,qGAAqG;AACrG,SAAS,iBAAiB,GAAQ;IAChC,IAAI,CAAC,KAAK,OAAO;IAEjB,8CAA8C;IAC9C,IAAI,YAAY,EAAE;IAClB,IAAI;QACF,IAAI,IAAI,SAAS,EAAE;YACjB,YAAY,KAAK,KAAK,CAAC,IAAI,SAAS;QACtC;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,sCAAsC;IACtD;IAEA,wDAAwD;IACxD,MAAM,OAAa;QACjB,IAAI,IAAI,EAAE;QACV,OAAO,IAAI,KAAK;QAChB,WAAW,IAAI,UAAU,IAAI;QAC7B,UAAU,IAAI,SAAS,IAAI;QAC3B,MAAM,IAAI,IAAI,IAAI;QAClB,WAAW,IAAI,UAAU;QACzB,WAAW,IAAI,UAAU,IAAI;QAC7B,WAAW,IAAI,UAAU,IAAI;QAC7B,aAAa,IAAI,YAAY,IAAI;QACjC,WAAW;QACX,eAAe,QAAQ,IAAI,cAAc;QACzC,WAAW,IAAI,UAAU,IAAI;IAC/B;IAEA,oDAAoD;IACpD,IAAI,IAAI,aAAa,EAAE;QACpB,KAAa,aAAa,GAAG,IAAI,aAAa;IACjD;IAEA,OAAO;AACT;AAEA,8DAA8D;AAC9D,SAAS,oBAAoB,GAAQ;IACnC,IAAI,CAAC,KAAK,OAAO;IACjB,OAAO;QACL,IAAI,IAAI,EAAE;QACV,MAAM,IAAI,IAAI;QACd,MAAM,IAAI,IAAI;QACd,aAAa,IAAI,WAAW;QAC5B,OAAO,IAAI,KAAK,GAAG,WAAW,IAAI,KAAK,IAAI;QAC3C,gBAAgB,IAAI,gBAAgB,GAAG,WAAW,IAAI,gBAAgB,IAAI;QAC1E,QAAQ,IAAI,MAAM,GAAG,cAAc,IAAI,MAAM,EAAE,EAAE,IAAI,EAAE;QACvD,UAAU,IAAI,QAAQ;QACtB,MAAM,IAAI,IAAI,GAAG,cAAc,IAAI,IAAI,EAAE,EAAE,IAAI,EAAE;QACjD,OAAO,IAAI,KAAK,GAAG,SAAS,IAAI,KAAK,EAAE,MAAM;QAC7C,UAAU,QAAQ,IAAI,QAAQ;QAC9B,gBAAgB,IAAI,cAAc,GAAG,cAAc,IAAI,cAAc,EAAE,CAAC,KAAK,CAAC;QAC9E,WAAW,IAAI,UAAU;QACzB,WAAW,IAAI,UAAU;QACzB,SAAS,EAAE;QACX,QAAQ,IAAI,MAAM,GAAG,WAAW,IAAI,MAAM,IAAI;QAC9C,aAAa,IAAI,YAAY,GAAG,SAAS,IAAI,YAAY,EAAE,MAAM;QACjE,iBAAiB,IAAI,gBAAgB,GAAG,cAAc,IAAI,gBAAgB,EAAE,EAAE,IAAI,EAAE;IACtF;AACF;AAEA,+DAA+D;AAC/D,SAAS,qBAAqB,GAAQ;IACpC,IAAI,CAAC,KAAK,OAAO;IACjB,OAAO;QACL,IAAI,IAAI,EAAE;QACV,OAAO,IAAI,KAAK;QAChB,UAAU,IAAI,QAAQ;QACtB,MAAM,IAAI,IAAI;QACd,SAAS,IAAI,OAAO;QACpB,YAAY,IAAI,UAAU;QAC1B,SAAS,IAAI,OAAO;QACpB,YAAY,IAAI,UAAU;QAC1B,QAAQ,IAAI,MAAM;QAClB,aAAa,IAAI,YAAY;QAC7B,aAAa,IAAI,YAAY;QAC7B,YAAY,IAAI,WAAW;QAC3B,UAAU,IAAI,QAAQ;QACtB,MAAM,IAAI,IAAI,GAAG,cAAc,IAAI,IAAI,EAAE,EAAE,IAAI,EAAE;QACjD,UAAU,IAAI,QAAQ,GAAG,cAAc,IAAI,QAAQ,EAAE,EAAE,IAAI,EAAE;QAC7D,aAAa,IAAI,YAAY;QAC7B,UAAU,IAAI,SAAS;QACvB,UAAU,QAAQ,IAAI,QAAQ;QAC9B,QAAQ,IAAI,MAAM,IAAI;QACtB,WAAW,IAAI,UAAU;QACzB,iBAAiB,IAAI,gBAAgB;QACrC,OAAO,IAAI,KAAK,GAAG,SAAS,IAAI,KAAK,EAAE,MAAM;QAC7C,OAAO,IAAI,KAAK,GAAG,SAAS,IAAI,KAAK,EAAE,MAAM;QAC7C,WAAW,IAAI,UAAU;QACzB,WAAW,IAAI,UAAU;IAC3B;AACF;AAEA,MAAM;IACI,QAAa;IACb,QAAgB,EAAE,CAAC;IACnB,WAAsB,EAAE,CAAC;IACzB,YAAwB,EAAE,CAAC;IACnC,yBAAyB;IAER,UAAU,iBAAiB;IAC3B,aAAa,oBAAoB;IACjC,eAAe,uBAAuB;IACvD,iBAAiB;IAEjB,YAAY,oBAAyB,CAAE;QACrC,IAAI,CAAC,OAAO,GAAG,sBAAsB,mDAAmD;QACxF,uCAAgB;;QAMhB;IACF;IAEQ,qBAAwB,GAAW,EAAE,YAAiB,EAAO;QACnE,wCAAe,OAAO,cAAc,mDAAmD;;IAczF;IAEQ,mBAAsB,GAAW,EAAE,IAAS,EAAQ;QAC1D,wCAAe;;IAEjB;IAEQ,8BAAoC;QAC1C,wCAAe;;IAsBjB;IAEA,uBAAuB;IACvB,MAAM,WAA4B;QAChC,wCAAe;YACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE;YAC5B,IAAI;gBACF,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;gBAClC,MAAM,OAAO,KAAK,GAAG;gBACrB,gEAAgE;gBAChE,OAAO,KAAK,GAAG,CAAC,kBAAkB,GAAG,CAAC,CAAC;oBACrC,MAAM,EAAE,aAAa,EAAE,GAAG,iBAAiB,GAAG;oBAC9C,OAAO,iBAAyB,uCAAuC;gBACzE;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,OAAO,EAAE;YACX;QACF;QACA,kCAAkC;QAClC,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG;YAC9B,QAAQ,IAAI,CAAC;YACb,IAAI,CAAC,KAAK,GAAG,EAAE;YACf,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK;QAClD;QACA,OAAO;eAAI,IAAI,CAAC,KAAK;SAAC;IACxB;IAEA,MAAM,eAAe,KAAa,EAAwB;QACxD,wCAAe;YACb,MAAM,eAAe,uCAAuC,MAAM,WAAW;YAC7E,IAAI,cAAc;gBAChB,MAAM,EAAE,aAAa,EAAE,GAAG,iBAAiB,GAAG;gBAC9C,OAAO;YACT;YACA,OAAO;QACT;;QAQA,MAAM;IAER;IAEA,MAAM,YAAY,EAAU,EAAwB;QAClD,wCAAe;YACX,MAAM,eAAe,oBAAoB;YACzC,IAAI,cAAc;gBACd,MAAM,EAAE,aAAa,EAAE,GAAG,iBAAiB,GAAG;gBAC9C,OAAO;YACX;YACA,OAAO;QACX;;QAQA,MAAM;IAER;IAEA,MAAM,WAAW,QAAgG,EAAwB;QACvI,wCAAe;YACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACjB,QAAQ,KAAK,CAAC;gBACd,OAAO;YACT;YAEA,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,QAAQ,EAAE;gBACzC,QAAQ,KAAK,CAAC;gBACd,OAAO;YACT;YAEA,IAAI;gBACF,+BAA+B;gBAC/B,MAAM,eAAe,uCAAuC,SAAS,KAAK,CAAC,WAAW;gBACtF,IAAI,cAAc;oBAChB,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,SAAS,KAAK,CAAC,eAAe,CAAC;oBAC5E,OAAO;gBACT;gBAEA,+CAA+C;gBAC/C,MAAM,MAAM,IAAI,OAAO,WAAW;gBAClC,MAAM,UAAU;oBACd,IAAI,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD;oBACT,OAAO,SAAS,KAAK,CAAC,WAAW;oBACjC,WAAW,SAAS,SAAS,IAAI;oBACjC,UAAU,SAAS,QAAQ,IAAI;oBAC/B,MAAM,SAAS,IAAI,IAAI;oBACvB,WAAW;oBACX,WAAW;oBACX,WAAW,SAAS,SAAS,IAAI;oBACjC,aAAa,SAAS,WAAW,IAAI;oBACrC,WAAW,SAAS,SAAS,IAAI,EAAE;oBACnC,eAAe,SAAS,aAAa,IAAI;oBACzC,WAAW;gBACb;gBAEA,oBAAoB;gBACpB,MAAM,eAAe,MAAM,qBAAqB,SAAS,QAAQ;gBAEjE,kDAAkD;gBAClD,4DAA4D;gBAC5D,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,mBAAmB,GAAG;gBAC3D,MAAM,OAAO,mBAAmB;oBAAE,GAAG,iBAAiB;oBAAE,eAAe;gBAAa;gBAEpF,IAAI,CAAC,MAAM;oBACT,QAAQ,KAAK,CAAC;oBACd,OAAO;gBACT;gBAEA,uCAAuC;gBACvC,MAAM,EAAE,aAAa,EAAE,GAAG,iBAAiB,GAAG;gBAC9C,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,OAAO;YACT;QACF;QAEA,mBAAmB;QACnB,IAAI;YACF,6CAA6C;YAC7C,MAAM,eAAe,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,WAAW,OAAO,SAAS,KAAK,CAAC,WAAW;YAC9F,IAAI,cAAc;gBAChB,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,SAAS,KAAK,CAAC,eAAe,CAAC;gBAChF,OAAO;YACT;YAEA,MAAM,MAAM,IAAI,OAAO,WAAW;YAClC,MAAM,UAAgB;gBACpB,GAAG,QAAQ;gBACX,IAAI,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD;gBACT,OAAO,SAAS,KAAK,CAAC,WAAW;gBACjC,MAAM,SAAS,IAAI,IAAI;gBACvB,WAAW;gBACX,WAAW;gBACX,WAAW,SAAS,SAAS,IAAI;gBACjC,aAAa,SAAS,WAAW,IAAI;gBACrC,WAAW,SAAS,SAAS,IAAI,EAAE;gBACnC,eAAe,SAAS,aAAa,IAAI;gBACzC,WAAW;YACb;YAEA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YAChB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK;YAChD,OAAO;gBAAE,GAAG,OAAO;YAAC;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO;QACT;IACF;IAEA,MAAM,iBAAiB,KAAa,EAAE,QAAgB,EAAwB;QAC5E,wCAAe;YACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACjB,QAAQ,KAAK,CAAC;gBACd,OAAO;YACT;YAEA,IAAI;gBACF,sDAAsD;gBACtD,MAAM,OAAO,uCAAuC,MAAM,WAAW;gBAErE,IAAI,CAAC,QAAQ,CAAC,KAAK,aAAa,EAAE;oBAChC,QAAQ,GAAG,CAAC,wDAAwD;oBACpE,OAAO;gBACT;gBAEA,6CAA6C;gBAC7C,MAAM,gBAAgB,MAAM,wBAAwB,UAAU,KAAK,aAAa;gBAChF,IAAI,CAAC,eAAe;oBAClB,QAAQ,GAAG,CAAC,2CAA2C;oBACvD,OAAO;gBACT;gBAEA,yBAAyB;gBACzB,MAAM,MAAM,IAAI,OAAO,WAAW;gBAClC,MAAM,aAAa,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;gBACxC,WAAW,GAAG,CAAC,KAAK,KAAK,EAAE;gBAE3B,gCAAgC;gBAChC,MAAM,cAAc,oBAAoB,KAAK,EAAE;gBAC/C,IAAI,CAAC,aAAa;oBAChB,QAAQ,KAAK,CAAC;oBACd,OAAO;gBACT;gBAEA,2CAA2C;gBAC3C,MAAM,EAAE,aAAa,EAAE,GAAG,iBAAiB,GAAG;gBAC9C,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4CAA4C;gBAC1D,OAAO;YACT;QACF;QAEA,kCAAkC;QAClC,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,WAAW,OAAO,MAAM,WAAW;QAC7E,IAAI,CAAC,MAAM;YACT,QAAQ,GAAG,CAAC,oDAAoD;YAChE,OAAO;QACT;QAEA,wEAAwE;QACxE,MAAM,MAAM,IAAI,OAAO,WAAW;QAClC,MAAM,cAAc;YAAE,GAAG,IAAI;YAAE,WAAW;YAAK,eAAe;QAAK;QAEnE,mCAAmC;QACnC,MAAM,YAAY,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;QAC5D,IAAI,cAAc,CAAC,GAAG;YACpB,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG;YACxB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK;QAClD;QAEA,OAAO;IACT;IAEA,MAAM,WAAW,EAAU,EAAE,QAAmE,EAAwB;QACtH,wCAAe;YACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;YAC1B,MAAM,eAAe,oBAAoB;YACzC,IAAI,CAAC,cAAc,OAAO;YAE1B,MAAM,iBAAiB;gBAAE,GAAG,QAAQ;gBAAE,YAAY,IAAI,OAAO,WAAW;YAAG;YAC3E,MAAM,aAAa,OAAO,IAAI,CAAC,gBAC5B,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,OAAO,CAAC,UAAU,CAAA,SAAU,CAAC,CAAC,EAAE,OAAO,WAAW,IAAI,EAAE,IAAI,CAAC,EAC/E,IAAI,CAAC;YACR,MAAM,SAAS,OAAO,MAAM,CAAC;YAE7B,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO,cAAc,sBAAsB;YAEpE,IAAI;gBACF,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,WAAW,aAAa,CAAC;gBAC/E,KAAK,GAAG,IAAI,QAAQ;gBACpB,MAAM,cAAc,oBAAoB;gBACxC,IAAI,aAAa;oBACb,MAAM,EAAE,aAAa,EAAE,GAAG,iBAAiB,GAAG;oBAC9C,OAAO;gBACX;gBACA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,OAAO;YACT;QACF;QACA,mBAAmB;QACnB,MAAM,YAAY,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACrD,IAAI,cAAc,CAAC,GAAG,OAAO;QAC7B,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG;YAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU;YAAE,GAAG,QAAQ;YAAE,WAAW,IAAI,OAAO,WAAW;QAAG;QACrG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK;QAChD,OAAO;YAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU;QAAC;IACpC;IAEA,0BAA0B;IAC1B,MAAM,cAAkC;QACtC,wCAAe;YACb,OAAO;QACT;;IAEF;IAEA,MAAM,cAAc,WAAmG,EAA2B;QAChJ,wCAAe;YACb,OAAO,sBAAsB;QAC/B;;QACA,mBAAmB;QACnB,MAAM;QACN,MAAM;QACN,MAAM;IASR;IAEA,sGAAsG;IACtG,MAAM,eAAe,EAAU,EAA2B;QACxD,wCAAe;YACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;YAC1B,IAAI;gBACF,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;gBAClC,MAAM,MAAM,KAAK,GAAG,CAAC;gBACrB,OAAO,MAAM,oBAAoB,OAAO;YAC1C,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,OAAO;YACT;QACF;QACA,MAAM,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACjD,OAAO,UAAU;YAAE,GAAG,OAAO;QAAC,IAAI;IACpC;IAEA,4BAA4B;IAC5B,eAA2B;QACzB,wCAAe;YACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE;YAC5B,IAAI;gBACF,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;gBAClC,MAAM,OAAO,KAAK,GAAG;gBACrB,OAAO,KAAK,GAAG,CAAC;YAClB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,OAAO,EAAE;YACX;QACF;QACA,sCAAsC;QACtC,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG;YAClC,QAAQ,IAAI,CAAC;YACb,IAAI,CAAC,SAAS,GAAG,EAAE;YACnB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS;QAC3D;QACA,OAAO;eAAI,IAAI,CAAC,SAAS;SAAC;IAC5B;IAEA,cAAc,KAAiB,EAAQ;QACrC,wCAAe;YACb,iFAAiF;YACjF,QAAQ,IAAI,CAAC;YACb;QACF;;IAGF;IAEA,gBAAgB,EAAU,EAAmB;QAC3C,wCAAe;YACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;YAC1B,IAAI;gBACF,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;gBAClC,MAAM,MAAM,KAAK,GAAG,CAAC;gBACrB,OAAO,MAAM,qBAAqB,OAAO;YAC3C,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;gBACvD,OAAO;YACT;QACF;QACA,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC/C,OAAO,OAAO;YAAE,GAAG,IAAI;QAAC,IAAI;IAC9B;IAEA,kBAAkB,IAAY,EAAmB;QAC/C,wCAAe;YACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;YAC1B,IAAI;gBACF,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;gBAClC,MAAM,MAAM,KAAK,GAAG,CAAC;gBACrB,OAAO,MAAM,qBAAqB,OAAO;YAC3C,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2CAA2C;gBACzD,OAAO;YACT;QACF;QACA,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QACjD,OAAO,OAAO;YAAE,GAAG,IAAI;QAAC,IAAI;IAC9B;IAEA,eAAe,QAA0D,EAAmB;QAC1F,wCAAe;YACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;YAC1B,IAAI;gBACF,MAAM,MAAM,IAAI,OAAO,WAAW;gBAClC,MAAM,QAAQ,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD;gBAEnB,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;;;;;;;QAOnC,CAAC;gBAED,KAAK,GAAG,CACN,OACA,SAAS,KAAK,EACd,SAAS,QAAQ,IAAI,MACrB,SAAS,IAAI,EACb,SAAS,OAAO,EAChB,SAAS,UAAU,IAAI,MACvB,SAAS,OAAO,EAChB,SAAS,UAAU,IAAI,MACvB,SAAS,MAAM,EACf,SAAS,WAAW,IAAI,MACxB,SAAS,WAAW,IAAI,MACxB,SAAS,UAAU,IAAI,MACvB,SAAS,QAAQ,EACjB,KAAK,SAAS,CAAC,SAAS,IAAI,IAAI,EAAE,GAClC,KAAK,SAAS,CAAC,SAAS,QAAQ,IAAI,EAAE,GACtC,SAAS,WAAW,EACpB,SAAS,QAAQ,IAAI,MACrB,SAAS,QAAQ,GAAG,IAAI,GACxB,SAAS,MAAM,IAAI,SACnB,SAAS,SAAS,IAAI,MACtB,SAAS,eAAe,IAAI,MAC5B,SAAS,KAAK,IAAI,GAClB,SAAS,KAAK,IAAI,GAClB,KACA;gBAGF,OAAO,IAAI,CAAC,eAAe,CAAC;YAC9B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,OAAO;YACT;QACF;QAEA,mBAAmB;QACnB,MAAM,MAAM,IAAI,OAAO,WAAW;QAClC,MAAM,UAAoB;YACxB,GAAG,QAAQ;YACX,IAAI,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD;YACT,WAAW;YACX,WAAW;QACb;QAEA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QACpB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS;QACzD,OAAO;YAAE,GAAG,OAAO;QAAC;IACtB;IAEA,eAAe,EAAU,EAAE,QAA2B,EAAmB;QACvE,wCAAe;YACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;YAC1B,IAAI;gBACF,MAAM,MAAM,IAAI,OAAO,WAAW;gBAClC,MAAM,aAAa;oBAAE,GAAG,QAAQ;oBAAE,YAAY;gBAAI;gBAElD,6BAA6B;gBAC7B,MAAM,SAAS,OAAO,IAAI,CAAC,YAAY,MAAM,CAAC,CAAA,MAAO,QAAQ;gBAC7D,MAAM,aAAa,OAAO,GAAG,CAAC,CAAA;oBAC5B,uDAAuD;oBACvD,MAAM,QAAQ,IAAI,OAAO,CAAC,UAAU,CAAA,SAAU,CAAC,CAAC,EAAE,OAAO,WAAW,IAAI;oBACxE,OAAO,GAAG,MAAM,IAAI,CAAC;gBACvB,GAAG,IAAI,CAAC;gBAER,MAAM,SAAS,OAAO,GAAG,CAAC,CAAA;oBACxB,MAAM,QAAQ,UAAU,CAAC,IAAsB;oBAC/C,IAAI,QAAQ,UAAU,QAAQ,YAAY;wBACxC,OAAO,KAAK,SAAS,CAAC,SAAS,EAAE;oBACnC;oBACA,IAAI,QAAQ,YAAY;wBACtB,OAAO,QAAQ,IAAI;oBACrB;oBACA,OAAO;gBACT;gBAEA,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,sBAAsB,EAAE,WAAW,aAAa,CAAC;gBACpF,KAAK,GAAG,IAAI,QAAQ;gBAEpB,OAAO,IAAI,CAAC,eAAe,CAAC;YAC9B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,OAAO;YACT;QACF;QAEA,mBAAmB;QACnB,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACzD,IAAI,cAAc,CAAC,GAAG,OAAO;QAE7B,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG;YAC1B,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU;YAC5B,GAAG,QAAQ;YACX,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS;QACzD,OAAO;YAAE,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU;QAAC;IACxC;IAEA,eAAe,EAAU,EAAW;QAClC,wCAAe;YACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;YAC1B,IAAI;gBACF,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;gBAClC,MAAM,SAAS,KAAK,GAAG,CAAC;gBACxB,OAAO,OAAO,OAAO,GAAG;YAC1B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,OAAO;YACT;QACF;QAEA,mBAAmB;QACnB,MAAM,gBAAgB,IAAI,CAAC,SAAS,CAAC,MAAM;QAC3C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAErD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,eAAe;YACzC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS;YACzD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,0DAA0D;IAC1D,MAAM,kBAAiC;QACrC,wCAAe;YACX,QAAQ,IAAI,CAAC;YACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACnB,IAAI;gBACA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAClB,QAAQ,GAAG,CAAC;YAChB,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,mDAAmD;YACrE;YACA;QACJ;;IAKF;AAGF;AAKO,MAAM,SAAS,IAAI,mBAAmB", "debugId": null}}, {"offset": {"line": 993, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/lib/auth/nextauth.ts"], "sourcesContent": ["/**\n * NextAuth.js Configuration\n * Provides enterprise-grade authentication with backward compatibility\n */\n\nimport { NextAuthOptions } from 'next-auth';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport { sqlite } from '../sqlite';\nimport bcrypt from 'bcryptjs';\nimport { User } from '../../types';\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      id: 'credentials',\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          throw new Error('Email and password are required');\n        }\n\n        try {\n          // Get user from database\n          const users = await sqlite.getUsers();\n          const user = users.find(u => u.email.toLowerCase() === credentials.email.toLowerCase());\n\n          if (!user) {\n            throw new Error('Invalid email or password');\n          }\n\n          // For backward compatibility, check if user has password_hash\n          // If not, this might be a legacy user - handle accordingly\n          let isValidPassword = false;\n          \n          if (user.password_hash) {\n            isValidPassword = await bcrypt.compare(credentials.password, user.password_hash);\n          } else {\n            // Legacy user without hashed password - this shouldn't happen in production\n            // but we handle it for development/migration scenarios\n            console.warn('User found without password hash - this should not happen in production');\n            return null;\n          }\n\n          if (!isValidPassword) {\n            throw new Error('Invalid email or password');\n          }\n\n          // Return user object that will be stored in JWT\n          return {\n            id: user.id,\n            email: user.email,\n            name: `${user.firstName || ''} ${user.lastName || ''}`.trim(),\n            firstName: user.firstName,\n            lastName: user.lastName,\n            role: user.role,\n            avatar: user.avatar,\n            phone: user.phone,\n            address: user.address,\n            preferences: user.preferences,\n            company: user.company,\n          };\n        } catch (error) {\n          console.error('Authentication error:', error);\n          throw new Error('Authentication failed');\n        }\n      }\n    })\n  ],\n  \n  session: {\n    strategy: 'jwt',\n    maxAge: 30 * 24 * 60 * 60, // 30 days\n  },\n  \n  jwt: {\n    maxAge: 30 * 24 * 60 * 60, // 30 days\n  },\n  \n  callbacks: {\n    async jwt({ token, user, account }) {\n      // Initial sign in\n      if (account && user) {\n        return {\n          ...token,\n          id: user.id,\n          role: user.role,\n          firstName: user.firstName,\n          lastName: user.lastName,\n          avatar: user.avatar,\n          phone: user.phone,\n          address: user.address,\n          preferences: user.preferences,\n          company: user.company,\n        };\n      }\n\n      // Return previous token if the access token has not expired yet\n      return token;\n    },\n    \n    async session({ session, token }) {\n      // Send properties to the client\n      if (token) {\n        session.user = {\n          ...session.user,\n          id: token.id as string,\n          role: token.role as string,\n          firstName: token.firstName as string,\n          lastName: token.lastName as string,\n          avatar: token.avatar as string,\n          phone: token.phone as string,\n          address: token.address as any,\n          preferences: token.preferences as any,\n          company: token.company as string,\n        };\n      }\n      \n      return session;\n    },\n    \n    async redirect({ url, baseUrl }) {\n      // Allows relative callback URLs\n      if (url.startsWith('/')) return `${baseUrl}${url}`;\n      // Allows callback URLs on the same origin\n      else if (new URL(url).origin === baseUrl) return url;\n      return baseUrl;\n    }\n  },\n  \n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup',\n    error: '/auth/error',\n  },\n  \n  events: {\n    async signIn({ user, account, profile, isNewUser }) {\n      console.log('User signed in:', user.email);\n      \n      // Update last login time\n      try {\n        const users = await sqlite.getUsers();\n        const existingUser = users.find(u => u.id === user.id);\n        if (existingUser) {\n          // Note: For now we'll skip updating last login in SQLite\n          // This would require implementing an updateUser method\n          console.log('User signed in:', user.email);\n        }\n      } catch (error) {\n        console.error('Error updating last login:', error);\n      }\n    },\n    \n    async signOut({ token }) {\n      console.log('User signed out:', token?.email);\n    }\n  },\n  \n  debug: process.env.NODE_ENV === 'development',\n  \n  secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-change-in-production',\n};\n\n/**\n * Helper function to create a new user with NextAuth compatibility\n */\nexport async function createUserWithAuth(userData: {\n  email: string;\n  password: string;\n  firstName?: string;\n  lastName?: string;\n  role?: 'admin' | 'user';\n  phone?: string;\n  company?: string;\n}): Promise<User | null> {\n  try {\n    // Hash password\n    const saltRounds = 12;\n    const password_hash = await bcrypt.hash(userData.password, saltRounds);\n    \n    // Create user object\n    const newUser: User = {\n      id: `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n      email: userData.email.toLowerCase(),\n      password_hash,\n      firstName: userData.firstName || '',\n      lastName: userData.lastName || '',\n      role: userData.role || 'user',\n      phone: userData.phone,\n      company: userData.company,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n      preferences: {\n        language: 'ar',\n        theme: 'light',\n        notifications: true,\n        newsletter: false\n      }\n    };\n    \n    // Save to database\n    const createdUser = await sqlite.createUser({\n      ...userData,\n      password: userData.password, // This will be hashed by the sqlite service\n    });\n\n    if (!createdUser) {\n      throw new Error('Failed to create user');\n    }\n    \n    return createdUser;\n  } catch (error) {\n    console.error('Error creating user:', error);\n    return null;\n  }\n}\n\n/**\n * Helper function to verify user credentials\n */\nexport async function verifyCredentials(email: string, password: string): Promise<User | null> {\n  try {\n    const users = await sqlite.getUsers();\n    const user = users.find(u => u.email.toLowerCase() === email.toLowerCase());\n\n    if (!user || !user.password_hash) {\n      return null;\n    }\n\n    const isValid = await bcrypt.compare(password, user.password_hash);\n    return isValid ? user : null;\n  } catch (error) {\n    console.error('Error verifying credentials:', error);\n    return null;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AACA;AACA;;;;AAGO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,IAAI;YACJ,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,MAAM,IAAI,MAAM;gBAClB;gBAEA,IAAI;oBACF,yBAAyB;oBACzB,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ;oBACnC,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,WAAW,OAAO,YAAY,KAAK,CAAC,WAAW;oBAEpF,IAAI,CAAC,MAAM;wBACT,MAAM,IAAI,MAAM;oBAClB;oBAEA,8DAA8D;oBAC9D,2DAA2D;oBAC3D,IAAI,kBAAkB;oBAEtB,IAAI,KAAK,aAAa,EAAE;wBACtB,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,YAAY,QAAQ,EAAE,KAAK,aAAa;oBACjF,OAAO;wBACL,4EAA4E;wBAC5E,uDAAuD;wBACvD,QAAQ,IAAI,CAAC;wBACb,OAAO;oBACT;oBAEA,IAAI,CAAC,iBAAiB;wBACpB,MAAM,IAAI,MAAM;oBAClB;oBAEA,gDAAgD;oBAChD,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,MAAM,GAAG,KAAK,SAAS,IAAI,GAAG,CAAC,EAAE,KAAK,QAAQ,IAAI,IAAI,CAAC,IAAI;wBAC3D,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;wBACvB,MAAM,KAAK,IAAI;wBACf,QAAQ,KAAK,MAAM;wBACnB,OAAO,KAAK,KAAK;wBACjB,SAAS,KAAK,OAAO;wBACrB,aAAa,KAAK,WAAW;wBAC7B,SAAS,KAAK,OAAO;oBACvB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,yBAAyB;oBACvC,MAAM,IAAI,MAAM;gBAClB;YACF;QACF;KACD;IAED,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IAEA,KAAK;QACH,QAAQ,KAAK,KAAK,KAAK;IACzB;IAEA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE;YAChC,kBAAkB;YAClB,IAAI,WAAW,MAAM;gBACnB,OAAO;oBACL,GAAG,KAAK;oBACR,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;oBACvB,QAAQ,KAAK,MAAM;oBACnB,OAAO,KAAK,KAAK;oBACjB,SAAS,KAAK,OAAO;oBACrB,aAAa,KAAK,WAAW;oBAC7B,SAAS,KAAK,OAAO;gBACvB;YACF;YAEA,gEAAgE;YAChE,OAAO;QACT;QAEA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,gCAAgC;YAChC,IAAI,OAAO;gBACT,QAAQ,IAAI,GAAG;oBACb,GAAG,QAAQ,IAAI;oBACf,IAAI,MAAM,EAAE;oBACZ,MAAM,MAAM,IAAI;oBAChB,WAAW,MAAM,SAAS;oBAC1B,UAAU,MAAM,QAAQ;oBACxB,QAAQ,MAAM,MAAM;oBACpB,OAAO,MAAM,KAAK;oBAClB,SAAS,MAAM,OAAO;oBACtB,aAAa,MAAM,WAAW;oBAC9B,SAAS,MAAM,OAAO;gBACxB;YACF;YAEA,OAAO;QACT;QAEA,MAAM,UAAS,EAAE,GAAG,EAAE,OAAO,EAAE;YAC7B,gCAAgC;YAChC,IAAI,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,UAAU,KAAK;iBAE7C,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,SAAS,OAAO;YACjD,OAAO;QACT;IACF;IAEA,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,OAAO;IACT;IAEA,QAAQ;QACN,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE;YAChD,QAAQ,GAAG,CAAC,mBAAmB,KAAK,KAAK;YAEzC,yBAAyB;YACzB,IAAI;gBACF,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ;gBACnC,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;gBACrD,IAAI,cAAc;oBAChB,yDAAyD;oBACzD,uDAAuD;oBACvD,QAAQ,GAAG,CAAC,mBAAmB,KAAK,KAAK;gBAC3C;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;YAC9C;QACF;QAEA,MAAM,SAAQ,EAAE,KAAK,EAAE;YACrB,QAAQ,GAAG,CAAC,oBAAoB,OAAO;QACzC;IACF;IAEA,OAAO,oDAAyB;IAEhC,QAAQ,QAAQ,GAAG,CAAC,eAAe,IAAI;AACzC;AAKO,eAAe,mBAAmB,QAQxC;IACC,IAAI;QACF,gBAAgB;QAChB,MAAM,aAAa;QACnB,MAAM,gBAAgB,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,SAAS,QAAQ,EAAE;QAE3D,qBAAqB;QACrB,MAAM,UAAgB;YACpB,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YACnE,OAAO,SAAS,KAAK,CAAC,WAAW;YACjC;YACA,WAAW,SAAS,SAAS,IAAI;YACjC,UAAU,SAAS,QAAQ,IAAI;YAC/B,MAAM,SAAS,IAAI,IAAI;YACvB,OAAO,SAAS,KAAK;YACrB,SAAS,SAAS,OAAO;YACzB,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;YACjC,aAAa;gBACX,UAAU;gBACV,OAAO;gBACP,eAAe;gBACf,YAAY;YACd;QACF;QAEA,mBAAmB;QACnB,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC;YAC1C,GAAG,QAAQ;YACX,UAAU,SAAS,QAAQ;QAC7B;QAEA,IAAI,CAAC,aAAa;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;IACT;AACF;AAKO,eAAe,kBAAkB,KAAa,EAAE,QAAgB;IACrE,IAAI;QACF,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ;QACnC,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,WAAW,OAAO,MAAM,WAAW;QAExE,IAAI,CAAC,QAAQ,CAAC,KAAK,aAAa,EAAE;YAChC,OAAO;QACT;QAEA,MAAM,UAAU,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU,KAAK,aAAa;QACjE,OAAO,UAAU,OAAO;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth';\nimport { authOptions } from '../../../../lib/auth/nextauth';\n\nconst handler = NextAuth(authOptions);\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,gIAAA,CAAA,cAAW", "debugId": null}}]}