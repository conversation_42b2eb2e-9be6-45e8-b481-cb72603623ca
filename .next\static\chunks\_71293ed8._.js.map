{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/pages/NotFoundPage.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { HomeIcon } from 'lucide-react';\n\nexport default function NotFoundPage() {\n  return (\n    <div className=\"min-h-[80vh] flex items-center justify-center\">\n      <div className=\"text-center space-y-6\">\n        <h1 className=\"text-6xl font-bold text-gray-900\">404</h1>\n        <h2 className=\"text-2xl font-semibold text-gray-700\">Page Not Found</h2>\n        <p className=\"text-gray-600 max-w-md mx-auto\">\n          The page you're looking for doesn't exist or has been moved.\n        </p>\n        <Link\n          href=\"/\"\n          className=\"inline-flex items-center gap-2 px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n        >\n          <HomeIcon size={20} />\n          Return Home\n        </Link>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAmC;;;;;;8BACjD,6LAAC;oBAAG,WAAU;8BAAuC;;;;;;8BACrD,6LAAC;oBAAE,WAAU;8BAAiC;;;;;;8BAG9C,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;;sCAEV,6LAAC,yMAAA,CAAA,WAAQ;4BAAC,MAAM;;;;;;wBAAM;;;;;;;;;;;;;;;;;;AAMhC;KAnBwB", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/app/%5Blocale%5D/not-found.tsx"], "sourcesContent": ["'use client';\n\nimport { Suspense } from 'react';\nimport NotFoundPage from '../../pages/NotFoundPage';\n\n// Loading fallback component\nconst LoadingFallback = () => (\n  <div className=\"min-h-screen flex items-center justify-center\">\n    <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500\"></div>\n  </div>\n);\n\nexport default function NotFound() {\n  return (\n    <Suspense fallback={<LoadingFallback />}>\n      <NotFoundPage />\n    </Suspense>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,6BAA6B;AAC7B,MAAM,kBAAkB,kBACtB,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;;;;;;;;;;KAFb;AAMS,SAAS;IACtB,qBACE,6LAAC,6JAAA,CAAA,WAAQ;QAAC,wBAAU,6LAAC;;;;;kBACnB,cAAA,6LAAC,gIAAA,CAAA,UAAY;;;;;;;;;;AAGnB;MANwB", "debugId": null}}]}