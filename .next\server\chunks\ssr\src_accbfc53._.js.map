{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/ui/Breadcrumb.tsx"], "sourcesContent": ["'use client';\n\nimport { Fragment } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { ChevronRight, Home } from 'lucide-react';\nimport { cn } from '../../lib/utils';\nimport { useTranslation } from '../../translations';\n\nexport interface BreadcrumbItem {\n  label: string;\n  href?: string;\n  isCurrentPage?: boolean;\n}\n\ninterface BreadcrumbProps {\n  items?: BreadcrumbItem[];\n  className?: string;\n  showHome?: boolean;\n  separator?: 'chevron' | 'slash';\n}\n\nexport function Breadcrumb({ \n  items = [], \n  className = '', \n  showHome = true, \n  separator = 'chevron' \n}: BreadcrumbProps) {\n  const { language } = useTranslation();\n  const pathname = usePathname();\n  const currentLanguage = language;\n  const isRTL = currentLanguage === 'ar';\n\n  // Auto-generate breadcrumbs if no items provided\n  const breadcrumbItems = items.length > 0 ? items : generateBreadcrumbs(pathname, currentLanguage);\n\n  // Add home item if showHome is true and not already present\n  const finalItems = showHome && breadcrumbItems[0]?.label !== (currentLanguage === 'ar' ? 'الرئيسية' : 'Home')\n    ? [\n        {\n          label: currentLanguage === 'ar' ? 'الرئيسية' : 'Home',\n          href: `/${currentLanguage}`,\n          isCurrentPage: false\n        },\n        ...breadcrumbItems\n      ]\n    : breadcrumbItems;\n\n  if (finalItems.length <= 1) {\n    return null; // Don't show breadcrumb for single item or empty\n  }\n\n  const SeparatorIcon = separator === 'chevron' ? ChevronRight : null;\n\n  return (\n    <nav \n      aria-label={currentLanguage === 'ar' ? 'مسار التنقل' : 'Breadcrumb navigation'}\n      className={cn(\n        \"flex items-center space-x-2 rtl:space-x-reverse text-sm\",\n        \"py-4 px-6 bg-slate-50/50 dark:bg-slate-900/50 border-b border-slate-200/50 dark:border-slate-700/50\",\n        className\n      )}\n      dir={isRTL ? 'rtl' : 'ltr'}\n    >\n      <ol className=\"flex items-center space-x-2 rtl:space-x-reverse\">\n        {finalItems.map((item, index) => {\n          const isLast = index === finalItems.length - 1;\n          const isFirst = index === 0;\n\n          return (\n            <Fragment key={index}>\n              <li className=\"flex items-center\">\n                {item.href && !item.isCurrentPage ? (\n                  <Link\n                    href={item.href}\n                    className={cn(\n                      \"flex items-center gap-2 text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-200\",\n                      \"transition-colors duration-200 font-medium\",\n                      \"hover:underline underline-offset-4 decoration-2 decoration-slate-300 dark:decoration-slate-600\"\n                    )}\n                  >\n                    {isFirst && showHome && (\n                      <Home className=\"h-4 w-4 flex-shrink-0\" />\n                    )}\n                    <span className=\"truncate max-w-[200px] sm:max-w-none\">\n                      {item.label}\n                    </span>\n                  </Link>\n                ) : (\n                  <span\n                    className={cn(\n                      \"flex items-center gap-2 font-semibold\",\n                      isLast \n                        ? \"text-slate-900 dark:text-slate-100\" \n                        : \"text-slate-600 dark:text-slate-400\"\n                    )}\n                    aria-current={isLast ? \"page\" : undefined}\n                  >\n                    {isFirst && showHome && (\n                      <Home className=\"h-4 w-4 flex-shrink-0\" />\n                    )}\n                    <span className=\"truncate max-w-[200px] sm:max-w-none\">\n                      {item.label}\n                    </span>\n                  </span>\n                )}\n              </li>\n\n              {!isLast && (\n                <li className=\"flex items-center\" aria-hidden=\"true\">\n                  {separator === 'chevron' ? (\n                    <ChevronRight className={cn(\n                      \"h-4 w-4 text-slate-400 dark:text-slate-600 flex-shrink-0\",\n                      isRTL && \"rotate-180\"\n                    )} />\n                  ) : (\n                    <span className=\"text-slate-400 dark:text-slate-600 mx-2\">/</span>\n                  )}\n                </li>\n              )}\n            </Fragment>\n          );\n        })}\n      </ol>\n    </nav>\n  );\n}\n\n// Auto-generate breadcrumbs from pathname\nfunction generateBreadcrumbs(pathname: string, language: string): BreadcrumbItem[] {\n  const segments = pathname.split('/').filter(Boolean);\n  const breadcrumbs: BreadcrumbItem[] = [];\n  \n  // Remove language segment\n  if (segments[0] === 'en' || segments[0] === 'ar') {\n    segments.shift();\n  }\n\n  let currentPath = `/${language}`;\n\n  segments.forEach((segment, index) => {\n    currentPath += `/${segment}`;\n    const isLast = index === segments.length - 1;\n    \n    // Generate label based on segment\n    const label = generateSegmentLabel(segment, language);\n    \n    breadcrumbs.push({\n      label,\n      href: isLast ? undefined : currentPath,\n      isCurrentPage: isLast\n    });\n  });\n\n  return breadcrumbs;\n}\n\n// Generate human-readable labels for URL segments\nfunction generateSegmentLabel(segment: string, language: string): string {\n  const isArabic = language === 'ar';\n  \n  // Common translations\n  const translations: Record<string, { en: string; ar: string }> = {\n    'shop': { en: 'Shop', ar: 'المتجر' },\n    'product': { en: 'Product', ar: 'المنتج' },\n    'category': { en: 'Category', ar: 'الفئة' },\n    'electronics': { en: 'Electronics', ar: 'الإلكترونيات' },\n    'computers': { en: 'Computers', ar: 'أجهزة الكمبيوتر' },\n    'phones': { en: 'Phones', ar: 'الهواتف' },\n    'accessories': { en: 'Accessories', ar: 'الإكسسوارات' },\n    'machinery': { en: 'Machinery', ar: 'الآلات' },\n    'services': { en: 'Services', ar: 'الخدمات' },\n    'clearance': { en: 'Clearance', ar: 'التصفية' },\n    'wholesale': { en: 'Wholesale', ar: 'الجملة' },\n    'blog': { en: 'Blog', ar: 'المدونة' },\n    'about': { en: 'About', ar: 'حول' },\n    'contact': { en: 'Contact', ar: 'اتصل بنا' },\n    'faq': { en: 'FAQ', ar: 'الأسئلة الشائعة' },\n    'shipping': { en: 'Shipping', ar: 'معلومات الشحن' },\n    'privacy': { en: 'Privacy Policy', ar: 'سياسة الخصوصية' },\n    'terms': { en: 'Terms of Service', ar: 'شروط الخدمة' }\n  };\n\n  // Check if we have a translation\n  if (translations[segment]) {\n    return isArabic ? translations[segment].ar : translations[segment].en;\n  }\n\n  // Convert slug to title case\n  return segment\n    .split('-')\n    .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n    .join(' ');\n}\n\nexport default Breadcrumb;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AAPA;;;;;;;;AAsBO,SAAS,WAAW,EACzB,QAAQ,EAAE,EACV,YAAY,EAAE,EACd,WAAW,IAAI,EACf,YAAY,SAAS,EACL;IAChB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD;IAClC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,kBAAkB;IACxB,MAAM,QAAQ,oBAAoB;IAElC,iDAAiD;IACjD,MAAM,kBAAkB,MAAM,MAAM,GAAG,IAAI,QAAQ,oBAAoB,UAAU;IAEjF,4DAA4D;IAC5D,MAAM,aAAa,YAAY,eAAe,CAAC,EAAE,EAAE,UAAU,CAAC,oBAAoB,OAAO,aAAa,MAAM,IACxG;QACE;YACE,OAAO,oBAAoB,OAAO,aAAa;YAC/C,MAAM,CAAC,CAAC,EAAE,iBAAiB;YAC3B,eAAe;QACjB;WACG;KACJ,GACD;IAEJ,IAAI,WAAW,MAAM,IAAI,GAAG;QAC1B,OAAO,MAAM,iDAAiD;IAChE;IAEA,MAAM,gBAAgB,cAAc,YAAY,sNAAA,CAAA,eAAY,GAAG;IAE/D,qBACE,8OAAC;QACC,cAAY,oBAAoB,OAAO,gBAAgB;QACvD,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA,uGACA;QAEF,KAAK,QAAQ,QAAQ;kBAErB,cAAA,8OAAC;YAAG,WAAU;sBACX,WAAW,GAAG,CAAC,CAAC,MAAM;gBACrB,MAAM,SAAS,UAAU,WAAW,MAAM,GAAG;gBAC7C,MAAM,UAAU,UAAU;gBAE1B,qBACE,8OAAC,qMAAA,CAAA,WAAQ;;sCACP,8OAAC;4BAAG,WAAU;sCACX,KAAK,IAAI,IAAI,CAAC,KAAK,aAAa,iBAC/B,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6GACA,8CACA;;oCAGD,WAAW,0BACV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAElB,8OAAC;wCAAK,WAAU;kDACb,KAAK,KAAK;;;;;;;;;;;qDAIf,8OAAC;gCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yCACA,SACI,uCACA;gCAEN,gBAAc,SAAS,SAAS;;oCAE/B,WAAW,0BACV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAElB,8OAAC;wCAAK,WAAU;kDACb,KAAK,KAAK;;;;;;;;;;;;;;;;;wBAMlB,CAAC,wBACA,8OAAC;4BAAG,WAAU;4BAAoB,eAAY;sCAC3C,cAAc,0BACb,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACxB,4DACA,SAAS;;;;;qDAGX,8OAAC;gCAAK,WAAU;0CAA0C;;;;;;;;;;;;mBA9CnD;;;;;YAoDnB;;;;;;;;;;;AAIR;AAEA,0CAA0C;AAC1C,SAAS,oBAAoB,QAAgB,EAAE,QAAgB;IAC7D,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;IAC5C,MAAM,cAAgC,EAAE;IAExC,0BAA0B;IAC1B,IAAI,QAAQ,CAAC,EAAE,KAAK,QAAQ,QAAQ,CAAC,EAAE,KAAK,MAAM;QAChD,SAAS,KAAK;IAChB;IAEA,IAAI,cAAc,CAAC,CAAC,EAAE,UAAU;IAEhC,SAAS,OAAO,CAAC,CAAC,SAAS;QACzB,eAAe,CAAC,CAAC,EAAE,SAAS;QAC5B,MAAM,SAAS,UAAU,SAAS,MAAM,GAAG;QAE3C,kCAAkC;QAClC,MAAM,QAAQ,qBAAqB,SAAS;QAE5C,YAAY,IAAI,CAAC;YACf;YACA,MAAM,SAAS,YAAY;YAC3B,eAAe;QACjB;IACF;IAEA,OAAO;AACT;AAEA,kDAAkD;AAClD,SAAS,qBAAqB,OAAe,EAAE,QAAgB;IAC7D,MAAM,WAAW,aAAa;IAE9B,sBAAsB;IACtB,MAAM,eAA2D;QAC/D,QAAQ;YAAE,IAAI;YAAQ,IAAI;QAAS;QACnC,WAAW;YAAE,IAAI;YAAW,IAAI;QAAS;QACzC,YAAY;YAAE,IAAI;YAAY,IAAI;QAAQ;QAC1C,eAAe;YAAE,IAAI;YAAe,IAAI;QAAe;QACvD,aAAa;YAAE,IAAI;YAAa,IAAI;QAAkB;QACtD,UAAU;YAAE,IAAI;YAAU,IAAI;QAAU;QACxC,eAAe;YAAE,IAAI;YAAe,IAAI;QAAc;QACtD,aAAa;YAAE,IAAI;YAAa,IAAI;QAAS;QAC7C,YAAY;YAAE,IAAI;YAAY,IAAI;QAAU;QAC5C,aAAa;YAAE,IAAI;YAAa,IAAI;QAAU;QAC9C,aAAa;YAAE,IAAI;YAAa,IAAI;QAAS;QAC7C,QAAQ;YAAE,IAAI;YAAQ,IAAI;QAAU;QACpC,SAAS;YAAE,IAAI;YAAS,IAAI;QAAM;QAClC,WAAW;YAAE,IAAI;YAAW,IAAI;QAAW;QAC3C,OAAO;YAAE,IAAI;YAAO,IAAI;QAAkB;QAC1C,YAAY;YAAE,IAAI;YAAY,IAAI;QAAgB;QAClD,WAAW;YAAE,IAAI;YAAkB,IAAI;QAAiB;QACxD,SAAS;YAAE,IAAI;YAAoB,IAAI;QAAc;IACvD;IAEA,iCAAiC;IACjC,IAAI,YAAY,CAAC,QAAQ,EAAE;QACzB,OAAO,WAAW,YAAY,CAAC,QAAQ,CAAC,EAAE,GAAG,YAAY,CAAC,QAAQ,CAAC,EAAE;IACvE;IAEA,6BAA6B;IAC7B,OAAO,QACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;AACV;uCAEe", "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/hooks/useBreadcrumbs.ts"], "sourcesContent": ["'use client';\n\nimport { useMemo } from 'react';\nimport { usePathname } from 'next/navigation';\nimport { BreadcrumbItem } from '../components/ui/Breadcrumb';\n\ninterface UseBreadcrumbsOptions {\n  customItems?: BreadcrumbItem[];\n  productName?: string;\n  categoryName?: string;\n  serviceName?: string;\n  blogTitle?: string;\n  language: string;\n}\n\nexport function useBreadcrumbs({\n  customItems,\n  productName,\n  categoryName,\n  serviceName,\n  blogTitle,\n  language\n}: UseBreadcrumbsOptions) {\n  const pathname = usePathname();\n  \n  return useMemo(() => {\n    if (customItems) {\n      return customItems;\n    }\n\n    const isArabic = language === 'ar';\n    const segments = pathname.split('/').filter(Boolean);\n    \n    // Remove language segment\n    if (segments[0] === 'en' || segments[0] === 'ar') {\n      segments.shift();\n    }\n\n    const breadcrumbs: BreadcrumbItem[] = [];\n    let currentPath = `/${language}`;\n\n    // Handle different page types\n    if (segments[0] === 'shop') {\n      // Shop pages\n      breadcrumbs.push({\n        label: isArabic ? 'المتجر' : 'Shop',\n        href: `/${language}/shop`,\n        isCurrentPage: segments.length === 1\n      });\n\n      if (segments[1] === 'product' && segments[2]) {\n        // Product detail page\n        if (categoryName) {\n          breadcrumbs.push({\n            label: categoryName,\n            href: `/${language}/shop?category=${encodeURIComponent(categoryName.toLowerCase())}`,\n            isCurrentPage: false\n          });\n        }\n        \n        breadcrumbs.push({\n          label: productName || segments[2].replace(/-/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase()),\n          isCurrentPage: true\n        });\n      } else if (segments[1] && segments[1] !== 'product') {\n        // Category page\n        breadcrumbs.push({\n          label: categoryName || segments[1].replace(/-/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase()),\n          isCurrentPage: true\n        });\n      }\n    } else if (segments[0] === 'machinery') {\n      // Machinery pages\n      breadcrumbs.push({\n        label: isArabic ? 'الآلات' : 'Machinery',\n        href: `/${language}/machinery`,\n        isCurrentPage: segments.length === 1\n      });\n\n      if (segments[1]) {\n        breadcrumbs.push({\n          label: productName || segments[1].replace(/-/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase()),\n          isCurrentPage: true\n        });\n      }\n    } else if (segments[0] === 'services') {\n      // Services pages\n      breadcrumbs.push({\n        label: isArabic ? 'الخدمات' : 'Services',\n        href: `/${language}/services`,\n        isCurrentPage: segments.length === 1\n      });\n\n      if (segments[1]) {\n        breadcrumbs.push({\n          label: serviceName || segments[1].replace(/-/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase()),\n          isCurrentPage: true\n        });\n      }\n    } else if (segments[0] === 'clearance') {\n      // Clearance pages\n      breadcrumbs.push({\n        label: isArabic ? 'التصفية' : 'Clearance',\n        href: `/${language}/clearance`,\n        isCurrentPage: segments.length === 1\n      });\n\n      if (segments[1]) {\n        breadcrumbs.push({\n          label: segments[1].replace(/-/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase()),\n          isCurrentPage: true\n        });\n      }\n    } else if (segments[0] === 'wholesale') {\n      // Wholesale pages\n      breadcrumbs.push({\n        label: isArabic ? 'الجملة' : 'Wholesale',\n        href: `/${language}/wholesale`,\n        isCurrentPage: segments.length === 1\n      });\n\n      if (segments[1]) {\n        breadcrumbs.push({\n          label: segments[1].replace(/-/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase()),\n          isCurrentPage: true\n        });\n      }\n    } else if (segments[0] === 'blog') {\n      // Blog pages\n      breadcrumbs.push({\n        label: isArabic ? 'المدونة' : 'Blog',\n        href: `/${language}/blog`,\n        isCurrentPage: segments.length === 1\n      });\n\n      if (segments[1]) {\n        breadcrumbs.push({\n          label: blogTitle || segments[1].replace(/-/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase()),\n          isCurrentPage: true\n        });\n      }\n    } else if (segments[0] === 'about') {\n      breadcrumbs.push({\n        label: isArabic ? 'حول' : 'About',\n        isCurrentPage: true\n      });\n    } else if (segments[0] === 'contact') {\n      breadcrumbs.push({\n        label: isArabic ? 'اتصل بنا' : 'Contact',\n        isCurrentPage: true\n      });\n    } else if (segments[0] === 'faq') {\n      breadcrumbs.push({\n        label: isArabic ? 'الأسئلة الشائعة' : 'FAQ',\n        isCurrentPage: true\n      });\n    } else if (segments[0] === 'shipping') {\n      breadcrumbs.push({\n        label: isArabic ? 'معلومات الشحن' : 'Shipping',\n        isCurrentPage: true\n      });\n    } else if (segments[0] === 'privacy') {\n      breadcrumbs.push({\n        label: isArabic ? 'سياسة الخصوصية' : 'Privacy Policy',\n        isCurrentPage: true\n      });\n    } else if (segments[0] === 'terms') {\n      breadcrumbs.push({\n        label: isArabic ? 'شروط الخدمة' : 'Terms of Service',\n        isCurrentPage: true\n      });\n    } else if (segments[0] === 'account') {\n      // Account pages\n      breadcrumbs.push({\n        label: isArabic ? 'حسابي' : 'My Account',\n        href: `/${language}/account`,\n        isCurrentPage: segments.length === 1\n      });\n\n      if (segments[1]) {\n        const accountPageLabels: Record<string, { en: string; ar: string }> = {\n          'orders': { en: 'Orders', ar: 'الطلبات' },\n          'addresses': { en: 'Addresses', ar: 'العناوين' },\n          'payment-methods': { en: 'Payment Methods', ar: 'طرق الدفع' },\n          'settings': { en: 'Settings', ar: 'الإعدادات' },\n          'loyalty': { en: 'Loyalty Program', ar: 'برنامج الولاء' }\n        };\n\n        const pageLabel = accountPageLabels[segments[1]];\n        breadcrumbs.push({\n          label: pageLabel ? (isArabic ? pageLabel.ar : pageLabel.en) : segments[1].replace(/-/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase()),\n          isCurrentPage: true\n        });\n      }\n    } else if (segments[0] === 'shop' && segments[1] === 'wishlist') {\n      // Wishlist page (special case under shop)\n      breadcrumbs.push({\n        label: isArabic ? 'المتجر' : 'Shop',\n        href: `/${language}/shop`,\n        isCurrentPage: false\n      });\n      breadcrumbs.push({\n        label: isArabic ? 'المفضلة' : 'Wishlist',\n        isCurrentPage: true\n      });\n    } else if (segments[0]) {\n      // Generic page\n      breadcrumbs.push({\n        label: segments[0].replace(/-/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase()),\n        isCurrentPage: true\n      });\n    }\n\n    return breadcrumbs;\n  }, [pathname, customItems, productName, categoryName, serviceName, blogTitle, language]);\n}\n\nexport default useBreadcrumbs;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;AAeO,SAAS,eAAe,EAC7B,WAAW,EACX,WAAW,EACX,YAAY,EACZ,WAAW,EACX,SAAS,EACT,QAAQ,EACc;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACb,IAAI,aAAa;YACf,OAAO;QACT;QAEA,MAAM,WAAW,aAAa;QAC9B,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QAE5C,0BAA0B;QAC1B,IAAI,QAAQ,CAAC,EAAE,KAAK,QAAQ,QAAQ,CAAC,EAAE,KAAK,MAAM;YAChD,SAAS,KAAK;QAChB;QAEA,MAAM,cAAgC,EAAE;QACxC,IAAI,cAAc,CAAC,CAAC,EAAE,UAAU;QAEhC,8BAA8B;QAC9B,IAAI,QAAQ,CAAC,EAAE,KAAK,QAAQ;YAC1B,aAAa;YACb,YAAY,IAAI,CAAC;gBACf,OAAO,WAAW,WAAW;gBAC7B,MAAM,CAAC,CAAC,EAAE,SAAS,KAAK,CAAC;gBACzB,eAAe,SAAS,MAAM,KAAK;YACrC;YAEA,IAAI,QAAQ,CAAC,EAAE,KAAK,aAAa,QAAQ,CAAC,EAAE,EAAE;gBAC5C,sBAAsB;gBACtB,IAAI,cAAc;oBAChB,YAAY,IAAI,CAAC;wBACf,OAAO;wBACP,MAAM,CAAC,CAAC,EAAE,SAAS,eAAe,EAAE,mBAAmB,aAAa,WAAW,KAAK;wBACpF,eAAe;oBACjB;gBACF;gBAEA,YAAY,IAAI,CAAC;oBACf,OAAO,eAAe,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;oBACxF,eAAe;gBACjB;YACF,OAAO,IAAI,QAAQ,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,KAAK,WAAW;gBACnD,gBAAgB;gBAChB,YAAY,IAAI,CAAC;oBACf,OAAO,gBAAgB,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;oBACzF,eAAe;gBACjB;YACF;QACF,OAAO,IAAI,QAAQ,CAAC,EAAE,KAAK,aAAa;YACtC,kBAAkB;YAClB,YAAY,IAAI,CAAC;gBACf,OAAO,WAAW,WAAW;gBAC7B,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC;gBAC9B,eAAe,SAAS,MAAM,KAAK;YACrC;YAEA,IAAI,QAAQ,CAAC,EAAE,EAAE;gBACf,YAAY,IAAI,CAAC;oBACf,OAAO,eAAe,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;oBACxF,eAAe;gBACjB;YACF;QACF,OAAO,IAAI,QAAQ,CAAC,EAAE,KAAK,YAAY;YACrC,iBAAiB;YACjB,YAAY,IAAI,CAAC;gBACf,OAAO,WAAW,YAAY;gBAC9B,MAAM,CAAC,CAAC,EAAE,SAAS,SAAS,CAAC;gBAC7B,eAAe,SAAS,MAAM,KAAK;YACrC;YAEA,IAAI,QAAQ,CAAC,EAAE,EAAE;gBACf,YAAY,IAAI,CAAC;oBACf,OAAO,eAAe,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;oBACxF,eAAe;gBACjB;YACF;QACF,OAAO,IAAI,QAAQ,CAAC,EAAE,KAAK,aAAa;YACtC,kBAAkB;YAClB,YAAY,IAAI,CAAC;gBACf,OAAO,WAAW,YAAY;gBAC9B,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC;gBAC9B,eAAe,SAAS,MAAM,KAAK;YACrC;YAEA,IAAI,QAAQ,CAAC,EAAE,EAAE;gBACf,YAAY,IAAI,CAAC;oBACf,OAAO,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;oBACzE,eAAe;gBACjB;YACF;QACF,OAAO,IAAI,QAAQ,CAAC,EAAE,KAAK,aAAa;YACtC,kBAAkB;YAClB,YAAY,IAAI,CAAC;gBACf,OAAO,WAAW,WAAW;gBAC7B,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC;gBAC9B,eAAe,SAAS,MAAM,KAAK;YACrC;YAEA,IAAI,QAAQ,CAAC,EAAE,EAAE;gBACf,YAAY,IAAI,CAAC;oBACf,OAAO,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;oBACzE,eAAe;gBACjB;YACF;QACF,OAAO,IAAI,QAAQ,CAAC,EAAE,KAAK,QAAQ;YACjC,aAAa;YACb,YAAY,IAAI,CAAC;gBACf,OAAO,WAAW,YAAY;gBAC9B,MAAM,CAAC,CAAC,EAAE,SAAS,KAAK,CAAC;gBACzB,eAAe,SAAS,MAAM,KAAK;YACrC;YAEA,IAAI,QAAQ,CAAC,EAAE,EAAE;gBACf,YAAY,IAAI,CAAC;oBACf,OAAO,aAAa,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;oBACtF,eAAe;gBACjB;YACF;QACF,OAAO,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS;YAClC,YAAY,IAAI,CAAC;gBACf,OAAO,WAAW,QAAQ;gBAC1B,eAAe;YACjB;QACF,OAAO,IAAI,QAAQ,CAAC,EAAE,KAAK,WAAW;YACpC,YAAY,IAAI,CAAC;gBACf,OAAO,WAAW,aAAa;gBAC/B,eAAe;YACjB;QACF,OAAO,IAAI,QAAQ,CAAC,EAAE,KAAK,OAAO;YAChC,YAAY,IAAI,CAAC;gBACf,OAAO,WAAW,oBAAoB;gBACtC,eAAe;YACjB;QACF,OAAO,IAAI,QAAQ,CAAC,EAAE,KAAK,YAAY;YACrC,YAAY,IAAI,CAAC;gBACf,OAAO,WAAW,kBAAkB;gBACpC,eAAe;YACjB;QACF,OAAO,IAAI,QAAQ,CAAC,EAAE,KAAK,WAAW;YACpC,YAAY,IAAI,CAAC;gBACf,OAAO,WAAW,mBAAmB;gBACrC,eAAe;YACjB;QACF,OAAO,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS;YAClC,YAAY,IAAI,CAAC;gBACf,OAAO,WAAW,gBAAgB;gBAClC,eAAe;YACjB;QACF,OAAO,IAAI,QAAQ,CAAC,EAAE,KAAK,WAAW;YACpC,gBAAgB;YAChB,YAAY,IAAI,CAAC;gBACf,OAAO,WAAW,UAAU;gBAC5B,MAAM,CAAC,CAAC,EAAE,SAAS,QAAQ,CAAC;gBAC5B,eAAe,SAAS,MAAM,KAAK;YACrC;YAEA,IAAI,QAAQ,CAAC,EAAE,EAAE;gBACf,MAAM,oBAAgE;oBACpE,UAAU;wBAAE,IAAI;wBAAU,IAAI;oBAAU;oBACxC,aAAa;wBAAE,IAAI;wBAAa,IAAI;oBAAW;oBAC/C,mBAAmB;wBAAE,IAAI;wBAAmB,IAAI;oBAAY;oBAC5D,YAAY;wBAAE,IAAI;wBAAY,IAAI;oBAAY;oBAC9C,WAAW;wBAAE,IAAI;wBAAmB,IAAI;oBAAgB;gBAC1D;gBAEA,MAAM,YAAY,iBAAiB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAChD,YAAY,IAAI,CAAC;oBACf,OAAO,YAAa,WAAW,UAAU,EAAE,GAAG,UAAU,EAAE,GAAI,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;oBAChI,eAAe;gBACjB;YACF;QACF,OAAO,IAAI,QAAQ,CAAC,EAAE,KAAK,UAAU,QAAQ,CAAC,EAAE,KAAK,YAAY;YAC/D,0CAA0C;YAC1C,YAAY,IAAI,CAAC;gBACf,OAAO,WAAW,WAAW;gBAC7B,MAAM,CAAC,CAAC,EAAE,SAAS,KAAK,CAAC;gBACzB,eAAe;YACjB;YACA,YAAY,IAAI,CAAC;gBACf,OAAO,WAAW,YAAY;gBAC9B,eAAe;YACjB;QACF,OAAO,IAAI,QAAQ,CAAC,EAAE,EAAE;YACtB,eAAe;YACf,YAAY,IAAI,CAAC;gBACf,OAAO,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;gBACzE,eAAe;YACjB;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAU;QAAa;QAAa;QAAc;QAAa;QAAW;KAAS;AACzF;uCAEe", "debugId": null}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/data/unifiedServices.ts"], "sourcesContent": ["import { Service, ServiceCategory, ServiceFormField, ServiceWorkflow } from '../types/index';\n\n// Unified Service Categories - Merging business services with regular services\nexport const unifiedServiceCategories: ServiceCategory[] = [\n  {\n    id: 'order-management',\n    name: 'End-to-End Order Management',\n    name_ar: 'إدارة الطلبيات الشاملة',\n    description: 'Complete order lifecycle management from product sourcing to final delivery, with dedicated account management and real-time tracking.',\n    description_ar: 'إدارة دورة حياة الطلبيات الكاملة من مصادر المنتجات وحتى التسليم النهائي، مع إدارة حسابات مخصصة وتتبع في الوقت الفعلي.',\n    icon: 'Package',\n    order: 1,\n    isActive: true,\n    services: ['order-management-service', 'bulk-order-processing']\n  },\n  {\n    id: 'quality-assurance',\n    name: 'Quality Assurance & Inspection',\n    name_ar: '<PERSON><PERSON><PERSON> الجودة والفحص',\n    description: 'Comprehensive quality control services including pre-shipment inspections, factory audits, and product testing to ensure excellence.',\n    description_ar: 'خدمات شاملة لمراقبة الجودة تشمل فحوصات ما قبل الشحن وتدقيق المصانع واختبار المنتجات لضمان التميز.',\n    icon: 'Search',\n    order: 2,\n    isActive: true,\n    services: ['inspection-service', 'quality-control', 'factory-audits']\n  },\n  {\n    id: 'logistics-shipping',\n    name: 'Global Logistics & Shipping',\n    name_ar: 'الخدمات اللوجستية والشحن العالمي',\n    description: 'International shipping solutions including air freight, sea shipping, customs clearance, and door-to-door delivery services.',\n    description_ar: 'حلول الشحن الدولي تشمل الشحن الجوي والبحري والتخليص الجمركي وخدمات التوصيل من الباب إلى الباب.',\n    icon: 'Ship',\n    order: 3,\n    isActive: true,\n    services: ['shipping-service', 'customs-clearance', 'freight-forwarding']\n  },\n  {\n    id: 'storage-warehousing',\n    name: 'Storage & Warehousing Solutions',\n    name_ar: 'حلول التخزين والمستودعات',\n    description: 'Secure warehousing facilities with advanced inventory management, climate control, and fulfillment services.',\n    description_ar: 'مرافق تخزين آمنة مع إدارة متقدمة للمخزون وتحكم في المناخ وخدمات الوفاء بالطلبات.',\n    icon: 'Warehouse',\n    order: 4,\n    isActive: true,\n    services: ['storage-service', 'inventory-management', 'fulfillment-services']\n  },\n  {\n    id: 'certification-compliance',\n    name: 'Certification & Compliance',\n    name_ar: 'الشهادات والامتثال',\n    description: 'Expert assistance with product certifications, regulatory compliance, and documentation for global market access.',\n    description_ar: 'مساعدة خبيرة في شهادات المنتجات والامتثال التنظيمي والوثائق للوصول إلى الأسواق العالمية.',\n    icon: 'FileCheck',\n    order: 5,\n    isActive: true,\n    services: ['certification-service', 'regulatory-compliance', 'documentation-support']\n  },\n  {\n    id: 'business-consulting',\n    name: 'Strategic Business Consulting',\n    name_ar: 'الاستشارات التجارية الاستراتيجية',\n    description: 'Professional consulting services for supply chain optimization, market entry strategies, and operational efficiency.',\n    description_ar: 'خدمات استشارية مهنية لتحسين سلسلة التوريد واستراتيجيات دخول السوق والكفاءة التشغيلية.',\n    icon: 'TrendingUp',\n    order: 6,\n    isActive: true,\n    services: ['consulting-service', 'market-analysis', 'operational-optimization']\n  }\n];\n\n// Unified Services - Comprehensive list merging both systems\nexport const unifiedServices: Service[] = [\n  // Order Management Services\n  {\n    id: 'order-management-service',\n    name: 'Complete Order Management Solutions',\n    name_ar: 'حلول إدارة الطلبيات الشاملة',\n    slug: 'order-management',\n    description: 'Transform your procurement process with our end-to-end order management service. From product sourcing to final delivery, we handle every aspect of your orders with dedicated account management, competitive pricing negotiations, and guaranteed quality control.',\n    description_ar: 'حوّل عملية الشراء الخاصة بك مع خدمة إدارة الطلبيات الشاملة. من مصادر المنتجات وحتى التسليم النهائي، نتعامل مع كل جانب من طلبياتك مع إدارة حسابات مخصصة ومفاوضات أسعار تنافسية وضمان مراقبة الجودة.',\n    icon: 'Package',\n    features: [\n      'Dedicated account manager for personalized service',\n      'Product sourcing from verified suppliers',\n      'Competitive price negotiations',\n      'Quality control at every stage',\n      'Real-time order tracking and updates',\n      'Flexible payment terms and options',\n      'Risk management and insurance coverage'\n    ],\n    features_ar: [\n      'مدير حساب مخصص للخدمة الشخصية',\n      'مصادر المنتجات من موردين معتمدين',\n      'مفاوضات أسعار تنافسية',\n      'مراقبة الجودة في كل مرحلة',\n      'تتبع الطلبات والتحديثات في الوقت الفعلي',\n      'شروط ومخيارات دفع مرنة',\n      'إدارة المخاطر وتغطية التأمين'\n    ],\n    category: 'order-management',\n    pricing: {\n      model: 'percentage',\n      basePrice: 0,\n      currency: 'USD',\n      description: '3-5% of order value',\n      description_ar: '3-5% من قيمة الطلب'\n    },\n    isBusinessService: true,\n    createdAt: new Date().toISOString()\n  },\n  {\n    id: 'bulk-order-processing',\n    name: 'Bulk Order Processing',\n    name_ar: 'معالجة الطلبات الكبيرة',\n    slug: 'bulk-orders',\n    description: 'Streamlined processing for large volume orders with special pricing, priority handling, and dedicated logistics coordination.',\n    description_ar: 'معالجة مبسطة للطلبات الكبيرة مع تسعير خاص ومعالجة ذات أولوية وتنسيق لوجستي مخصص.',\n    icon: 'Package',\n    features: [\n      'Volume-based pricing discounts',\n      'Priority order processing',\n      'Dedicated logistics coordination',\n      'Flexible delivery scheduling',\n      'Custom packaging solutions',\n      'Extended payment terms',\n      'Bulk order tracking dashboard'\n    ],\n    features_ar: [\n      'خصومات تسعير على أساس الحجم',\n      'معالجة الطلبات ذات الأولوية',\n      'تنسيق لوجستي مخصص',\n      'جدولة تسليم مرنة',\n      'حلول تعبئة مخصصة',\n      'شروط دفع ممتدة',\n      'لوحة تتبع الطلبات الكبيرة'\n    ],\n    category: 'order-management',\n    pricing: {\n      model: 'tiered',\n      basePrice: 0,\n      currency: 'USD',\n      description: 'Tiered pricing based on volume',\n      description_ar: 'تسعير متدرج على أساس الحجم'\n    },\n    isBusinessService: true,\n    createdAt: new Date().toISOString()\n  },\n  // Quality Assurance Services\n  {\n    id: 'inspection-service',\n    name: 'Comprehensive Inspection Services',\n    name_ar: 'خدمات الفحص الشاملة',\n    slug: 'inspection',\n    description: 'Professional quality control and product inspection services at every stage of your supply chain, ensuring products meet international standards.',\n    description_ar: 'خدمات مراقبة الجودة وفحص المنتجات المهنية في كل مرحلة من مراحل سلسلة التوريد، مما يضمن أن المنتجات تلبي المعايير الدولية.',\n    icon: 'Search',\n    features: [\n      'Pre-shipment quality inspections',\n      'During production monitoring',\n      'Container loading supervision',\n      'Factory audits and assessments',\n      'Product testing and verification',\n      'Detailed inspection reports with photos',\n      'Quality control consulting and training'\n    ],\n    features_ar: [\n      'فحص الجودة قبل الشحن',\n      'مراقبة أثناء الإنتاج',\n      'الإشراف على تحميل الحاويات',\n      'تدقيق وتقييم المصانع',\n      'اختبار المنتجات والتحقق منها',\n      'تقارير فحص مفصلة مع صور',\n      'استشارات وتدريب مراقبة الجودة'\n    ],\n    category: 'quality-assurance',\n    pricing: {\n      model: 'fixed',\n      basePrice: 300,\n      currency: 'USD',\n      description: 'Starting from $300 per inspection',\n      description_ar: 'يبدأ من 300 دولار لكل فحص'\n    },\n    isBusinessService: true,\n    createdAt: new Date().toISOString()\n  },\n  {\n    id: 'quality-control',\n    name: 'Advanced Quality Control Systems',\n    name_ar: 'أنظمة مراقبة الجودة المتقدمة',\n    slug: 'quality-control',\n    description: 'Implement comprehensive quality control systems with real-time monitoring, statistical process control, and continuous improvement methodologies.',\n    description_ar: 'تنفيذ أنظمة مراقبة الجودة الشاملة مع المراقبة في الوقت الفعلي والتحكم الإحصائي في العمليات ومنهجيات التحسين المستمر.',\n    icon: 'Award',\n    features: [\n      'Statistical process control implementation',\n      'Real-time quality monitoring systems',\n      'Quality management system setup',\n      'Continuous improvement programs',\n      'Quality metrics and KPI tracking',\n      'Staff training and certification',\n      'Quality documentation and procedures'\n    ],\n    features_ar: [\n      'تنفيذ التحكم الإحصائي في العمليات',\n      'أنظمة مراقبة الجودة في الوقت الفعلي',\n      'إعداد نظام إدارة الجودة',\n      'برامج التحسين المستمر',\n      'تتبع مقاييس الجودة ومؤشرات الأداء الرئيسية',\n      'تدريب الموظفين والحصول على الشهادات',\n      'وثائق وإجراءات الجودة'\n    ],\n    category: 'quality-assurance',\n    pricing: {\n      model: 'project',\n      basePrice: 2000,\n      currency: 'USD',\n      description: 'Project-based pricing from $2,000',\n      description_ar: 'تسعير على أساس المشروع من 2000 دولار'\n    },\n    isBusinessService: true,\n    createdAt: new Date().toISOString()\n  },\n  // Logistics & Shipping Services\n  {\n    id: 'shipping-service',\n    name: 'Global Shipping & Logistics',\n    name_ar: 'الشحن والخدمات اللوجستية العالمية',\n    slug: 'shipping',\n    description: 'End-to-end international shipping solutions including air freight, sea shipping, customs clearance, and door-to-door delivery services.',\n    description_ar: 'حلول الشحن الدولي الشاملة تشمل الشحن الجوي والبحري والتخليص الجمركي وخدمات التوصيل من الباب إلى الباب.',\n    icon: 'Ship',\n    features: [\n      'International air freight services',\n      'Ocean freight and container shipping',\n      'Door-to-door delivery solutions',\n      'Customs clearance and documentation',\n      'Cargo insurance and protection',\n      'Real-time shipment tracking',\n      'Specialized handling for fragile items'\n    ],\n    features_ar: [\n      'خدمات الشحن الجوي الدولي',\n      'الشحن البحري وشحن الحاويات',\n      'حلول التوصيل من الباب إلى الباب',\n      'التخليص الجمركي والوثائق',\n      'تأمين وحماية البضائع',\n      'تتبع الشحنات في الوقت الفعلي',\n      'المناولة المتخصصة للعناصر الهشة'\n    ],\n    category: 'logistics-shipping',\n    pricing: {\n      model: 'weight_volume',\n      basePrice: 0,\n      currency: 'USD',\n      description: 'Based on weight, volume, and destination',\n      description_ar: 'على أساس الوزن والحجم والوجهة'\n    },\n    isBusinessService: true,\n    createdAt: new Date().toISOString()\n  },\n  {\n    id: 'customs-clearance',\n    name: 'Customs Clearance & Documentation',\n    name_ar: 'التخليص الجمركي والوثائق',\n    slug: 'customs-clearance',\n    description: 'Expert customs clearance services with complete documentation support, duty optimization, and compliance management for smooth international trade.',\n    description_ar: 'خدمات التخليص الجمركي الخبيرة مع دعم الوثائق الكامل وتحسين الرسوم وإدارة الامتثال للتجارة الدولية السلسة.',\n    icon: 'FileCheck',\n    features: [\n      'Complete customs documentation',\n      'Duty and tax optimization',\n      'Import/export license assistance',\n      'Compliance verification',\n      'Customs bond arrangements',\n      'Expedited clearance services',\n      'Regulatory updates and guidance'\n    ],\n    features_ar: [\n      'وثائق جمركية كاملة',\n      'تحسين الرسوم والضرائب',\n      'مساعدة في تراخيص الاستيراد/التصدير',\n      'التحقق من الامتثال',\n      'ترتيبات الضمان الجمركي',\n      'خدمات التخليص المعجل',\n      'التحديثات والإرشادات التنظيمية'\n    ],\n    category: 'logistics-shipping',\n    pricing: {\n      model: 'fixed',\n      basePrice: 150,\n      currency: 'USD',\n      description: 'Starting from $150 per shipment',\n      description_ar: 'يبدأ من 150 دولار لكل شحنة'\n    },\n    isBusinessService: true,\n    createdAt: new Date().toISOString()\n  },\n  // Storage & Warehousing Services\n  {\n    id: 'storage-service',\n    name: 'Advanced Storage Solutions',\n    name_ar: 'حلول التخزين المتقدمة',\n    slug: 'storage',\n    description: 'Secure, climate-controlled warehousing facilities with advanced inventory management systems, real-time tracking, and flexible storage options.',\n    description_ar: 'مرافق تخزين آمنة ومتحكم في مناخها مع أنظمة إدارة المخزون المتقدمة والتتبع في الوقت الفعلي وخيارات التخزين المرنة.',\n    icon: 'Warehouse',\n    features: [\n      'Climate-controlled storage facilities',\n      'Advanced security systems and monitoring',\n      'Real-time inventory tracking and management',\n      'Short and long-term storage options',\n      'Pick, pack, and fulfillment services',\n      'Cross-docking capabilities',\n      'Inventory management software integration'\n    ],\n    features_ar: [\n      'مرافق تخزين متحكم في مناخها',\n      'أنظمة أمان ومراقبة متقدمة',\n      'تتبع وإدارة المخزون في الوقت الفعلي',\n      'خيارات تخزين قصيرة وطويلة الأجل',\n      'خدمات الانتقاء والتعبئة والوفاء',\n      'قدرات النقل المتقاطع',\n      'تكامل برامج إدارة المخزون'\n    ],\n    category: 'storage-warehousing',\n    pricing: {\n      model: 'monthly',\n      basePrice: 50,\n      currency: 'USD',\n      description: 'From $50 per pallet per month',\n      description_ar: 'من 50 دولار لكل منصة نقالة شهرياً'\n    },\n    isBusinessService: true,\n    createdAt: new Date().toISOString()\n  },\n  // Certification & Compliance Services\n  {\n    id: 'certification-service',\n    name: 'Product Certification & Compliance',\n    name_ar: 'شهادات المنتجات والامتثال',\n    slug: 'certification',\n    description: 'Expert assistance with product certifications, regulatory compliance, and documentation requirements for global market access and regulatory approval.',\n    description_ar: 'مساعدة خبيرة في شهادات المنتجات والامتثال التنظيمي ومتطلبات الوثائق للوصول إلى الأسواق العالمية والموافقة التنظيمية.',\n    icon: 'Award',\n    features: [\n      'CE certification assistance and guidance',\n      'FDA compliance support and documentation',\n      'ISO certification consulting and implementation',\n      'Product testing coordination and management',\n      'Regulatory documentation preparation',\n      'Compliance monitoring and updates',\n      'International standards consultation'\n    ],\n    features_ar: [\n      'المساعدة والإرشاد في شهادة CE',\n      'دعم الامتثال لمعايير FDA والوثائق',\n      'استشارات وتنفيذ شهادة ISO',\n      'تنسيق وإدارة اختبار المنتجات',\n      'إعداد الوثائق التنظيمية',\n      'مراقبة الامتثال والتحديثات',\n      'استشارات المعايير الدولية'\n    ],\n    category: 'certification-compliance',\n    pricing: {\n      model: 'project',\n      basePrice: 1500,\n      currency: 'USD',\n      description: 'Project-based from $1,500',\n      description_ar: 'على أساس المشروع من 1500 دولار'\n    },\n    isBusinessService: true,\n    createdAt: new Date().toISOString()\n  },\n  // Business Consulting Services\n  {\n    id: 'consulting-service',\n    name: 'Strategic Business Consulting',\n    name_ar: 'الاستشارات التجارية الاستراتيجية',\n    slug: 'consulting',\n    description: 'Professional consulting services for supply chain optimization, market entry strategies, operational efficiency, and business growth planning.',\n    description_ar: 'خدمات استشارية مهنية لتحسين سلسلة التوريد واستراتيجيات دخول السوق والكفاءة التشغيلية وتخطيط نمو الأعمال.',\n    icon: 'TrendingUp',\n    features: [\n      'Supply chain optimization and analysis',\n      'Market entry strategies and planning',\n      'Operational efficiency improvements',\n      'Cost reduction analysis and implementation',\n      'Vendor management and sourcing strategies',\n      'Risk assessment and mitigation planning',\n      'Business growth and expansion planning'\n    ],\n    features_ar: [\n      'تحسين وتحليل سلسلة التوريد',\n      'استراتيجيات وتخطيط دخول السوق',\n      'تحسينات الكفاءة التشغيلية',\n      'تحليل وتنفيذ خفض التكاليف',\n      'إدارة الموردين واستراتيجيات المصادر',\n      'تقييم المخاطر وتخطيط التخفيف',\n      'تخطيط نمو وتوسع الأعمال'\n    ],\n    category: 'business-consulting',\n    pricing: {\n      model: 'hourly',\n      basePrice: 200,\n      currency: 'USD',\n      description: 'From $200 per hour',\n      description_ar: 'من 200 دولار في الساعة'\n    },\n    isBusinessService: true,\n    createdAt: new Date().toISOString()\n  }\n];\n\n// Export helper functions for service management\nexport const getUnifiedServicesByCategory = (categoryId: string): Service[] => {\n  return unifiedServices.filter(service => service.category === categoryId);\n};\n\nexport const getUnifiedServiceBySlug = (slug: string): Service | undefined => {\n  return unifiedServices.find(service => service.slug === slug);\n};\n\nexport const getFeaturedUnifiedServices = (limit: number = 6): Service[] => {\n  return unifiedServices.slice(0, limit);\n};\n\nexport const getActiveUnifiedCategories = (): ServiceCategory[] => {\n  return unifiedServiceCategories.filter(category => category.isActive);\n};\n"], "names": [], "mappings": ";;;;;;;;AAGO,MAAM,2BAA8C;IACzD;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,OAAO;QACP,UAAU;QACV,UAAU;YAAC;YAA4B;SAAwB;IACjE;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,OAAO;QACP,UAAU;QACV,UAAU;YAAC;YAAsB;YAAmB;SAAiB;IACvE;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,OAAO;QACP,UAAU;QACV,UAAU;YAAC;YAAoB;YAAqB;SAAqB;IAC3E;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,OAAO;QACP,UAAU;QACV,UAAU;YAAC;YAAmB;YAAwB;SAAuB;IAC/E;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,OAAO;QACP,UAAU;QACV,UAAU;YAAC;YAAyB;YAAyB;SAAwB;IACvF;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,OAAO;QACP,UAAU;QACV,UAAU;YAAC;YAAsB;YAAmB;SAA2B;IACjF;CACD;AAGM,MAAM,kBAA6B;IACxC,4BAA4B;IAC5B;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,MAAM;QACN,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,SAAS;YACP,OAAO;YACP,WAAW;YACX,UAAU;YACV,aAAa;YACb,gBAAgB;QAClB;QACA,mBAAmB;QACnB,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,MAAM;QACN,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,SAAS;YACP,OAAO;YACP,WAAW;YACX,UAAU;YACV,aAAa;YACb,gBAAgB;QAClB;QACA,mBAAmB;QACnB,WAAW,IAAI,OAAO,WAAW;IACnC;IACA,6BAA6B;IAC7B;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,MAAM;QACN,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,SAAS;YACP,OAAO;YACP,WAAW;YACX,UAAU;YACV,aAAa;YACb,gBAAgB;QAClB;QACA,mBAAmB;QACnB,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,MAAM;QACN,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,SAAS;YACP,OAAO;YACP,WAAW;YACX,UAAU;YACV,aAAa;YACb,gBAAgB;QAClB;QACA,mBAAmB;QACnB,WAAW,IAAI,OAAO,WAAW;IACnC;IACA,gCAAgC;IAChC;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,MAAM;QACN,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,SAAS;YACP,OAAO;YACP,WAAW;YACX,UAAU;YACV,aAAa;YACb,gBAAgB;QAClB;QACA,mBAAmB;QACnB,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,MAAM;QACN,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,SAAS;YACP,OAAO;YACP,WAAW;YACX,UAAU;YACV,aAAa;YACb,gBAAgB;QAClB;QACA,mBAAmB;QACnB,WAAW,IAAI,OAAO,WAAW;IACnC;IACA,iCAAiC;IACjC;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,MAAM;QACN,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,SAAS;YACP,OAAO;YACP,WAAW;YACX,UAAU;YACV,aAAa;YACb,gBAAgB;QAClB;QACA,mBAAmB;QACnB,WAAW,IAAI,OAAO,WAAW;IACnC;IACA,sCAAsC;IACtC;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,MAAM;QACN,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,SAAS;YACP,OAAO;YACP,WAAW;YACX,UAAU;YACV,aAAa;YACb,gBAAgB;QAClB;QACA,mBAAmB;QACnB,WAAW,IAAI,OAAO,WAAW;IACnC;IACA,+BAA+B;IAC/B;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,MAAM;QACN,aAAa;QACb,gBAAgB;QAChB,MAAM;QACN,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,SAAS;YACP,OAAO;YACP,WAAW;YACX,UAAU;YACV,aAAa;YACb,gBAAgB;QAClB;QACA,mBAAmB;QACnB,WAAW,IAAI,OAAO,WAAW;IACnC;CACD;AAGM,MAAM,+BAA+B,CAAC;IAC3C,OAAO,gBAAgB,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;AAChE;AAEO,MAAM,0BAA0B,CAAC;IACtC,OAAO,gBAAgB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,KAAK;AAC1D;AAEO,MAAM,6BAA6B,CAAC,QAAgB,CAAC;IAC1D,OAAO,gBAAgB,KAAK,CAAC,GAAG;AAClC;AAEO,MAAM,6BAA6B;IACxC,OAAO,yBAAyB,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ;AACtE", "debugId": null}}, {"offset": {"line": 944, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/services/BusinessServiceRequestForm.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { \n  Send, \n  Upload, \n  X, \n  FileText, \n  AlertCircle,\n  CheckCircle,\n  Loader2\n} from 'lucide-react';\nimport { Button } from '../ui/Button';\nimport { Card } from '../ui/Card';\nimport { useLanguageStore } from '../../stores/languageStore';\nimport { useThemeStore } from '../../stores/themeStore';\nimport { useAuthStore } from '../../stores/authStore';\nimport { cn } from '../../lib/utils';\nimport { Service, ServiceFormField } from '../../types';\nimport { createServiceRequest } from '../../services/BusinessServiceService';\n\ninterface BusinessServiceRequestFormProps {\n  service: Service;\n  onClose?: () => void;\n  onSuccess?: (requestId: string) => void;\n}\n\nexport function BusinessServiceRequestForm({ \n  service, \n  onClose, \n  onSuccess \n}: BusinessServiceRequestFormProps) {\n  const { language } = useLanguageStore();\n  const { theme } = useThemeStore();\n  const { user, isAuthenticated } = useAuthStore();\n  const router = useRouter();\n  \n  const [formData, setFormData] = useState<Record<string, any>>({});\n  const [files, setFiles] = useState<File[]>([]);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\n\n  // التحقق من تسجيل الدخول\n  useEffect(() => {\n    if (!isAuthenticated) {\n      router.push('/auth/signin');\n      return;\n    }\n  }, [isAuthenticated, router]);\n\n  // معالجة تغيير قيم النموذج\n  const handleInputChange = (fieldId: string, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldId]: value\n    }));\n    \n    // إزالة الخطأ عند التغيير\n    if (errors[fieldId]) {\n      setErrors(prev => {\n        const newErrors = { ...prev };\n        delete newErrors[fieldId];\n        return newErrors;\n      });\n    }\n  };\n\n  // معالجة رفع الملفات\n  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const selectedFiles = Array.from(event.target.files || []);\n    setFiles(prev => [...prev, ...selectedFiles]);\n  };\n\n  // إزالة ملف\n  const removeFile = (index: number) => {\n    setFiles(prev => prev.filter((_, i) => i !== index));\n  };\n\n  // التحقق من صحة النموذج\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {};\n    \n    service.formFields?.forEach(field => {\n      if (field.required && !formData[field.id]) {\n        newErrors[field.id] = language === 'ar' \n          ? 'هذا الحقل مطلوب'\n          : 'This field is required';\n      }\n      \n      // التحقق من صحة البريد الإلكتروني\n      if (field.id === 'customer_email' && formData[field.id]) {\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(formData[field.id])) {\n          newErrors[field.id] = language === 'ar'\n            ? 'البريد الإلكتروني غير صالح'\n            : 'Invalid email address';\n        }\n      }\n    });\n    \n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // إرسال النموذج\n  const handleSubmit = async (event: React.FormEvent) => {\n    event.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n    \n    setIsSubmitting(true);\n    setSubmitStatus('idle');\n    \n    try {\n      // إعداد بيانات العميل\n      const customerInfo = {\n        name: formData.customer_name || '',\n        email: formData.customer_email || '',\n        phone: formData.customer_phone || '',\n        company: formData.company_name || ''\n      };\n      \n      // إنشاء طلب الخدمة\n      const request = await createServiceRequest({\n        serviceId: service.id.toString(),\n        serviceName: service.name,\n        serviceName_ar: service.name_ar,\n        userId: user?.id || '',\n        customerInfo,\n        formData,\n        attachments: [] // سيتم معالجة الملفات لاحقاً\n      });\n      \n      if (request) {\n        setSubmitStatus('success');\n        onSuccess?.(request.id);\n        \n        // إعادة توجيه إلى صفحة تتبع الطلب\n        setTimeout(() => {\n          router.push(`/dashboard/services/${request.id}`);\n        }, 2000);\n      } else {\n        throw new Error('Failed to create service request');\n      }\n    } catch (error) {\n      console.error('Error submitting service request:', error);\n      setSubmitStatus('error');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // عرض حالة النجاح\n  if (submitStatus === 'success') {\n    return (\n      <Card className=\"p-8 text-center\">\n        <CheckCircle className=\"h-16 w-16 text-green-500 mx-auto mb-4\" />\n        <h3 className=\"text-xl font-bold mb-2\">\n          {language === 'ar' ? 'تم إرسال الطلب بنجاح!' : 'Request Submitted Successfully!'}\n        </h3>\n        <p className=\"text-gray-600 mb-4\">\n          {language === 'ar' \n            ? 'سيتم مراجعة طلبك وإرسال عرض السعر قريباً'\n            : 'Your request will be reviewed and a quote will be sent soon'\n          }\n        </p>\n        <Button onClick={() => router.push('/dashboard')}>\n          {language === 'ar' ? 'الذهاب إلى لوحة التحكم' : 'Go to Dashboard'}\n        </Button>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto\">\n      <Card className=\"p-6\">\n        {/* رأس النموذج */}\n        <div className=\"flex items-center justify-between mb-6\">\n          <div>\n            <h2 className=\"text-2xl font-bold\">\n              {language === 'ar' ? 'تقديم طلبية جديد' : 'Submit New Request'}\n            </h2>\n            <p className=\"text-gray-600 mt-1\">\n              {language === 'ar' \n                ? `أهلاً بك في نظام إدارة الطلبيات لـ 'ارتال'. يرجى ملء التفاصيل التالية لطلبك لضمان معالجة سريعة ودقيقة.`\n                : `Welcome to Artal's order management system. Please fill in the following details for your request to ensure quick and accurate processing.`\n              }\n            </p>\n          </div>\n          {onClose && (\n            <Button variant=\"ghost\" size=\"sm\" onClick={onClose}>\n              <X className=\"h-4 w-4\" />\n            </Button>\n          )}\n        </div>\n\n        {/* النموذج */}\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* حقول النموذج */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            {service.formFields?.map((field) => (\n              <div \n                key={field.id} \n                className={cn(\n                  field.type === 'textarea' && 'md:col-span-2'\n                )}\n              >\n                <label className=\"block text-sm font-medium mb-2\">\n                  {language === 'ar' ? field.name_ar || field.name : field.name}\n                  {field.required && <span className=\"text-red-500 mr-1\">*</span>}\n                </label>\n                \n                {field.type === 'textarea' ? (\n                  <textarea\n                    value={formData[field.id] || ''}\n                    onChange={(e) => handleInputChange(field.id, e.target.value)}\n                    placeholder={language === 'ar' ? field.placeholder_ar || field.placeholder : field.placeholder}\n                    className={cn(\n                      'w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent',\n                      errors[field.id] && 'border-red-500',\n                      'min-h-[100px]'\n                    )}\n                    rows={4}\n                  />\n                ) : field.type === 'file' ? (\n                  <div>\n                    <input\n                      type=\"file\"\n                      multiple\n                      accept={field.validation?.fileTypes?.join(',')}\n                      onChange={handleFileUpload}\n                      className=\"hidden\"\n                      id={`file-${field.id}`}\n                    />\n                    <label\n                      htmlFor={`file-${field.id}`}\n                      className=\"flex items-center justify-center w-full px-4 py-6 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-primary-500 transition-colors\"\n                    >\n                      <div className=\"text-center\">\n                        <Upload className=\"h-8 w-8 text-gray-400 mx-auto mb-2\" />\n                        <p className=\"text-sm text-gray-600\">\n                          {language === 'ar' ? 'اضغط لرفع الملفات' : 'Click to upload files'}\n                        </p>\n                        <p className=\"text-xs text-gray-500 mt-1\">\n                          {field.validation?.fileTypes?.join(', ')}\n                        </p>\n                      </div>\n                    </label>\n                    \n                    {/* عرض الملفات المرفوعة */}\n                    {files.length > 0 && (\n                      <div className=\"mt-3 space-y-2\">\n                        {files.map((file, index) => (\n                          <div key={index} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                            <div className=\"flex items-center\">\n                              <FileText className=\"h-4 w-4 text-gray-500 mr-2\" />\n                              <span className=\"text-sm\">{file.name}</span>\n                            </div>\n                            <Button\n                              type=\"button\"\n                              variant=\"ghost\"\n                              size=\"sm\"\n                              onClick={() => removeFile(index)}\n                            >\n                              <X className=\"h-4 w-4\" />\n                            </Button>\n                          </div>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                ) : (\n                  <input\n                    type={field.type}\n                    value={formData[field.id] || ''}\n                    onChange={(e) => handleInputChange(field.id, e.target.value)}\n                    placeholder={language === 'ar' ? field.placeholder_ar || field.placeholder : field.placeholder}\n                    className={cn(\n                      'w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent',\n                      errors[field.id] && 'border-red-500'\n                    )}\n                  />\n                )}\n                \n                {errors[field.id] && (\n                  <p className=\"text-red-500 text-sm mt-1 flex items-center\">\n                    <AlertCircle className=\"h-4 w-4 mr-1\" />\n                    {errors[field.id]}\n                  </p>\n                )}\n              </div>\n            ))}\n          </div>\n\n          {/* رسالة خطأ عامة */}\n          {submitStatus === 'error' && (\n            <div className=\"p-4 bg-red-50 border border-red-200 rounded-lg\">\n              <p className=\"text-red-700 flex items-center\">\n                <AlertCircle className=\"h-4 w-4 mr-2\" />\n                {language === 'ar' \n                  ? 'حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.'\n                  : 'An error occurred while submitting the request. Please try again.'\n                }\n              </p>\n            </div>\n          )}\n\n          {/* أزرار الإجراءات */}\n          <div className=\"flex justify-end space-x-4 pt-6 border-t\">\n            {onClose && (\n              <Button type=\"button\" variant=\"outline\" onClick={onClose}>\n                {language === 'ar' ? 'إلغاء' : 'Cancel'}\n              </Button>\n            )}\n            <Button \n              type=\"submit\" \n              disabled={isSubmitting}\n              className=\"flex items-center\"\n            >\n              {isSubmitting ? (\n                <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n              ) : (\n                <Send className=\"h-4 w-4 mr-2\" />\n              )}\n              {isSubmitting \n                ? (language === 'ar' ? 'جاري الإرسال...' : 'Submitting...')\n                : (language === 'ar' ? 'إرسال الطلب' : 'Submit Request')\n              }\n            </Button>\n          </div>\n        </form>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AAEA;AApBA;;;;;;;;;;;;AA4BO,SAAS,2BAA2B,EACzC,OAAO,EACP,OAAO,EACP,SAAS,EACuB;IAChC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD;IACpC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,gBAAa,AAAD;IAC9B,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IAE/E,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,iBAAiB;YACpB,OAAO,IAAI,CAAC;YACZ;QACF;IACF,GAAG;QAAC;QAAiB;KAAO;IAE5B,2BAA2B;IAC3B,MAAM,oBAAoB,CAAC,SAAiB;QAC1C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE;YACb,CAAC;QAED,0BAA0B;QAC1B,IAAI,MAAM,CAAC,QAAQ,EAAE;YACnB,UAAU,CAAA;gBACR,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,QAAQ;gBACzB,OAAO;YACT;QACF;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,MAAM,gBAAgB,MAAM,IAAI,CAAC,MAAM,MAAM,CAAC,KAAK,IAAI,EAAE;QACzD,SAAS,CAAA,OAAQ;mBAAI;mBAAS;aAAc;IAC9C;IAEA,YAAY;IACZ,MAAM,aAAa,CAAC;QAClB,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IAC/C;IAEA,wBAAwB;IACxB,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,QAAQ,UAAU,EAAE,QAAQ,CAAA;YAC1B,IAAI,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE;gBACzC,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,aAAa,OAC/B,oBACA;YACN;YAEA,kCAAkC;YAClC,IAAI,MAAM,EAAE,KAAK,oBAAoB,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE;gBACvD,MAAM,aAAa;gBACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG;oBACxC,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,aAAa,OAC/B,+BACA;gBACN;YACF;QACF;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,gBAAgB;IAChB,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QAEpB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,sBAAsB;YACtB,MAAM,eAAe;gBACnB,MAAM,SAAS,aAAa,IAAI;gBAChC,OAAO,SAAS,cAAc,IAAI;gBAClC,OAAO,SAAS,cAAc,IAAI;gBAClC,SAAS,SAAS,YAAY,IAAI;YACpC;YAEA,mBAAmB;YACnB,MAAM,UAAU,MAAM,CAAA,GAAA,yIAAA,CAAA,uBAAoB,AAAD,EAAE;gBACzC,WAAW,QAAQ,EAAE,CAAC,QAAQ;gBAC9B,aAAa,QAAQ,IAAI;gBACzB,gBAAgB,QAAQ,OAAO;gBAC/B,QAAQ,MAAM,MAAM;gBACpB;gBACA;gBACA,aAAa,EAAE,CAAC,6BAA6B;YAC/C;YAEA,IAAI,SAAS;gBACX,gBAAgB;gBAChB,YAAY,QAAQ,EAAE;gBAEtB,kCAAkC;gBAClC,WAAW;oBACT,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,QAAQ,EAAE,EAAE;gBACjD,GAAG;YACL,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,gBAAgB;QAClB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,kBAAkB;IAClB,IAAI,iBAAiB,WAAW;QAC9B,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,8OAAC;oBAAG,WAAU;8BACX,aAAa,OAAO,0BAA0B;;;;;;8BAEjD,8OAAC;oBAAE,WAAU;8BACV,aAAa,OACV,6CACA;;;;;;8BAGN,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAS,IAAM,OAAO,IAAI,CAAC;8BAChC,aAAa,OAAO,2BAA2B;;;;;;;;;;;;IAIxD;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BAEd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CACX,aAAa,OAAO,qBAAqB;;;;;;8CAE5C,8OAAC;oCAAE,WAAU;8CACV,aAAa,OACV,CAAC,sGAAsG,CAAC,GACxG,CAAC,0IAA0I,CAAC;;;;;;;;;;;;wBAInJ,yBACC,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,SAAS;sCACzC,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAMnB,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,UAAU,EAAE,IAAI,CAAC,sBACxB,8OAAC;oCAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,MAAM,IAAI,KAAK,cAAc;;sDAG/B,8OAAC;4CAAM,WAAU;;gDACd,aAAa,OAAO,MAAM,OAAO,IAAI,MAAM,IAAI,GAAG,MAAM,IAAI;gDAC5D,MAAM,QAAQ,kBAAI,8OAAC;oDAAK,WAAU;8DAAoB;;;;;;;;;;;;wCAGxD,MAAM,IAAI,KAAK,2BACd,8OAAC;4CACC,OAAO,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI;4CAC7B,UAAU,CAAC,IAAM,kBAAkB,MAAM,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;4CAC3D,aAAa,aAAa,OAAO,MAAM,cAAc,IAAI,MAAM,WAAW,GAAG,MAAM,WAAW;4CAC9F,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mGACA,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,kBACpB;4CAEF,MAAM;;;;;mDAEN,MAAM,IAAI,KAAK,uBACjB,8OAAC;;8DACC,8OAAC;oDACC,MAAK;oDACL,QAAQ;oDACR,QAAQ,MAAM,UAAU,EAAE,WAAW,KAAK;oDAC1C,UAAU;oDACV,WAAU;oDACV,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;;;;;;8DAExB,8OAAC;oDACC,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;oDAC3B,WAAU;8DAEV,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC;gEAAE,WAAU;0EACV,aAAa,OAAO,sBAAsB;;;;;;0EAE7C,8OAAC;gEAAE,WAAU;0EACV,MAAM,UAAU,EAAE,WAAW,KAAK;;;;;;;;;;;;;;;;;gDAMxC,MAAM,MAAM,GAAG,mBACd,8OAAC;oDAAI,WAAU;8DACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;4DAAgB,WAAU;;8EACzB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,8MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,8OAAC;4EAAK,WAAU;sFAAW,KAAK,IAAI;;;;;;;;;;;;8EAEtC,8OAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,WAAW;8EAE1B,cAAA,8OAAC,4LAAA,CAAA,IAAC;wEAAC,WAAU;;;;;;;;;;;;2DAXP;;;;;;;;;;;;;;;iEAmBlB,8OAAC;4CACC,MAAM,MAAM,IAAI;4CAChB,OAAO,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI;4CAC7B,UAAU,CAAC,IAAM,kBAAkB,MAAM,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;4CAC3D,aAAa,aAAa,OAAO,MAAM,cAAc,IAAI,MAAM,WAAW,GAAG,MAAM,WAAW;4CAC9F,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mGACA,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI;;;;;;wCAKzB,MAAM,CAAC,MAAM,EAAE,CAAC,kBACf,8OAAC;4CAAE,WAAU;;8DACX,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDACtB,MAAM,CAAC,MAAM,EAAE,CAAC;;;;;;;;mCArFhB,MAAM,EAAE;;;;;;;;;;wBA6FlB,iBAAiB,yBAChB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;kDACX,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,aAAa,OACV,uDACA;;;;;;;;;;;;sCAOV,8OAAC;4BAAI,WAAU;;gCACZ,yBACC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAQ;oCAAU,SAAS;8CAC9C,aAAa,OAAO,UAAU;;;;;;8CAGnC,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU;oCACV,WAAU;;wCAET,6BACC,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAEjB,eACI,aAAa,OAAO,oBAAoB,kBACxC,aAAa,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD", "debugId": null}}, {"offset": {"line": 1467, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/services/OptimizedBusinessServicesPage.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport {\n  ArrowRight,\n  ArrowLeft,\n  Package,\n  Search,\n  Warehouse,\n  Ship,\n  FileCheck,\n  Award,\n  TrendingUp,\n  Truck,\n  CheckCircle,\n  Clock,\n  Users,\n  Star,\n  Phone,\n  Mail,\n  Globe,\n  Shield,\n  Zap,\n  Target,\n  BarChart3,\n  Headphones,\n  ThumbsUp,\n  Building2,\n  MapPin\n} from 'lucide-react';\nimport { Button } from '../ui/Button';\nimport { Card } from '../ui/Card';\nimport { Breadcrumb } from '../ui/Breadcrumb';\nimport { useLanguageStore } from '../../stores/languageStore';\nimport { useBreadcrumbs } from '../../hooks/useBreadcrumbs';\nimport { Service, ServiceCategory } from '../../types';\nimport { \n  unifiedServices, \n  unifiedServiceCategories,\n  getUnifiedServicesByCategory,\n  getFeaturedUnifiedServices \n} from '../../data/unifiedServices';\nimport { BusinessServiceRequestForm } from './BusinessServiceRequestForm';\n\n// Enhanced service icons mapping\nconst serviceIcons = {\n  Package,\n  Search,\n  Warehouse,\n  Ship,\n  FileCheck,\n  Award,\n  TrendingUp,\n  Truck,\n  Target,\n  BarChart3,\n  Building2\n};\n\nexport function OptimizedBusinessServicesPage() {\n  const { language } = useLanguageStore();\n  const router = useRouter();\n\n  const [services] = useState<Service[]>(unifiedServices);\n  const [categories] = useState<ServiceCategory[]>(unifiedServiceCategories);\n  const [selectedService, setSelectedService] = useState<Service | null>(null);\n  const [showRequestForm, setShowRequestForm] = useState(false);\n  const [activeCategory, setActiveCategory] = useState<string>('');\n  const [loading] = useState(false);\n\n  // Generate breadcrumbs for services page\n  const breadcrumbItems = useBreadcrumbs({\n    language: language\n  });\n\n  // Handle service request\n  const handleRequestService = (service: Service) => {\n    setSelectedService(service);\n    setShowRequestForm(true);\n  };\n\n  // Handle request success\n  const handleRequestSuccess = () => {\n    setShowRequestForm(false);\n    setSelectedService(null);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-950 dark:to-blue-950\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-primary-500 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-slate-400\">\n            {language === 'ar' ? 'جاري تحميل الخدمات...' : 'Loading services...'}\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-slate-950 dark:via-slate-900 dark:to-blue-950\">\n      {/* Enhanced Breadcrumb Navigation */}\n      <Breadcrumb items={breadcrumbItems} />\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Services Overview Section */}\n        <div id=\"services-overview\" className=\"mb-20\">\n          <div className=\"text-center mb-16\">\n            <div className=\"inline-flex items-center px-4 py-2 bg-primary-100 dark:bg-primary-900 rounded-full text-sm font-medium text-primary-600 dark:text-primary-400 mb-6\">\n              <Target className=\"h-4 w-4 mr-2\" />\n              {language === 'ar' ? 'خدماتنا الأساسية' : 'Our Core Services'}\n            </div>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 dark:text-slate-100 mb-6\">\n              {language === 'ar' ? 'خدمات مصممة لنجاحك' : 'Services Designed for Your Success'}\n            </h2>\n            <p className=\"text-xl text-gray-600 dark:text-slate-300 max-w-4xl mx-auto leading-relaxed\">\n              {language === 'ar'\n                ? 'نقدم مجموعة شاملة من الخدمات التجارية المتخصصة لدعم كل جانب من جوانب عملياتك التجارية'\n                : 'We offer a comprehensive suite of specialized business services to support every aspect of your commercial operations'\n              }\n            </p>\n          </div>\n\n          {/* Enhanced Services Grid - Direct Service Access */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {services.slice(0, 9).map((service) => {\n              const IconComponent = serviceIcons[service.icon as keyof typeof serviceIcons] || Package;\n\n              return (\n                <Card\n                  key={service.id}\n                  className=\"group p-6 hover:shadow-2xl transition-all duration-500 border-0 bg-gradient-to-br from-white to-gray-50 dark:from-slate-800 dark:to-slate-700 hover:from-primary-50 hover:to-blue-50 dark:hover:from-primary-950 dark:hover:to-blue-950 cursor-pointer\"\n                  onClick={() => router.push(`/${language}/services/${service.slug}`)}\n                >\n                  <div className=\"flex items-start mb-4\">\n                    <div className=\"flex items-center justify-center w-12 h-12 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900 dark:to-primary-800 rounded-xl mr-4 group-hover:from-primary-200 group-hover:to-primary-300 dark:group-hover:from-primary-800 dark:group-hover:to-primary-700 transition-all duration-300\">\n                      <IconComponent className=\"h-6 w-6 text-primary-600 dark:text-primary-400\" />\n                    </div>\n                    <div className=\"flex-1\">\n                      <h3 className=\"text-lg font-bold text-gray-900 dark:text-slate-100 mb-2 group-hover:text-primary-700 dark:group-hover:text-primary-400 transition-colors\">\n                        {language === 'ar' ? service.name_ar || service.name : service.name}\n                      </h3>\n                      <p className=\"text-sm text-gray-600 dark:text-slate-400 leading-relaxed line-clamp-2\">\n                        {language === 'ar' ? service.description_ar || service.description : service.description}\n                      </p>\n                    </div>\n                  </div>\n\n                  {/* Key Features Preview */}\n                  <div className=\"mb-4\">\n                    <div className=\"flex flex-wrap gap-1\">\n                      {(language === 'ar' ? service.features_ar || service.features : service.features)\n                        ?.slice(0, 3)\n                        .map((feature, index) => (\n                        <span key={index} className=\"inline-flex items-center px-2 py-1 bg-primary-50 dark:bg-primary-900 text-primary-700 dark:text-primary-300 text-xs rounded-full\">\n                          {feature.length > 20 ? `${feature.substring(0, 20)}...` : feature}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* Action Buttons */}\n                  <div className=\"flex gap-2\">\n                    <Button\n                      size=\"sm\"\n                      className=\"flex-1 group-hover:bg-primary-600 group-hover:shadow-lg transition-all duration-300\"\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        handleRequestService(service);\n                      }}\n                    >\n                      {language === 'ar' ? 'طلب عرض سعر' : 'Get Quote'}\n                    </Button>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      className=\"px-4 group-hover:border-primary-300 group-hover:text-primary-600 transition-all duration-300\"\n                      onClick={() => router.push(`/${language}/services/${service.slug}`)}\n                    >\n                      {language === 'ar' ? 'التفاصيل' : 'Details'}\n                    </Button>\n                  </div>\n                </Card>\n              );\n            })}\n          </div>\n\n          {/* View All Services Button */}\n          <div className=\"text-center mt-12\">\n            <Button\n              size=\"lg\"\n              variant=\"outline\"\n              className=\"px-8 py-4 text-lg font-semibold border-2 border-primary-200 text-primary-600 hover:bg-primary-50 hover:border-primary-300\"\n              onClick={() => document.getElementById('all-services')?.scrollIntoView({ behavior: 'smooth' })}\n            >\n              {language === 'ar' ? 'عرض جميع الخدمات' : 'View All Services'}\n              {language === 'ar' ? <ArrowLeft className=\"ml-2 h-5 w-5\" /> : <ArrowRight className=\"ml-2 h-5 w-5\" />}\n            </Button>\n          </div>\n        </div>\n\n        {/* All Services Section */}\n        <div id=\"all-services\" className=\"scroll-mt-20\">\n          <div className=\"text-center mb-16\">\n            <div className=\"inline-flex items-center px-4 py-2 bg-blue-100 rounded-full text-sm font-medium text-blue-600 mb-6\">\n              <Building2 className=\"h-4 w-4 mr-2\" />\n              {language === 'ar' ? 'جميع خدماتنا' : 'All Our Services'}\n            </div>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              {language === 'ar' ? 'استكشف مجموعة خدماتنا الكاملة' : 'Explore Our Complete Service Portfolio'}\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed\">\n              {language === 'ar'\n                ? 'خدمات متخصصة ومتكاملة مصممة لدعم كل جانب من جوانب عملياتك التجارية'\n                : 'Specialized and integrated services designed to support every aspect of your business operations'\n              }\n            </p>\n          </div>\n\n          {/* Services by Category */}\n          <div className=\"space-y-20\">\n            {categories.map((category) => {\n              const categoryServices = getUnifiedServicesByCategory(category.id);\n\n              if (categoryServices.length === 0) return null;\n\n              return (\n                <div key={category.id} id={`category-${category.id}`} className=\"scroll-mt-20\">\n                  {/* Category Header */}\n                  <div className=\"flex items-center mb-12\">\n                    <div className=\"flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl mr-6\">\n                      {React.createElement(serviceIcons[category.icon as keyof typeof serviceIcons] || Package, { className: 'h-8 w-8 text-primary-600' })}\n                    </div>\n                    <div>\n                      <h3 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                        {language === 'ar' ? category.name_ar || category.name : category.name}\n                      </h3>\n                      <p className=\"text-lg text-gray-600 max-w-2xl\">\n                        {language === 'ar' ? category.description_ar || category.description : category.description}\n                      </p>\n                    </div>\n                  </div>\n\n                  {/* Services Grid */}\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\">\n                    {categoryServices.map((service) => {\n                      const IconComponent = serviceIcons[service.icon as keyof typeof serviceIcons] || Package;\n\n                      return (\n                        <Card key={service.id} className=\"group p-6 hover:shadow-xl transition-all duration-300 border-0 bg-white hover:bg-gradient-to-br hover:from-primary-50 hover:to-blue-50 cursor-pointer\"\n                          onClick={() => router.push(`/${language}/services/${service.slug}`)}\n                        >\n                          {/* Service Header */}\n                          <div className=\"flex items-start mb-4\">\n                            <div className=\"flex items-center justify-center w-12 h-12 bg-gradient-to-br from-primary-100 to-primary-200 rounded-xl mr-4 group-hover:from-primary-200 group-hover:to-primary-300 transition-all duration-300\">\n                              <IconComponent className=\"h-6 w-6 text-primary-600\" />\n                            </div>\n                            <div className=\"flex-1\">\n                              <h4 className=\"text-lg font-bold text-gray-900 mb-2 group-hover:text-primary-700 transition-colors\">\n                                {language === 'ar' ? service.name_ar || service.name : service.name}\n                              </h4>\n                              <p className=\"text-sm text-gray-600 leading-relaxed line-clamp-2\">\n                                {language === 'ar' ? service.description_ar || service.description : service.description}\n                              </p>\n                            </div>\n                          </div>\n\n                          {/* Key Features Preview */}\n                          <div className=\"mb-4\">\n                            <div className=\"flex flex-wrap gap-1\">\n                              {(language === 'ar' ? service.features_ar || service.features : service.features)\n                                ?.slice(0, 3)\n                                .map((feature, index) => (\n                                <span key={index} className=\"inline-flex items-center px-2 py-1 bg-primary-50 text-primary-700 text-xs rounded-full\">\n                                  {feature.length > 15 ? `${feature.substring(0, 15)}...` : feature}\n                                </span>\n                              ))}\n                            </div>\n                          </div>\n\n                          {/* Quick Info */}\n                          <div className=\"flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg\">\n                            <div className=\"flex items-center text-yellow-600\">\n                              <Star className=\"h-4 w-4 mr-1\" />\n                              <span className=\"text-sm font-semibold\">4.9/5</span>\n                            </div>\n                            <span className=\"text-sm text-primary-600 font-medium\">\n                              {language === 'ar' ? 'عرض سعر مخصص' : 'Custom Quote'}\n                            </span>\n                          </div>\n\n                          {/* Action Buttons */}\n                          <div className=\"flex gap-2\">\n                            <Button\n                              size=\"sm\"\n                              className=\"flex-1 group-hover:bg-primary-600 group-hover:shadow-lg transition-all duration-300\"\n                              onClick={(e) => {\n                                e.stopPropagation();\n                                handleRequestService(service);\n                              }}\n                            >\n                              {language === 'ar' ? 'طلب عرض سعر' : 'Get Quote'}\n                            </Button>\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              className=\"px-4 group-hover:border-primary-300 group-hover:text-primary-600 transition-all duration-300\"\n                              onClick={() => router.push(`/${language}/services/${service.slug}`)}\n                            >\n                              {language === 'ar' ? 'التفاصيل' : 'Details'}\n                            </Button>\n                          </div>\n                        </Card>\n                      );\n                    })}\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Compact Why Choose Us Section */}\n        <div className=\"mt-16 mb-16\">\n          <div className=\"text-center mb-12\">\n            <div className=\"inline-flex items-center px-4 py-2 bg-green-100 rounded-full text-sm font-medium text-green-600 mb-4\">\n              <ThumbsUp className=\"h-4 w-4 mr-2\" />\n              {language === 'ar' ? 'لماذا تختارنا' : 'Why Choose Us'}\n            </div>\n            <h2 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4\">\n              {language === 'ar' ? 'شريكك الموثوق في النجاح' : 'Your Trusted Partner in Success'}\n            </h2>\n            <p className=\"text-gray-600 max-w-3xl mx-auto\">\n              {language === 'ar'\n                ? 'نتميز بخبرتنا الواسعة وخدماتنا المتكاملة التي تضمن نجاح أعمالك'\n                : 'We stand out with our extensive expertise and integrated services that ensure your business success'\n              }\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-6\">\n            <div className=\"text-center p-4\">\n              <div className=\"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mx-auto mb-3\">\n                <Clock className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <h3 className=\"text-sm font-semibold text-gray-900 mb-2\">\n                {language === 'ar' ? 'استجابة سريعة' : 'Quick Response'}\n              </h3>\n              <p className=\"text-gray-600 text-xs\">\n                {language === 'ar' ? 'رد خلال ساعة واحدة' : 'Response within 1 hour'}\n              </p>\n            </div>\n            <div className=\"text-center p-4\">\n              <div className=\"flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mx-auto mb-3\">\n                <Shield className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <h3 className=\"text-sm font-semibold text-gray-900 mb-2\">\n                {language === 'ar' ? 'ضمان الجودة' : 'Quality Guarantee'}\n              </h3>\n              <p className=\"text-gray-600 text-xs\">\n                {language === 'ar' ? 'ضمان جودة 100%' : '100% quality guarantee'}\n              </p>\n            </div>\n            <div className=\"text-center p-4\">\n              <div className=\"flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mx-auto mb-3\">\n                <Users className=\"h-6 w-6 text-purple-600\" />\n              </div>\n              <h3 className=\"text-sm font-semibold text-gray-900 mb-2\">\n                {language === 'ar' ? 'فريق خبراء' : 'Expert Team'}\n              </h3>\n              <p className=\"text-gray-600 text-xs\">\n                {language === 'ar' ? 'خبراء متخصصين 24/7' : 'Specialized experts 24/7'}\n              </p>\n            </div>\n            <div className=\"text-center p-4\">\n              <div className=\"flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-full mx-auto mb-3\">\n                <Globe className=\"h-6 w-6 text-yellow-600\" />\n              </div>\n              <h3 className=\"text-sm font-semibold text-gray-900 mb-2\">\n                {language === 'ar' ? 'تغطية عالمية' : 'Global Coverage'}\n              </h3>\n              <p className=\"text-gray-600 text-xs\">\n                {language === 'ar' ? 'خدمات في 45+ دولة' : 'Services in 45+ countries'}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Enhanced Call-to-Action Section */}\n        <div className=\"mt-20\">\n          <div className=\"relative overflow-hidden bg-gradient-to-br from-primary-600 via-primary-700 to-secondary-600 rounded-3xl\">\n            <div className=\"absolute inset-0 bg-black/10\"></div>\n            <div className=\"relative px-8 py-16 text-center text-white\">\n              <div className=\"max-w-4xl mx-auto\">\n                <div className=\"inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-sm font-medium mb-6\">\n                  <Star className=\"h-4 w-4 mr-2\" />\n                  {language === 'ar' ? 'خدمة عملاء متميزة' : 'Premium Customer Service'}\n                </div>\n\n                <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\n                  {language === 'ar' ? 'ابدأ رحلة نجاحك معنا اليوم' : 'Start Your Success Journey with Us Today'}\n                </h2>\n\n                <p className=\"text-xl md:text-2xl text-primary-100 mb-8 leading-relaxed\">\n                  {language === 'ar'\n                    ? 'احصل على استشارة مجانية وعرض سعر مخصص لاحتياجاتك التجارية. فريقنا من الخبراء جاهز لمساعدتك في تحقيق أهدافك'\n                    : 'Get a free consultation and custom quote for your business needs. Our team of experts is ready to help you achieve your goals'\n                  }\n                </p>\n\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-8\">\n                  <Button\n                    variant=\"secondary\"\n                    size=\"lg\"\n                    className=\"bg-white text-primary-600 hover:bg-primary-50 px-8 py-4 text-lg font-semibold\"\n                    onClick={() => router.push(`/${language}/contact`)}\n                  >\n                    <Mail className=\"mr-2 h-5 w-5\" />\n                    {language === 'ar' ? 'احصل على استشارة مجانية' : 'Get Free Consultation'}\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    size=\"lg\"\n                    className=\"border-white text-white hover:bg-white hover:text-primary-600 px-8 py-4 text-lg font-semibold\"\n                    onClick={() => {\n                      window.open('tel:+966123456789', '_self');\n                    }}\n                  >\n                    <Phone className=\"mr-2 h-5 w-5\" />\n                    {language === 'ar' ? 'اتصل بنا الآن' : 'Call Us Now'}\n                  </Button>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 text-center\">\n                  <div>\n                    <Clock className=\"h-8 w-8 mx-auto mb-2 text-primary-200\" />\n                    <p className=\"text-sm font-medium\">\n                      {language === 'ar' ? 'استجابة فورية' : 'Instant Response'}\n                    </p>\n                    <p className=\"text-xs text-primary-100\">\n                      {language === 'ar' ? 'خلال دقائق' : 'Within minutes'}\n                    </p>\n                  </div>\n                  <div>\n                    <Users className=\"h-8 w-8 mx-auto mb-2 text-primary-200\" />\n                    <p className=\"text-sm font-medium\">\n                      {language === 'ar' ? 'خبراء متخصصون' : 'Specialized Experts'}\n                    </p>\n                    <p className=\"text-xs text-primary-100\">\n                      {language === 'ar' ? 'في خدمتك دائماً' : 'Always at your service'}\n                    </p>\n                  </div>\n                  <div>\n                    <Shield className=\"h-8 w-8 mx-auto mb-2 text-primary-200\" />\n                    <p className=\"text-sm font-medium\">\n                      {language === 'ar' ? 'ضمان شامل' : 'Complete Guarantee'}\n                    </p>\n                    <p className=\"text-xs text-primary-100\">\n                      {language === 'ar' ? 'رضاك هو هدفنا' : 'Your satisfaction is our goal'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Decorative Elements */}\n            <div className=\"absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32\"></div>\n            <div className=\"absolute bottom-0 left-0 w-64 h-64 bg-white/5 rounded-full translate-y-32 -translate-x-32\"></div>\n          </div>\n        </div>\n      </div>\n\n      {/* Request Form Modal */}\n      {showRequestForm && selectedService && (\n        <BusinessServiceRequestForm\n          service={selectedService}\n          onClose={() => setShowRequestForm(false)}\n          onSuccess={handleRequestSuccess}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2BA;AACA;AACA;AACA;AACA;AAEA;AAMA;AA3CA;;;;;;;;;;;;AA6CA,iCAAiC;AACjC,MAAM,eAAe;IACnB,SAAA,wMAAA,CAAA,UAAO;IACP,QAAA,sMAAA,CAAA,SAAM;IACN,WAAA,4MAAA,CAAA,YAAS;IACT,MAAA,kMAAA,CAAA,OAAI;IACJ,WAAA,gNAAA,CAAA,YAAS;IACT,OAAA,oMAAA,CAAA,QAAK;IACL,YAAA,kNAAA,CAAA,aAAU;IACV,OAAA,oMAAA,CAAA,QAAK;IACL,QAAA,sMAAA,CAAA,SAAM;IACN,WAAA,oNAAA,CAAA,YAAS;IACT,WAAA,gNAAA,CAAA,YAAS;AACX;AAEO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD;IACpC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,8HAAA,CAAA,kBAAe;IACtD,MAAM,CAAC,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,8HAAA,CAAA,2BAAwB;IACzE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3B,yCAAyC;IACzC,MAAM,kBAAkB,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE;QACrC,UAAU;IACZ;IAEA,yBAAyB;IACzB,MAAM,uBAAuB,CAAC;QAC5B,mBAAmB;QACnB,mBAAmB;IACrB;IAEA,yBAAyB;IACzB,MAAM,uBAAuB;QAC3B,mBAAmB;QACnB,mBAAmB;IACrB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCACV,aAAa,OAAO,0BAA0B;;;;;;;;;;;;;;;;;IAKzD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,sIAAA,CAAA,aAAU;gBAAC,OAAO;;;;;;0BAEnB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,IAAG;wBAAoB,WAAU;;0CACpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CACjB,aAAa,OAAO,qBAAqB;;;;;;;kDAE5C,8OAAC;wCAAG,WAAU;kDACX,aAAa,OAAO,uBAAuB;;;;;;kDAE9C,8OAAC;wCAAE,WAAU;kDACV,aAAa,OACV,0FACA;;;;;;;;;;;;0CAMR,8OAAC;gCAAI,WAAU;0CACZ,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;oCACzB,MAAM,gBAAgB,YAAY,CAAC,QAAQ,IAAI,CAA8B,IAAI,wMAAA,CAAA,UAAO;oCAExF,qBACE,8OAAC,gIAAA,CAAA,OAAI;wCAEH,WAAU;wCACV,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,QAAQ,IAAI,EAAE;;0DAElE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAc,WAAU;;;;;;;;;;;kEAE3B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EACX,aAAa,OAAO,QAAQ,OAAO,IAAI,QAAQ,IAAI,GAAG,QAAQ,IAAI;;;;;;0EAErE,8OAAC;gEAAE,WAAU;0EACV,aAAa,OAAO,QAAQ,cAAc,IAAI,QAAQ,WAAW,GAAG,QAAQ,WAAW;;;;;;;;;;;;;;;;;;0DAM9F,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ,CAAC,aAAa,OAAO,QAAQ,WAAW,IAAI,QAAQ,QAAQ,GAAG,QAAQ,QAAQ,GAC5E,MAAM,GAAG,GACV,IAAI,CAAC,SAAS,sBACf,8OAAC;4DAAiB,WAAU;sEACzB,QAAQ,MAAM,GAAG,KAAK,GAAG,QAAQ,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;2DADjD;;;;;;;;;;;;;;;0DAQjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,WAAU;wDACV,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,qBAAqB;wDACvB;kEAEC,aAAa,OAAO,gBAAgB;;;;;;kEAEvC,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,QAAQ,IAAI,EAAE;kEAEjE,aAAa,OAAO,aAAa;;;;;;;;;;;;;uCAjDjC,QAAQ,EAAE;;;;;gCAsDrB;;;;;;0CAIF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,SAAS,cAAc,CAAC,iBAAiB,eAAe;4CAAE,UAAU;wCAAS;;wCAE3F,aAAa,OAAO,qBAAqB;wCACzC,aAAa,qBAAO,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;iEAAoB,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM1F,8OAAC;wBAAI,IAAG;wBAAe,WAAU;;0CAC/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CACpB,aAAa,OAAO,iBAAiB;;;;;;;kDAExC,8OAAC;wCAAG,WAAU;kDACX,aAAa,OAAO,kCAAkC;;;;;;kDAEzD,8OAAC;wCAAE,WAAU;kDACV,aAAa,OACV,uEACA;;;;;;;;;;;;0CAMR,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,mBAAmB,CAAA,GAAA,8HAAA,CAAA,+BAA4B,AAAD,EAAE,SAAS,EAAE;oCAEjE,IAAI,iBAAiB,MAAM,KAAK,GAAG,OAAO;oCAE1C,qBACE,8OAAC;wCAAsB,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE;wCAAE,WAAU;;0DAE9D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,cAAA,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,YAAY,CAAC,SAAS,IAAI,CAA8B,IAAI,wMAAA,CAAA,UAAO,EAAE;4DAAE,WAAW;wDAA2B;;;;;;kEAEpI,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EACX,aAAa,OAAO,SAAS,OAAO,IAAI,SAAS,IAAI,GAAG,SAAS,IAAI;;;;;;0EAExE,8OAAC;gEAAE,WAAU;0EACV,aAAa,OAAO,SAAS,cAAc,IAAI,SAAS,WAAW,GAAG,SAAS,WAAW;;;;;;;;;;;;;;;;;;0DAMjG,8OAAC;gDAAI,WAAU;0DACZ,iBAAiB,GAAG,CAAC,CAAC;oDACrB,MAAM,gBAAgB,YAAY,CAAC,QAAQ,IAAI,CAA8B,IAAI,wMAAA,CAAA,UAAO;oDAExF,qBACE,8OAAC,gIAAA,CAAA,OAAI;wDAAkB,WAAU;wDAC/B,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,QAAQ,IAAI,EAAE;;0EAGlE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAc,WAAU;;;;;;;;;;;kFAE3B,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAG,WAAU;0FACX,aAAa,OAAO,QAAQ,OAAO,IAAI,QAAQ,IAAI,GAAG,QAAQ,IAAI;;;;;;0FAErE,8OAAC;gFAAE,WAAU;0FACV,aAAa,OAAO,QAAQ,cAAc,IAAI,QAAQ,WAAW,GAAG,QAAQ,WAAW;;;;;;;;;;;;;;;;;;0EAM9F,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;8EACZ,CAAC,aAAa,OAAO,QAAQ,WAAW,IAAI,QAAQ,QAAQ,GAAG,QAAQ,QAAQ,GAC5E,MAAM,GAAG,GACV,IAAI,CAAC,SAAS,sBACf,8OAAC;4EAAiB,WAAU;sFACzB,QAAQ,MAAM,GAAG,KAAK,GAAG,QAAQ,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;2EADjD;;;;;;;;;;;;;;;0EAQjB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,kMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,8OAAC;gFAAK,WAAU;0FAAwB;;;;;;;;;;;;kFAE1C,8OAAC;wEAAK,WAAU;kFACb,aAAa,OAAO,iBAAiB;;;;;;;;;;;;0EAK1C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kIAAA,CAAA,SAAM;wEACL,MAAK;wEACL,WAAU;wEACV,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,qBAAqB;wEACvB;kFAEC,aAAa,OAAO,gBAAgB;;;;;;kFAEvC,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,QAAQ,IAAI,EAAE;kFAEjE,aAAa,OAAO,aAAa;;;;;;;;;;;;;uDA5D7B,QAAQ,EAAE;;;;;gDAiEzB;;;;;;;uCAvFM,SAAS,EAAE;;;;;gCA2FzB;;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACnB,aAAa,OAAO,kBAAkB;;;;;;;kDAEzC,8OAAC;wCAAG,WAAU;kDACX,aAAa,OAAO,4BAA4B;;;;;;kDAEnD,8OAAC;wCAAE,WAAU;kDACV,aAAa,OACV,mEACA;;;;;;;;;;;;0CAKR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAG,WAAU;0DACX,aAAa,OAAO,kBAAkB;;;;;;0DAEzC,8OAAC;gDAAE,WAAU;0DACV,aAAa,OAAO,uBAAuB;;;;;;;;;;;;kDAGhD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,8OAAC;gDAAG,WAAU;0DACX,aAAa,OAAO,gBAAgB;;;;;;0DAEvC,8OAAC;gDAAE,WAAU;0DACV,aAAa,OAAO,mBAAmB;;;;;;;;;;;;kDAG5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAG,WAAU;0DACX,aAAa,OAAO,eAAe;;;;;;0DAEtC,8OAAC;gDAAE,WAAU;0DACV,aAAa,OAAO,uBAAuB;;;;;;;;;;;;kDAGhD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAG,WAAU;0DACX,aAAa,OAAO,iBAAiB;;;;;;0DAExC,8OAAC;gDAAE,WAAU;0DACV,aAAa,OAAO,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;kCAOnD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACf,aAAa,OAAO,sBAAsB;;;;;;;0DAG7C,8OAAC;gDAAG,WAAU;0DACX,aAAa,OAAO,+BAA+B;;;;;;0DAGtD,8OAAC;gDAAE,WAAU;0DACV,aAAa,OACV,+GACA;;;;;;0DAIN,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,QAAQ,CAAC;;0EAEjD,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DACf,aAAa,OAAO,4BAA4B;;;;;;;kEAEnD,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,SAAS;4DACP,OAAO,IAAI,CAAC,qBAAqB;wDACnC;;0EAEA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,aAAa,OAAO,kBAAkB;;;;;;;;;;;;;0DAI3C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;gEAAE,WAAU;0EACV,aAAa,OAAO,kBAAkB;;;;;;0EAEzC,8OAAC;gEAAE,WAAU;0EACV,aAAa,OAAO,eAAe;;;;;;;;;;;;kEAGxC,8OAAC;;0EACC,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;gEAAE,WAAU;0EACV,aAAa,OAAO,kBAAkB;;;;;;0EAEzC,8OAAC;gEAAE,WAAU;0EACV,aAAa,OAAO,oBAAoB;;;;;;;;;;;;kEAG7C,8OAAC;;0EACC,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC;gEAAE,WAAU;0EACV,aAAa,OAAO,cAAc;;;;;;0EAErC,8OAAC;gEAAE,WAAU;0EACV,aAAa,OAAO,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQjD,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAMpB,mBAAmB,iCAClB,8OAAC,4JAAA,CAAA,6BAA0B;gBACzB,SAAS;gBACT,SAAS,IAAM,mBAAmB;gBAClC,WAAW;;;;;;;;;;;;AAKrB", "debugId": null}}, {"offset": {"line": 2569, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/app/%5Blocale%5D/services/page.tsx"], "sourcesContent": ["'use client';\n\nimport { Suspense } from 'react';\nimport { OptimizedBusinessServicesPage } from '../../../components/services/OptimizedBusinessServicesPage';\n\n// Loading fallback component\nconst LoadingFallback = () => (\n  <div className=\"min-h-screen flex items-center justify-center\">\n    <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500\"></div>\n  </div>\n);\n\nexport default function Services() {\n  return (\n    <Suspense fallback={<LoadingFallback />}>\n      <OptimizedBusinessServicesPage />\n    </Suspense>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,6BAA6B;AAC7B,MAAM,kBAAkB,kBACtB,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;;;;;;;;;;AAIJ,SAAS;IACtB,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBAAU,8OAAC;;;;;kBACnB,cAAA,8OAAC,+JAAA,CAAA,gCAA6B;;;;;;;;;;AAGpC", "debugId": null}}]}