(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__ebb3844e._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/lib/security/headers.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Security Headers Configuration
 * Implements comprehensive security headers for production deployment
 */ __turbopack_context__.s({
    "applyCorsHeaders": (()=>applyCorsHeaders),
    "applyRateLimitHeaders": (()=>applyRateLimitHeaders),
    "applySecurityHeaders": (()=>applySecurityHeaders),
    "auditSecurityHeaders": (()=>auditSecurityHeaders),
    "getContentSecurityPolicy": (()=>getContentSecurityPolicy),
    "getPermissionsPolicy": (()=>getPermissionsPolicy),
    "securityHeadersMiddleware": (()=>securityHeadersMiddleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
;
const defaultConfig = {
    contentSecurityPolicy: true,
    strictTransportSecurity: true,
    xFrameOptions: true,
    xContentTypeOptions: true,
    referrerPolicy: true,
    permissionsPolicy: true,
    crossOriginEmbedderPolicy: false,
    crossOriginOpenerPolicy: true,
    crossOriginResourcePolicy: true
};
function getContentSecurityPolicy(isDevelopment = false) {
    const policies = {
        'default-src': [
            "'self'"
        ],
        'script-src': [
            "'self'",
            "'unsafe-inline'",
            "'unsafe-eval'",
            'https://js.stripe.com',
            'https://checkout.stripe.com',
            'https://www.google-analytics.com',
            'https://www.googletagmanager.com',
            'https://connect.facebook.net',
            'https://www.facebook.com',
            ...isDevelopment ? [
                "'unsafe-inline'",
                "'unsafe-eval'"
            ] : []
        ],
        'style-src': [
            "'self'",
            "'unsafe-inline'",
            'https://fonts.googleapis.com'
        ],
        'img-src': [
            "'self'",
            'data:',
            'blob:',
            'https:',
            'https://images.unsplash.com',
            'https://via.placeholder.com',
            'https://www.google-analytics.com',
            'https://www.facebook.com'
        ],
        'font-src': [
            "'self'",
            'https://fonts.gstatic.com',
            'data:'
        ],
        'connect-src': [
            "'self'",
            'https://api.stripe.com',
            'https://checkout.stripe.com',
            'https://www.google-analytics.com',
            'https://analytics.google.com',
            'https://www.facebook.com',
            'https://connect.facebook.net',
            'wss:',
            ...isDevelopment ? [
                'ws:',
                'wss:'
            ] : []
        ],
        'frame-src': [
            "'self'",
            'https://js.stripe.com',
            'https://checkout.stripe.com',
            'https://www.facebook.com',
            'https://www.youtube.com'
        ],
        'worker-src': [
            "'self'",
            'blob:'
        ],
        'child-src': [
            "'self'",
            'blob:'
        ],
        'object-src': [
            "'none'"
        ],
        'base-uri': [
            "'self'"
        ],
        'form-action': [
            "'self'"
        ],
        'frame-ancestors': [
            "'none'"
        ],
        'upgrade-insecure-requests': isDevelopment ? [] : [
            ''
        ]
    };
    return Object.entries(policies).filter(([_, values])=>values.length > 0).map(([key, values])=>`${key} ${values.join(' ')}`).join('; ');
}
function getPermissionsPolicy() {
    const policies = {
        'accelerometer': [
            '()'
        ],
        'ambient-light-sensor': [
            '()'
        ],
        'autoplay': [
            '()'
        ],
        'battery': [
            '()'
        ],
        'camera': [
            '()'
        ],
        'cross-origin-isolated': [
            '()'
        ],
        'display-capture': [
            '()'
        ],
        'document-domain': [
            '()'
        ],
        'encrypted-media': [
            '()'
        ],
        'execution-while-not-rendered': [
            '()'
        ],
        'execution-while-out-of-viewport': [
            '()'
        ],
        'fullscreen': [
            '(self)'
        ],
        'geolocation': [
            '()'
        ],
        'gyroscope': [
            '()'
        ],
        'keyboard-map': [
            '()'
        ],
        'magnetometer': [
            '()'
        ],
        'microphone': [
            '()'
        ],
        'midi': [
            '()'
        ],
        'navigation-override': [
            '()'
        ],
        'payment': [
            '(self)'
        ],
        'picture-in-picture': [
            '()'
        ],
        'publickey-credentials-get': [
            '()'
        ],
        'screen-wake-lock': [
            '()'
        ],
        'sync-xhr': [
            '()'
        ],
        'usb': [
            '()'
        ],
        'web-share': [
            '(self)'
        ],
        'xr-spatial-tracking': [
            '()'
        ]
    };
    return Object.entries(policies).map(([key, values])=>`${key}=${values.join(' ')}`).join(', ');
}
function applySecurityHeaders(response, config = defaultConfig, isDevelopment = false) {
    // Content Security Policy
    if (config.contentSecurityPolicy) {
        response.headers.set('Content-Security-Policy', getContentSecurityPolicy(isDevelopment));
    }
    // Strict Transport Security (HTTPS only)
    if (config.strictTransportSecurity && !isDevelopment) {
        response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
    }
    // X-Frame-Options
    if (config.xFrameOptions) {
        response.headers.set('X-Frame-Options', 'DENY');
    }
    // X-Content-Type-Options
    if (config.xContentTypeOptions) {
        response.headers.set('X-Content-Type-Options', 'nosniff');
    }
    // Referrer Policy
    if (config.referrerPolicy) {
        response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    }
    // Permissions Policy
    if (config.permissionsPolicy) {
        response.headers.set('Permissions-Policy', getPermissionsPolicy());
    }
    // Cross-Origin Embedder Policy
    if (config.crossOriginEmbedderPolicy) {
        response.headers.set('Cross-Origin-Embedder-Policy', 'require-corp');
    }
    // Cross-Origin Opener Policy
    if (config.crossOriginOpenerPolicy) {
        response.headers.set('Cross-Origin-Opener-Policy', 'same-origin');
    }
    // Cross-Origin Resource Policy
    if (config.crossOriginResourcePolicy) {
        response.headers.set('Cross-Origin-Resource-Policy', 'same-origin');
    }
    // Additional security headers
    response.headers.set('X-DNS-Prefetch-Control', 'off');
    response.headers.set('X-Download-Options', 'noopen');
    response.headers.set('X-Permitted-Cross-Domain-Policies', 'none');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    // Remove potentially sensitive headers
    response.headers.delete('X-Powered-By');
    response.headers.delete('Server');
    return response;
}
function securityHeadersMiddleware(request, config) {
    const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    const isDevelopment = ("TURBOPACK compile-time value", "development") === 'development';
    return applySecurityHeaders(response, config, isDevelopment);
}
function applyRateLimitHeaders(response, limit, remaining, resetTime) {
    response.headers.set('X-RateLimit-Limit', limit.toString());
    response.headers.set('X-RateLimit-Remaining', remaining.toString());
    response.headers.set('X-RateLimit-Reset', resetTime.toString());
    if (remaining === 0) {
        response.headers.set('Retry-After', Math.ceil((resetTime - Date.now()) / 1000).toString());
    }
    return response;
}
function applyCorsHeaders(response, origin, allowedOrigins = []) {
    const isDevelopment = ("TURBOPACK compile-time value", "development") === 'development';
    // Allow all origins in development, specific origins in production
    if ("TURBOPACK compile-time truthy", 1) {
        response.headers.set('Access-Control-Allow-Origin', '*');
    } else {
        "TURBOPACK unreachable";
    }
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, X-CSRF-Token');
    response.headers.set('Access-Control-Max-Age', '86400'); // 24 hours
    return response;
}
function auditSecurityHeaders(headers) {
    const issues = [];
    const recommendations = [];
    let score = 100;
    const requiredHeaders = [
        'Content-Security-Policy',
        'X-Frame-Options',
        'X-Content-Type-Options',
        'Referrer-Policy'
    ];
    const recommendedHeaders = [
        'Strict-Transport-Security',
        'Permissions-Policy',
        'Cross-Origin-Opener-Policy'
    ];
    // Check required headers
    requiredHeaders.forEach((header)=>{
        if (!headers.get(header)) {
            issues.push(`Missing required header: ${header}`);
            score -= 20;
        }
    });
    // Check recommended headers
    recommendedHeaders.forEach((header)=>{
        if (!headers.get(header)) {
            recommendations.push(`Consider adding header: ${header}`);
            score -= 5;
        }
    });
    // Check for insecure values
    const csp = headers.get('Content-Security-Policy');
    if (csp && csp.includes("'unsafe-inline'") && !csp.includes('nonce-')) {
        issues.push('CSP allows unsafe-inline without nonce');
        score -= 10;
    }
    const frameOptions = headers.get('X-Frame-Options');
    if (frameOptions && frameOptions.toLowerCase() === 'allowall') {
        issues.push('X-Frame-Options set to ALLOWALL (insecure)');
        score -= 15;
    }
    return {
        score: Math.max(0, score),
        issues,
        recommendations
    };
}
}}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$headers$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/headers.ts [middleware-edge] (ecmascript)");
;
;
const locales = [
    'en',
    'ar'
];
const defaultLocale = 'ar';
// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map();
const rateLimitConfigs = {
    api: {
        windowMs: 15 * 60 * 1000,
        maxRequests: 100
    },
    auth: {
        windowMs: 15 * 60 * 1000,
        maxRequests: 5
    },
    upload: {
        windowMs: 60 * 60 * 1000,
        maxRequests: 10
    },
    default: {
        windowMs: 60 * 1000,
        maxRequests: 60
    }
};
/**
 * Get client IP address
 */ function getClientIP(request) {
    const forwarded = request.headers.get('x-forwarded-for');
    const realIP = request.headers.get('x-real-ip');
    if (forwarded) {
        return forwarded.split(',')[0].trim();
    }
    return realIP || 'unknown';
}
/**
 * Rate limiting implementation
 */ function checkRateLimit(clientIP, endpoint, config) {
    const key = `${clientIP}:${endpoint}`;
    const now = Date.now();
    // Clean up expired entries
    for (const [k, v] of rateLimitStore.entries()){
        if (v.resetTime < now) {
            rateLimitStore.delete(k);
        }
    }
    const current = rateLimitStore.get(key);
    if (!current || current.resetTime < now) {
        rateLimitStore.set(key, {
            count: 1,
            resetTime: now + config.windowMs
        });
        return {
            allowed: true,
            remaining: config.maxRequests - 1,
            resetTime: now + config.windowMs
        };
    }
    if (current.count >= config.maxRequests) {
        return {
            allowed: false,
            remaining: 0,
            resetTime: current.resetTime
        };
    }
    current.count++;
    rateLimitStore.set(key, current);
    return {
        allowed: true,
        remaining: config.maxRequests - current.count,
        resetTime: current.resetTime
    };
}
function getLocale(request) {
    // Check if there is a cookie with the locale
    const cookieLocale = request.cookies.get('NEXT_LOCALE')?.value;
    if (cookieLocale && locales.includes(cookieLocale)) {
        return cookieLocale;
    }
    // Check if there is a locale in the pathname
    const pathname = request.nextUrl.pathname;
    const pathnameLocale = locales.find((locale)=>pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`);
    if (pathnameLocale) {
        return pathnameLocale;
    }
    // Check the Accept-Language header
    const acceptLanguage = request.headers.get('accept-language');
    if (acceptLanguage) {
        const acceptedLocales = acceptLanguage.split(',').map((locale)=>locale.split(';')[0].trim());
        const matchedLocale = acceptedLocales.find((locale)=>locales.includes(locale));
        if (matchedLocale) {
            return matchedLocale;
        }
    }
    // Default to the default locale
    return defaultLocale;
}
function middleware(request) {
    const pathname = request.nextUrl.pathname;
    const clientIP = getClientIP(request);
    // Skip middleware for static assets and Next.js internals
    if (pathname.startsWith('/_next/') || pathname.startsWith('/static/') || pathname.includes('.') && !pathname.startsWith('/api/')) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
    let response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    try {
        // 1. Rate Limiting for API routes
        if (pathname.startsWith('/api/')) {
            const endpoint = pathname.startsWith('/api/auth/') ? 'auth' : 'api';
            const config = rateLimitConfigs[endpoint];
            const rateLimit = checkRateLimit(clientIP, endpoint, config);
            if (!rateLimit.allowed) {
                console.warn(`[SECURITY] Rate limit exceeded for ${clientIP} on ${pathname}`);
                response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Rate limit exceeded'
                }, {
                    status: 429
                });
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$headers$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["applyRateLimitHeaders"])(response, config.maxRequests, rateLimit.remaining, rateLimit.resetTime);
            }
            response = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$headers$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["applyRateLimitHeaders"])(response, config.maxRequests, rateLimit.remaining, rateLimit.resetTime);
        }
        // 2. Internationalization Logic
        const pathnameIsMissingLocale = locales.every((locale)=>!pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`);
        if (pathnameIsMissingLocale) {
            const locale = getLocale(request);
            const url = new URL(`/${locale}${pathname}`, request.url);
            url.search = request.nextUrl.search;
            response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
        }
        // 3. Apply Security Headers
        response = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$headers$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["applySecurityHeaders"])(response);
        // 4. Apply CORS Headers
        const origin = request.headers.get('origin');
        const allowedOrigins = [
            ("TURBOPACK compile-time value", "http://localhost:3000") || 'http://localhost:3000'
        ];
        response = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$headers$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["applyCorsHeaders"])(response, origin || undefined, allowedOrigins);
        return response;
    } catch (error) {
        console.error('Middleware error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
const config = {
    matcher: [
        // Skip all internal paths (_next, api, etc.)
        '/((?!api|_next/static|_next/image|favicon.ico).*)'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__ebb3844e._.js.map