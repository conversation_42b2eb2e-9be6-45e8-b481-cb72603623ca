module.exports = {

"[project]/src/components/shop/ShopPageEnhanced.tsx [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_shop_ShopPageEnhanced_tsx_6eb71af9._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/shop/ShopPageEnhanced.tsx [app-rsc] (ecmascript)");
    });
});
}}),

};