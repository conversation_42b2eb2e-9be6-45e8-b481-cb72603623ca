{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/shop/ShopPageEnhanced.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ShopPageEnhanced = registerClientReference(\n    function() { throw new Error(\"Attempted to call ShopPageEnhanced() from the server but ShopPageEnhanced is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/shop/ShopPageEnhanced.tsx <module evaluation>\",\n    \"ShopPageEnhanced\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0EACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/shop/ShopPageEnhanced.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ShopPageEnhanced = registerClientReference(\n    function() { throw new Error(\"Attempted to call ShopPageEnhanced() from the server but ShopPageEnhanced is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/shop/ShopPageEnhanced.tsx\",\n    \"ShopPageEnhanced\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,sDACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}