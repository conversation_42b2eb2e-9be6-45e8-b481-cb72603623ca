{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/forms/WholesaleQuoteForm.tsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { Button } from '../ui/Button';\nimport { Input } from '../ui/Input';\nimport { Card } from '../ui/Card';\nimport { Send, Upload, CheckCircle, X } from 'lucide-react';\nimport { useAuthStore } from '../../stores/authStore';\nimport { useTranslation } from '../../translations';\nimport { useThemeStore } from '../../stores/themeStore';\nimport { cn } from '../../lib/utils';\nimport { Product } from '../../types/index';\n\ninterface WholesaleQuoteFormProps {\n  isOpen: boolean;\n  onClose: () => void;\n  isCustomProduct?: boolean;\n  serviceName?: string;\n  product?: Product;\n  selectedProduct?: Product;\n  initialQuantity?: number;\n}\n\nexport function WholesaleQuoteForm({ isOpen, onClose, isCustomProduct = false, serviceName, product, selectedProduct, initialQuantity }: WholesaleQuoteFormProps) {\n  const [formData, setFormData] = useState({\n    companyName: '',\n    contactName: '',\n    email: '',\n    phone: '',\n    productType: '',\n    specifications: '',\n    targetQuantity: '',\n    targetPrice: '',\n    timeline: '',\n    additionalNotes: '',\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isSubmitted, setIsSubmitted] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const { user } = useAuthStore();\n  const { t } = useTranslation();\n  const { isDarkMode } = useThemeStore();\n\n  // تعبئة بيانات المستخدم تلقائيًا إذا كان مسجل الدخول\n  useEffect(() => {\n    if (user) {\n      setFormData(prev => ({\n        ...prev,\n        contactName: user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : prev.contactName,\n        email: user.email || prev.email,\n      }));\n    }\n  }, [user]);\n\n  // Pre-fill productType if product is provided\n  useEffect(() => {\n    const productToUse = selectedProduct || product;\n    if (productToUse) {\n      setFormData(prev => ({\n        ...prev,\n        productType: productToUse.name || prev.productType,\n        specifications: productToUse.specifications ?\n          Object.entries(productToUse.specifications)\n            .map(([key, value]) => `${key}: ${value}`)\n            .join('\\n') :\n          prev.specifications,\n        targetQuantity: initialQuantity ? initialQuantity.toString() : prev.targetQuantity,\n      }));\n    }\n  }, [product, selectedProduct, initialQuantity]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError(null);\n    setIsSubmitting(true);\n\n    try {\n      // محاكاة إرسال النموذج - لا يتطلب تسجيل الدخول\n      console.log('Quote request submitted:', formData);\n      console.log('Product info:', selectedProduct || product);\n\n      // محاكاة تأخير الشبكة\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      setIsSubmitted(true);\n\n      // إغلاق النموذج بعد فترة قصيرة\n      setTimeout(() => {\n        onClose();\n      }, 2000);\n    } catch (err) {\n      console.error('Error submitting form:', err);\n      setError(t('wholesale.submitError'));\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  // إذا لم يكن المودال مفتوحًا، لا نعرض شيئًا\n  if (!isOpen) {\n    return null;\n  }\n\n  // إذا تم إرسال النموذج بنجاح\n  if (isSubmitted) {\n    return (\n      <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50\">\n        <div className=\"w-full max-w-md\">\n          <Card className={cn(\"relative p-6\", isDarkMode ? \"bg-slate-800\" : \"bg-white\")}>\n            {/* Close Button */}\n            <button\n              onClick={onClose}\n              className={cn(\n                \"absolute top-4 right-4 z-10 p-2 rounded-full transition-colors\",\n                \"hover:bg-slate-100 dark:hover:bg-slate-700\",\n                \"focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\",\n                \"dark:focus:ring-offset-slate-800\"\n              )}\n              aria-label={t('common.close')}\n              type=\"button\"\n            >\n              <X className=\"h-5 w-5 text-slate-500 dark:text-slate-400\" />\n            </button>\n\n            <div className=\"text-center py-8\">\n              <div className={cn(\n                \"w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4\",\n                isDarkMode ? \"bg-green-900/20\" : \"bg-green-100\"\n              )}>\n                <CheckCircle className={cn(\"h-8 w-8\", isDarkMode ? \"text-green-400\" : \"text-green-600\")} />\n              </div>\n              <h3 className={cn(\"text-xl font-semibold mb-2\", isDarkMode ? \"text-white\" : \"text-slate-900\")}>\n                {t('wholesale.requestSubmitted')}\n              </h3>\n              <p className={cn(\"mb-6\", isDarkMode ? \"text-slate-300\" : \"text-slate-600\")}>\n                {t('wholesale.thankYou')}\n              </p>\n              <Button onClick={onClose}>\n                {t('wholesale.close')}\n              </Button>\n            </div>\n          </Card>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50\">\n      <div className=\"w-full max-w-2xl max-h-[90vh] overflow-y-auto\">\n        <Card className={cn(\"relative p-6\", isDarkMode ? \"bg-slate-800\" : \"bg-white\")}>\n          {/* Close Button */}\n          <button\n            onClick={onClose}\n            className={cn(\n              \"absolute top-4 right-4 z-10 p-2 rounded-full transition-colors\",\n              \"hover:bg-slate-100 dark:hover:bg-slate-700\",\n              \"focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\",\n              \"dark:focus:ring-offset-slate-800\"\n            )}\n            aria-label={t('common.close')}\n            type=\"button\"\n          >\n            <X className=\"h-5 w-5 text-slate-500 dark:text-slate-400\" />\n          </button>\n\n          <h3 className={cn(\"text-xl font-semibold mb-4 pr-12\", isDarkMode ? \"text-white\" : \"text-slate-900\")}>\n            {isCustomProduct ? t('wholesale.customProductTitle') : t('wholesale.wholesaleTitle')}\n          </h3>\n\n          {error && (\n            <div className={cn(\n              \"p-3 rounded-md mb-4\",\n              isDarkMode ? \"bg-red-900/20 text-red-300\" : \"bg-red-50 text-red-600\"\n            )}>\n              {error}\n            </div>\n          )}\n\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className={cn(\"block text-sm font-medium mb-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                  {t('wholesale.companyName')}\n                </label>\n                <Input\n                  name=\"companyName\"\n                  value={formData.companyName}\n                  onChange={handleChange}\n                  required\n                />\n              </div>\n\n              <div>\n                <label className={cn(\"block text-sm font-medium mb-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                  {t('wholesale.contactName')}\n                </label>\n                <Input\n                  name=\"contactName\"\n                  value={formData.contactName}\n                  onChange={handleChange}\n                  required\n                />\n              </div>\n\n              <div>\n                <label className={cn(\"block text-sm font-medium mb-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                  {t('wholesale.email')}\n                </label>\n                <Input\n                  type=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  required\n                />\n              </div>\n\n              <div>\n                <label className={cn(\"block text-sm font-medium mb-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                  {t('wholesale.phone')}\n                </label>\n                <Input\n                  type=\"tel\"\n                  name=\"phone\"\n                  value={formData.phone}\n                  onChange={handleChange}\n                  required\n                />\n              </div>\n            </div>\n\n            <div>\n              <label className={cn(\"block text-sm font-medium mb-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                {t('wholesale.productType')}\n              </label>\n              <Input\n                name=\"productType\"\n                value={formData.productType}\n                onChange={handleChange}\n                required\n                placeholder={t('wholesale.productTypePlaceholder')}\n              />\n            </div>\n\n            <div>\n              <label className={cn(\"block text-sm font-medium mb-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                {t('wholesale.specifications')}\n              </label>\n              <textarea\n                name=\"specifications\"\n                value={formData.specifications}\n                onChange={handleChange}\n                required\n                className={cn(\n                  \"w-full min-h-[100px] px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                  isDarkMode ? \"bg-slate-700 border-slate-600 text-white\" : \"bg-white border-slate-300\"\n                )}\n                placeholder={t('wholesale.specificationsPlaceholder')}\n              />\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className={cn(\"block text-sm font-medium mb-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                  {t('wholesale.targetQuantity')}\n                </label>\n                <Input\n                  name=\"targetQuantity\"\n                  value={formData.targetQuantity}\n                  onChange={handleChange}\n                  required\n                  placeholder={t('wholesale.targetQuantityPlaceholder')}\n                />\n              </div>\n\n              <div>\n                <label className={cn(\"block text-sm font-medium mb-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                  {t('wholesale.targetPrice')}\n                </label>\n                <Input\n                  name=\"targetPrice\"\n                  value={formData.targetPrice}\n                  onChange={handleChange}\n                  placeholder={t('wholesale.targetPricePlaceholder')}\n                />\n              </div>\n            </div>\n\n            <div>\n              <label className={cn(\"block text-sm font-medium mb-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                {t('wholesale.timeline')}\n              </label>\n              <Input\n                name=\"timeline\"\n                value={formData.timeline}\n                onChange={handleChange}\n                required\n                placeholder={t('wholesale.timelinePlaceholder')}\n              />\n            </div>\n\n            <div>\n              <label className={cn(\"block text-sm font-medium mb-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                {t('wholesale.additionalNotes')}\n              </label>\n              <textarea\n                name=\"additionalNotes\"\n                value={formData.additionalNotes}\n                onChange={handleChange}\n                className={cn(\n                  \"w-full min-h-[100px] px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                  isDarkMode ? \"bg-slate-700 border-slate-600 text-white\" : \"bg-white border-slate-300\"\n                )}\n                placeholder={t('wholesale.additionalNotesPlaceholder')}\n              />\n            </div>\n\n            {isCustomProduct && (\n              <div className=\"border-t pt-4\">\n                <label className={cn(\"block text-sm font-medium mb-2\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\")}>\n                  {t('wholesale.uploadFiles')}\n                </label>\n                <div className={cn(\n                  \"border-2 border-dashed rounded-lg p-4 text-center\",\n                  isDarkMode ? \"border-slate-600 bg-slate-700/30\" : \"border-slate-300 bg-slate-50\"\n                )}>\n                  <Upload className={cn(\"mx-auto h-8 w-8 mb-2\", isDarkMode ? \"text-slate-400\" : \"text-slate-400\")} />\n                  <p className={cn(\"text-sm\", isDarkMode ? \"text-slate-300\" : \"text-slate-600\")}>\n                    {t('wholesale.dropFilesHere')}\n                  </p>\n                  <input\n                    type=\"file\"\n                    multiple\n                    className=\"hidden\"\n                    accept=\".pdf,.doc,.docx,.jpg,.jpeg,.png\"\n                    id=\"file-upload\"\n                  />\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    className=\"mt-2\"\n                    onClick={() => {\n                      const fileInput = document.getElementById('file-upload') as HTMLInputElement;\n                      if (fileInput) fileInput.click();\n                    }}\n                  >\n                    {t('wholesale.selectFiles')}\n                  </Button>\n                </div>\n              </div>\n            )}\n\n            <div className=\"flex justify-end gap-2\">\n              <Button type=\"button\" variant=\"outline\" onClick={onClose}>\n                {t('wholesale.cancel')}\n              </Button>\n              <Button\n                type=\"submit\"\n                className=\"flex items-center gap-2\"\n                disabled={isSubmitting}\n              >\n                {!isSubmitting && <Send className=\"w-4 h-4\" />}\n                {isSubmitting ? t('wholesale.submitting') : t('wholesale.submitRequest')}\n              </Button>\n            </div>\n          </form>\n        </Card>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAaO,SAAS,mBAAmB,EAAE,MAAM,EAAE,OAAO,EAAE,kBAAkB,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,eAAe,EAAE,eAAe,EAA2B;;IAC9J,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,aAAa;QACb,aAAa;QACb,OAAO;QACP,OAAO;QACP,aAAa;QACb,gBAAgB;QAChB,gBAAgB;QAChB,aAAa;QACb,UAAU;QACV,iBAAiB;IACnB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD;IAEnC,qDAAqD;IACrD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,MAAM;gBACR;oDAAY,CAAA,OAAQ,CAAC;4BACnB,GAAG,IAAI;4BACP,aAAa,KAAK,SAAS,IAAI,KAAK,QAAQ,GAAG,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GAAG,KAAK,WAAW;4BACtG,OAAO,KAAK,KAAK,IAAI,KAAK,KAAK;wBACjC,CAAC;;YACH;QACF;uCAAG;QAAC;KAAK;IAET,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,eAAe,mBAAmB;YACxC,IAAI,cAAc;gBAChB;oDAAY,CAAA,OAAQ,CAAC;4BACnB,GAAG,IAAI;4BACP,aAAa,aAAa,IAAI,IAAI,KAAK,WAAW;4BAClD,gBAAgB,aAAa,cAAc,GACzC,OAAO,OAAO,CAAC,aAAa,cAAc,EACvC,GAAG;gEAAC,CAAC,CAAC,KAAK,MAAM,GAAK,GAAG,IAAI,EAAE,EAAE,OAAO;+DACxC,IAAI,CAAC,QACR,KAAK,cAAc;4BACrB,gBAAgB,kBAAkB,gBAAgB,QAAQ,KAAK,KAAK,cAAc;wBACpF,CAAC;;YACH;QACF;uCAAG;QAAC;QAAS;QAAiB;KAAgB;IAE9C,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QACT,gBAAgB;QAEhB,IAAI;YACF,+CAA+C;YAC/C,QAAQ,GAAG,CAAC,4BAA4B;YACxC,QAAQ,GAAG,CAAC,iBAAiB,mBAAmB;YAEhD,sBAAsB;YACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,eAAe;YAEf,+BAA+B;YAC/B,WAAW;gBACT;YACF,GAAG;QACL,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS,EAAE;QACb,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IACjD;IAEA,4CAA4C;IAC5C,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,6BAA6B;IAC7B,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,aAAa,iBAAiB;;sCAEhE,6LAAC;4BACC,SAAS;4BACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA,8CACA,8EACA;4BAEF,cAAY,EAAE;4BACd,MAAK;sCAEL,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;sCAGf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,wEACA,aAAa,oBAAoB;8CAEjC,cAAA,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,aAAa,mBAAmB;;;;;;;;;;;8CAExE,6LAAC;oCAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,aAAa,eAAe;8CACzE,EAAE;;;;;;8CAEL,6LAAC;oCAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,aAAa,mBAAmB;8CACtD,EAAE;;;;;;8CAEL,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;8CACd,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOjB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,aAAa,iBAAiB;;kCAEhE,6LAAC;wBACC,SAAS;wBACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA,8CACA,8EACA;wBAEF,cAAY,EAAE;wBACd,MAAK;kCAEL,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;kCAGf,6LAAC;wBAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC,aAAa,eAAe;kCAC/E,kBAAkB,EAAE,kCAAkC,EAAE;;;;;;oBAG1D,uBACC,6LAAC;wBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,uBACA,aAAa,+BAA+B;kCAE3C;;;;;;kCAIL,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;0DACpF,EAAE;;;;;;0DAEL,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU;gDACV,QAAQ;;;;;;;;;;;;kDAIZ,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;0DACpF,EAAE;;;;;;0DAEL,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU;gDACV,QAAQ;;;;;;;;;;;;kDAIZ,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;0DACpF,EAAE;;;;;;0DAEL,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,QAAQ;;;;;;;;;;;;kDAIZ,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;0DACpF,EAAE;;;;;;0DAEL,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,QAAQ;;;;;;;;;;;;;;;;;;0CAKd,6LAAC;;kDACC,6LAAC;wCAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;kDACpF,EAAE;;;;;;kDAEL,6LAAC,oIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,OAAO,SAAS,WAAW;wCAC3B,UAAU;wCACV,QAAQ;wCACR,aAAa,EAAE;;;;;;;;;;;;0CAInB,6LAAC;;kDACC,6LAAC;wCAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;kDACpF,EAAE;;;;;;kDAEL,6LAAC;wCACC,MAAK;wCACL,OAAO,SAAS,cAAc;wCAC9B,UAAU;wCACV,QAAQ;wCACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2GACA,aAAa,6CAA6C;wCAE5D,aAAa,EAAE;;;;;;;;;;;;0CAInB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;0DACpF,EAAE;;;;;;0DAEL,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,OAAO,SAAS,cAAc;gDAC9B,UAAU;gDACV,QAAQ;gDACR,aAAa,EAAE;;;;;;;;;;;;kDAInB,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;0DACpF,EAAE;;;;;;0DAEL,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU;gDACV,aAAa,EAAE;;;;;;;;;;;;;;;;;;0CAKrB,6LAAC;;kDACC,6LAAC;wCAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;kDACpF,EAAE;;;;;;kDAEL,6LAAC,oIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,OAAO,SAAS,QAAQ;wCACxB,UAAU;wCACV,QAAQ;wCACR,aAAa,EAAE;;;;;;;;;;;;0CAInB,6LAAC;;kDACC,6LAAC;wCAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;kDACpF,EAAE;;;;;;kDAEL,6LAAC;wCACC,MAAK;wCACL,OAAO,SAAS,eAAe;wCAC/B,UAAU;wCACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2GACA,aAAa,6CAA6C;wCAE5D,aAAa,EAAE;;;;;;;;;;;;4BAIlB,iCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC,aAAa,mBAAmB;kDACpF,EAAE;;;;;;kDAEL,6LAAC;wCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,qDACA,aAAa,qCAAqC;;0DAElD,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB,aAAa,mBAAmB;;;;;;0DAC9E,6LAAC;gDAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,aAAa,mBAAmB;0DACzD,EAAE;;;;;;0DAEL,6LAAC;gDACC,MAAK;gDACL,QAAQ;gDACR,WAAU;gDACV,QAAO;gDACP,IAAG;;;;;;0DAEL,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS;oDACP,MAAM,YAAY,SAAS,cAAc,CAAC;oDAC1C,IAAI,WAAW,UAAU,KAAK;gDAChC;0DAEC,EAAE;;;;;;;;;;;;;;;;;;0CAMX,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAQ;wCAAU,SAAS;kDAC9C,EAAE;;;;;;kDAEL,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;wCACV,UAAU;;4CAET,CAAC,8BAAgB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACjC,eAAe,EAAE,0BAA0B,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9D;GAlWgB;;QAiBG,6HAAA,CAAA,eAAY;QACf,+HAAA,CAAA,iBAAc;QACL,8HAAA,CAAA,gBAAa;;;KAnBtB", "debugId": null}}, {"offset": {"line": 691, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/ui/Badge.tsx"], "sourcesContent": ["import { cn } from '../../lib/utils';\n\ninterface BadgeProps {\n  children: React.ReactNode;\n  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'destructive' | 'outline';\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n  onClick?: () => void;\n}\n\nexport function Badge({\n  children,\n  variant = 'default',\n  size = 'md',\n  className,\n  onClick,\n}: BadgeProps) {\n  const baseStyles = \"inline-flex items-center justify-center font-medium transition-colors\";\n  \n  const variantStyles = {\n    default: \"bg-slate-100 text-slate-800 dark:bg-slate-700 dark:text-slate-100 hover:bg-slate-200 dark:hover:bg-slate-600\",\n    primary: \"bg-primary-100 text-primary-800 dark:bg-primary-900/30 dark:text-primary-300 hover:bg-primary-200 dark:hover:bg-primary-900/50\",\n    secondary: \"bg-secondary-100 text-secondary-800 dark:bg-secondary-900/30 dark:text-secondary-300 hover:bg-secondary-200 dark:hover:bg-secondary-900/50\",\n    success: \"bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-300 hover:bg-success-200 dark:hover:bg-success-900/50\",\n    warning: \"bg-warning-100 text-warning-800 dark:bg-warning-900/30 dark:text-warning-300 hover:bg-warning-200 dark:hover:bg-warning-900/50\",\n    destructive: \"bg-destructive-100 text-destructive-800 dark:bg-destructive-900/30 dark:text-destructive-300 hover:bg-destructive-200 dark:hover:bg-destructive-900/50\",\n    outline: \"border border-slate-200 dark:border-slate-700 text-slate-800 dark:text-slate-100 hover:bg-slate-100 dark:hover:bg-slate-800\",\n  };\n  \n  const sizeStyles = {\n    sm: \"text-xs px-2 py-0.5 rounded-full\",\n    md: \"text-sm px-2.5 py-0.5 rounded-full\",\n    lg: \"text-base px-3 py-1 rounded-full\",\n  };\n  \n  return (\n    <span\n      className={cn(\n        baseStyles,\n        variantStyles[variant],\n        sizeStyles[size],\n        onClick && \"cursor-pointer\",\n        className\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </span>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAUO,SAAS,MAAM,EACpB,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,SAAS,EACT,OAAO,EACI;IACX,MAAM,aAAa;IAEnB,MAAM,gBAAgB;QACpB,SAAS;QACT,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,aAAa;QACb,SAAS;IACX;IAEA,MAAM,aAAa;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,YACA,aAAa,CAAC,QAAQ,EACtB,UAAU,CAAC,KAAK,EAChB,WAAW,kBACX;QAEF,SAAS;kBAER;;;;;;AAGP;KAvCgB", "debugId": null}}, {"offset": {"line": 736, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/ui/Tooltip.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, ReactNode, cloneElement, isValidElement, ReactElement } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useThemeStore } from '../../stores/themeStore';\nimport { cn } from '../../lib/utils';\n\ninterface TooltipProps {\n  children: ReactElement;\n  content: ReactNode;\n  position?: 'top' | 'right' | 'bottom' | 'left';\n  delay?: number;\n  className?: string;\n  contentClassName?: string;\n  arrowClassName?: string;\n  showArrow?: boolean;\n  disabled?: boolean;\n}\n\nexport function Tooltip({\n  children,\n  content,\n  position = 'top',\n  delay = 300,\n  className,\n  contentClassName,\n  arrowClassName,\n  showArrow = true,\n  disabled = false,\n}: TooltipProps) {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isMounted, setIsMounted] = useState(false);\n  const timeoutRef = useRef<NodeJS.Timeout | null>(null);\n  const { isDarkMode } = useThemeStore();\n\n  const showTooltip = () => {\n    if (disabled) return;\n    \n    if (timeoutRef.current) {\n      clearTimeout(timeoutRef.current);\n    }\n    \n    timeoutRef.current = setTimeout(() => {\n      setIsVisible(true);\n      setIsMounted(true);\n    }, delay);\n  };\n\n  const hideTooltip = () => {\n    if (timeoutRef.current) {\n      clearTimeout(timeoutRef.current);\n      timeoutRef.current = null;\n    }\n    \n    setIsVisible(false);\n    \n    // إزالة العنصر من DOM بعد انتهاء الحركة\n    setTimeout(() => {\n      setIsMounted(false);\n    }, 200);\n  };\n\n  // تحديد موضع التلميح\n  const getPosition = () => {\n    switch (position) {\n      case 'top':\n        return { top: '-10px', left: '50%', transform: 'translate(-50%, -100%)' };\n      case 'right':\n        return { top: '50%', left: 'calc(100% + 10px)', transform: 'translateY(-50%)' };\n      case 'bottom':\n        return { top: 'calc(100% + 10px)', left: '50%', transform: 'translateX(-50%)' };\n      case 'left':\n        return { top: '50%', right: 'calc(100% + 10px)', transform: 'translateY(-50%)' };\n      default:\n        return { top: '-10px', left: '50%', transform: 'translate(-50%, -100%)' };\n    }\n  };\n\n  // تحديد موضع السهم\n  const getArrowPosition = () => {\n    switch (position) {\n      case 'top':\n        return { bottom: '-5px', left: '50%', transform: 'translateX(-50%) rotate(45deg)' };\n      case 'right':\n        return { left: '-5px', top: '50%', transform: 'translateY(-50%) rotate(45deg)' };\n      case 'bottom':\n        return { top: '-5px', left: '50%', transform: 'translateX(-50%) rotate(45deg)' };\n      case 'left':\n        return { right: '-5px', top: '50%', transform: 'translateY(-50%) rotate(45deg)' };\n      default:\n        return { bottom: '-5px', left: '50%', transform: 'translateX(-50%) rotate(45deg)' };\n    }\n  };\n\n  // تحديد حركة الظهور\n  const getAnimationVariants = () => {\n    const baseVariants = {\n      hidden: { opacity: 0 },\n      visible: { opacity: 1 },\n    };\n\n    switch (position) {\n      case 'top':\n        return {\n          hidden: { ...baseVariants.hidden, y: 10 },\n          visible: { ...baseVariants.visible, y: 0 },\n        };\n      case 'right':\n        return {\n          hidden: { ...baseVariants.hidden, x: -10 },\n          visible: { ...baseVariants.visible, x: 0 },\n        };\n      case 'bottom':\n        return {\n          hidden: { ...baseVariants.hidden, y: -10 },\n          visible: { ...baseVariants.visible, y: 0 },\n        };\n      case 'left':\n        return {\n          hidden: { ...baseVariants.hidden, x: 10 },\n          visible: { ...baseVariants.visible, x: 0 },\n        };\n      default:\n        return baseVariants;\n    }\n  };\n\n  if (!isValidElement(children)) {\n    return null;\n  }\n\n  const childProps = {\n    onMouseEnter: showTooltip,\n    onMouseLeave: hideTooltip,\n    onFocus: showTooltip,\n    onBlur: hideTooltip,\n  };\n\n  return (\n    <div className={cn(\"relative inline-block\", className)}>\n      {cloneElement(children, childProps)}\n      \n      <AnimatePresence>\n        {isMounted && (\n          <motion.div\n            initial=\"hidden\"\n            animate={isVisible ? \"visible\" : \"hidden\"}\n            exit=\"hidden\"\n            variants={getAnimationVariants()}\n            transition={{ duration: 0.2 }}\n            className={cn(\n              \"absolute z-50 whitespace-nowrap px-3 py-1.5 text-xs font-medium\",\n              \"bg-slate-900 text-white dark:bg-slate-700\",\n              \"rounded-md shadow-md\",\n              contentClassName\n            )}\n            style={getPosition()}\n          >\n            {content}\n            \n            {showArrow && (\n              <div\n                className={cn(\n                  \"absolute w-2 h-2 bg-slate-900 dark:bg-slate-700\",\n                  arrowClassName\n                )}\n                style={getArrowPosition()}\n              />\n            )}\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAmBO,SAAS,QAAQ,EACtB,QAAQ,EACR,OAAO,EACP,WAAW,KAAK,EAChB,QAAQ,GAAG,EACX,SAAS,EACT,gBAAgB,EAChB,cAAc,EACd,YAAY,IAAI,EAChB,WAAW,KAAK,EACH;;IACb,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IACjD,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD;IAEnC,MAAM,cAAc;QAClB,IAAI,UAAU;QAEd,IAAI,WAAW,OAAO,EAAE;YACtB,aAAa,WAAW,OAAO;QACjC;QAEA,WAAW,OAAO,GAAG,WAAW;YAC9B,aAAa;YACb,aAAa;QACf,GAAG;IACL;IAEA,MAAM,cAAc;QAClB,IAAI,WAAW,OAAO,EAAE;YACtB,aAAa,WAAW,OAAO;YAC/B,WAAW,OAAO,GAAG;QACvB;QAEA,aAAa;QAEb,wCAAwC;QACxC,WAAW;YACT,aAAa;QACf,GAAG;IACL;IAEA,qBAAqB;IACrB,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,KAAK;oBAAS,MAAM;oBAAO,WAAW;gBAAyB;YAC1E,KAAK;gBACH,OAAO;oBAAE,KAAK;oBAAO,MAAM;oBAAqB,WAAW;gBAAmB;YAChF,KAAK;gBACH,OAAO;oBAAE,KAAK;oBAAqB,MAAM;oBAAO,WAAW;gBAAmB;YAChF,KAAK;gBACH,OAAO;oBAAE,KAAK;oBAAO,OAAO;oBAAqB,WAAW;gBAAmB;YACjF;gBACE,OAAO;oBAAE,KAAK;oBAAS,MAAM;oBAAO,WAAW;gBAAyB;QAC5E;IACF;IAEA,mBAAmB;IACnB,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,QAAQ;oBAAQ,MAAM;oBAAO,WAAW;gBAAiC;YACpF,KAAK;gBACH,OAAO;oBAAE,MAAM;oBAAQ,KAAK;oBAAO,WAAW;gBAAiC;YACjF,KAAK;gBACH,OAAO;oBAAE,KAAK;oBAAQ,MAAM;oBAAO,WAAW;gBAAiC;YACjF,KAAK;gBACH,OAAO;oBAAE,OAAO;oBAAQ,KAAK;oBAAO,WAAW;gBAAiC;YAClF;gBACE,OAAO;oBAAE,QAAQ;oBAAQ,MAAM;oBAAO,WAAW;gBAAiC;QACtF;IACF;IAEA,oBAAoB;IACpB,MAAM,uBAAuB;QAC3B,MAAM,eAAe;YACnB,QAAQ;gBAAE,SAAS;YAAE;YACrB,SAAS;gBAAE,SAAS;YAAE;QACxB;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,QAAQ;wBAAE,GAAG,aAAa,MAAM;wBAAE,GAAG;oBAAG;oBACxC,SAAS;wBAAE,GAAG,aAAa,OAAO;wBAAE,GAAG;oBAAE;gBAC3C;YACF,KAAK;gBACH,OAAO;oBACL,QAAQ;wBAAE,GAAG,aAAa,MAAM;wBAAE,GAAG,CAAC;oBAAG;oBACzC,SAAS;wBAAE,GAAG,aAAa,OAAO;wBAAE,GAAG;oBAAE;gBAC3C;YACF,KAAK;gBACH,OAAO;oBACL,QAAQ;wBAAE,GAAG,aAAa,MAAM;wBAAE,GAAG,CAAC;oBAAG;oBACzC,SAAS;wBAAE,GAAG,aAAa,OAAO;wBAAE,GAAG;oBAAE;gBAC3C;YACF,KAAK;gBACH,OAAO;oBACL,QAAQ;wBAAE,GAAG,aAAa,MAAM;wBAAE,GAAG;oBAAG;oBACxC,SAAS;wBAAE,GAAG,aAAa,OAAO;wBAAE,GAAG;oBAAE;gBAC3C;YACF;gBACE,OAAO;QACX;IACF;IAEA,IAAI,eAAC,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;QAC7B,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,cAAc;QACd,cAAc;QACd,SAAS;QACT,QAAQ;IACV;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;;0BACzC,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE,UAAU;0BAExB,6LAAC,4LAAA,CAAA,kBAAe;0BACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAQ;oBACR,SAAS,YAAY,YAAY;oBACjC,MAAK;oBACL,UAAU;oBACV,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mEACA,6CACA,wBACA;oBAEF,OAAO;;wBAEN;wBAEA,2BACC,6LAAC;4BACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mDACA;4BAEF,OAAO;;;;;;;;;;;;;;;;;;;;;;;AAQvB;GA3JgB;;QAcS,8HAAA,CAAA,gBAAa;;;KAdtB", "debugId": null}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/ui/Slider.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as SliderPrimitive from '@radix-ui/react-slider';\nimport { cn } from '../../lib/utils';\n\ninterface SliderProps extends React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root> {\n  indicatorClassName?: string;\n  thumbClassName?: string;\n  trackClassName?: string;\n}\n\nconst Slider = React.forwardRef<\n  React.ElementRef<typeof SliderPrimitive.Root>,\n  SliderProps\n>(({ className, indicatorClassName, thumbClassName, trackClassName, ...props }, ref) => (\n  <SliderPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex w-full touch-none select-none items-center\",\n      className\n    )}\n    {...props}\n  >\n    <SliderPrimitive.Track\n      className={cn(\n        \"relative h-2 w-full grow overflow-hidden rounded-full bg-slate-200 dark:bg-slate-700\",\n        trackClassName\n      )}\n    >\n      <SliderPrimitive.Range\n        className={cn(\n          \"absolute h-full bg-primary-500 dark:bg-primary-400\",\n          indicatorClassName\n        )}\n      />\n    </SliderPrimitive.Track>\n    {props.value?.map((_, i) => (\n      <SliderPrimitive.Thumb\n        key={i}\n        className={cn(\n          \"block h-5 w-5 rounded-full border-2 border-primary-500 bg-white ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 dark:border-primary-400 dark:bg-slate-800 dark:ring-offset-slate-800 dark:focus-visible:ring-primary-400\",\n          thumbClassName\n        )}\n      />\n    ))}\n  </SliderPrimitive.Root>\n));\n\nSlider.displayName = SliderPrimitive.Root.displayName;\n\nexport { Slider };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAYA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,kBAAkB,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,OAAO,EAAE,oBAC9E,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;0BAET,6LAAC,qKAAA,CAAA,QAAqB;gBACpB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wFACA;0BAGF,cAAA,6LAAC,qKAAA,CAAA,QAAqB;oBACpB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;;;;;;;;;;;YAIL,MAAM,KAAK,EAAE,IAAI,CAAC,GAAG,kBACpB,6LAAC,qKAAA,CAAA,QAAqB;oBAEpB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4WACA;mBAHG;;;;;;;;;;;;AAUb,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1036, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/ui/animations/FadeIn.tsx"], "sourcesContent": ["import { ReactNode } from 'react';\nimport { motion, MotionProps } from 'framer-motion';\n\ninterface FadeInProps extends MotionProps {\n  children: ReactNode;\n  delay?: number;\n  duration?: number;\n  className?: string;\n  direction?: 'up' | 'down' | 'left' | 'right' | 'none';\n  distance?: number;\n}\n\nexport function FadeIn({ \n  children, \n  delay = 0, \n  duration = 0.5, \n  className = '',\n  direction = 'up',\n  distance = 20,\n  ...props \n}: FadeInProps) {\n  \n  // Set initial position based on direction\n  const getInitialPosition = () => {\n    switch (direction) {\n      case 'up':\n        return { y: distance };\n      case 'down':\n        return { y: -distance };\n      case 'left':\n        return { x: distance };\n      case 'right':\n        return { x: -distance };\n      case 'none':\n      default:\n        return {};\n    }\n  };\n\n  return (\n    <motion.div\n      initial={{ \n        opacity: 0,\n        ...getInitialPosition()\n      }}\n      animate={{ \n        opacity: 1,\n        y: direction === 'up' ? 0 : direction === 'down' ? 0 : undefined,\n        x: direction === 'left' ? 0 : direction === 'right' ? 0 : undefined\n      }}\n      transition={{ \n        duration,\n        delay,\n        ease: [0.25, 0.1, 0.25, 1.0] // Cubic bezier curve for smooth easing\n      }}\n      className={className}\n      {...props}\n    >\n      {children}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAWO,SAAS,OAAO,EACrB,QAAQ,EACR,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,YAAY,EAAE,EACd,YAAY,IAAI,EAChB,WAAW,EAAE,EACb,GAAG,OACS;IAEZ,0CAA0C;IAC1C,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,GAAG;gBAAS;YACvB,KAAK;gBACH,OAAO;oBAAE,GAAG,CAAC;gBAAS;YACxB,KAAK;gBACH,OAAO;oBAAE,GAAG;gBAAS;YACvB,KAAK;gBACH,OAAO;oBAAE,GAAG,CAAC;gBAAS;YACxB,KAAK;YACL;gBACE,OAAO,CAAC;QACZ;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YACP,SAAS;YACT,GAAG,oBAAoB;QACzB;QACA,SAAS;YACP,SAAS;YACT,GAAG,cAAc,OAAO,IAAI,cAAc,SAAS,IAAI;YACvD,GAAG,cAAc,SAAS,IAAI,cAAc,UAAU,IAAI;QAC5D;QACA,YAAY;YACV;YACA;YACA,MAAM;gBAAC;gBAAM;gBAAK;gBAAM;aAAI,CAAC,uCAAuC;QACtE;QACA,WAAW;QACV,GAAG,KAAK;kBAER;;;;;;AAGP;KAjDgB", "debugId": null}}, {"offset": {"line": 1109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/ui/animations/ScaleIn.tsx"], "sourcesContent": ["import { ReactNode } from 'react';\nimport { motion, MotionProps } from 'framer-motion';\n\ninterface ScaleInProps extends MotionProps {\n  children: ReactNode;\n  delay?: number;\n  duration?: number;\n  className?: string;\n  initialScale?: number;\n}\n\nexport function ScaleIn({\n  children,\n  delay = 0,\n  duration = 0.5,\n  className = '',\n  initialScale = 0.95,\n  ...props\n}: ScaleInProps) {\n  return (\n    <motion.div\n      initial={{ \n        opacity: 0,\n        scale: initialScale\n      }}\n      animate={{ \n        opacity: 1,\n        scale: 1\n      }}\n      transition={{ \n        duration,\n        delay,\n        ease: [0.25, 0.1, 0.25, 1.0]\n      }}\n      className={className}\n      {...props}\n    >\n      {children}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAUO,SAAS,QAAQ,EACtB,QAAQ,EACR,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,YAAY,EAAE,EACd,eAAe,IAAI,EACnB,GAAG,OACU;IACb,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YACP,SAAS;YACT,OAAO;QACT;QACA,SAAS;YACP,SAAS;YACT,OAAO;QACT;QACA,YAAY;YACV;YACA;YACA,MAAM;gBAAC;gBAAM;gBAAK;gBAAM;aAAI;QAC9B;QACA,WAAW;QACV,GAAG,KAAK;kBAER;;;;;;AAGP;KA7BgB", "debugId": null}}, {"offset": {"line": 1157, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/ui/animations/StaggerChildren.tsx"], "sourcesContent": ["import { ReactNode } from 'react';\nimport { motion } from 'framer-motion';\n\ninterface StaggerChildrenProps {\n  children: ReactNode;\n  className?: string;\n  staggerDelay?: number;\n  duration?: number;\n  delayStart?: number;\n}\n\nexport function StaggerChildren({\n  children,\n  className = '',\n  staggerDelay = 0.1,\n  duration = 0.5,\n  delayStart = 0\n}: StaggerChildrenProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: staggerDelay,\n        delayChildren: delayStart\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    show: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: duration,\n        ease: [0.25, 0.1, 0.25, 1.0]\n      }\n    }\n  };\n\n  return (\n    <motion.div\n      className={className}\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"show\"\n    >\n      {Array.isArray(children)\n        ? children.map((child, index) => (\n            <motion.div key={index} variants={itemVariants}>\n              {child}\n            </motion.div>\n          ))\n        : children}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAUO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,YAAY,EAAE,EACd,eAAe,GAAG,EAClB,WAAW,GAAG,EACd,aAAa,CAAC,EACO;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,MAAM;YACJ,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,MAAM;YACJ,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAM;oBAAK;oBAAM;iBAAI;YAC9B;QACF;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,UAAU;QACV,SAAQ;QACR,SAAQ;kBAEP,MAAM,OAAO,CAAC,YACX,SAAS,GAAG,CAAC,CAAC,OAAO,sBACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAa,UAAU;0BAC/B;eADc;;;;wBAInB;;;;;;AAGV;KA9CgB", "debugId": null}}, {"offset": {"line": 1227, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/ui/animations/ScrollAnimation.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useEffect, useState, ReactNode } from 'react';\nimport { motion, useAnimation, Variants } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { cn } from '../../../lib/utils';\n\ninterface ScrollAnimationProps {\n  children: ReactNode;\n  className?: string;\n  animation?: 'fade' | 'slide' | 'scale' | 'rotate' | 'flip' | 'bounce' | 'reveal';\n  direction?: 'up' | 'down' | 'left' | 'right';\n  delay?: number;\n  duration?: number;\n  threshold?: number;\n  once?: boolean;\n  distance?: number;\n  staggerChildren?: boolean;\n  staggerDelay?: number;\n  rootMargin?: string;\n  disabled?: boolean;\n}\n\nexport function ScrollAnimation({\n  children,\n  className,\n  animation = 'fade',\n  direction = 'up',\n  delay = 0,\n  duration = 0.5,\n  threshold = 0.1,\n  once = true,\n  distance = 50,\n  staggerChildren = false,\n  staggerDelay = 0.1,\n  rootMargin = '0px',\n  disabled = false,\n}: ScrollAnimationProps) {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: once,\n    threshold,\n    rootMargin,\n  });\n  const [isMounted, setIsMounted] = useState(false);\n\n  // تحقق مما إذا كان الجهاز محمولاً\n  const [isMobile, setIsMobile] = useState(false);\n  \n  useEffect(() => {\n    setIsMounted(true);\n    setIsMobile(window.innerWidth < 768);\n    \n    const handleResize = () => {\n      setIsMobile(window.innerWidth < 768);\n    };\n    \n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // تعطيل الرسوم المتحركة على الأجهزة المحمولة إذا كان disabled = true\n  const isAnimationDisabled = disabled || (isMobile && document.documentElement.classList.contains('mobile-device'));\n\n  // تحديد متغيرات الرسوم المتحركة بناءً على النوع والاتجاه\n  const getVariants = (): Variants => {\n    const baseVariants: Variants = {\n      hidden: {},\n      visible: {\n        transition: {\n          duration,\n          delay,\n          ease: 'easeOut',\n          staggerChildren: staggerChildren ? staggerDelay : 0,\n        },\n      },\n    };\n\n    switch (animation) {\n      case 'fade':\n        return {\n          ...baseVariants,\n          hidden: { opacity: 0 },\n          visible: { \n            opacity: 1,\n            transition: { ...baseVariants.visible.transition }\n          },\n        };\n      case 'slide':\n        return {\n          ...baseVariants,\n          hidden: { \n            opacity: 0,\n            x: direction === 'left' ? distance : direction === 'right' ? -distance : 0,\n            y: direction === 'up' ? distance : direction === 'down' ? -distance : 0,\n          },\n          visible: { \n            opacity: 1,\n            x: 0,\n            y: 0,\n            transition: { ...baseVariants.visible.transition }\n          },\n        };\n      case 'scale':\n        return {\n          ...baseVariants,\n          hidden: { opacity: 0, scale: 0.8 },\n          visible: { \n            opacity: 1,\n            scale: 1,\n            transition: { ...baseVariants.visible.transition }\n          },\n        };\n      case 'rotate':\n        return {\n          ...baseVariants,\n          hidden: { \n            opacity: 0,\n            rotate: direction === 'left' ? -90 : 90,\n            scale: 0.8,\n          },\n          visible: { \n            opacity: 1,\n            rotate: 0,\n            scale: 1,\n            transition: { ...baseVariants.visible.transition }\n          },\n        };\n      case 'flip':\n        return {\n          ...baseVariants,\n          hidden: { \n            opacity: 0,\n            rotateX: direction === 'up' || direction === 'down' ? 90 : 0,\n            rotateY: direction === 'left' || direction === 'right' ? 90 : 0,\n          },\n          visible: { \n            opacity: 1,\n            rotateX: 0,\n            rotateY: 0,\n            transition: { ...baseVariants.visible.transition }\n          },\n        };\n      case 'bounce':\n        return {\n          ...baseVariants,\n          hidden: { \n            opacity: 0,\n            y: direction === 'up' ? 50 : -50,\n          },\n          visible: { \n            opacity: 1,\n            y: 0,\n            transition: { \n              ...baseVariants.visible.transition,\n              type: 'spring',\n              stiffness: 300,\n              damping: 15,\n            }\n          },\n        };\n      case 'reveal':\n        return {\n          ...baseVariants,\n          hidden: { \n            clipPath: direction === 'up' ? 'inset(100% 0 0 0)' : \n                      direction === 'down' ? 'inset(0 0 100% 0)' : \n                      direction === 'left' ? 'inset(0 0 0 100%)' : \n                      'inset(0 100% 0 0)',\n            opacity: 0,\n          },\n          visible: { \n            clipPath: 'inset(0 0 0 0)',\n            opacity: 1,\n            transition: { \n              ...baseVariants.visible.transition,\n              duration: duration * 1.2,\n            }\n          },\n        };\n      default:\n        return baseVariants;\n    }\n  };\n\n  const variants = getVariants();\n\n  useEffect(() => {\n    if (inView) {\n      controls.start('visible');\n    } else if (!once) {\n      controls.start('hidden');\n    }\n  }, [controls, inView, once]);\n\n  // إذا كانت الرسوم المتحركة معطلة، قم بإرجاع المحتوى بدون رسوم متحركة\n  if (isAnimationDisabled || !isMounted) {\n    return <div className={className}>{children}</div>;\n  }\n\n  return (\n    <motion.div\n      ref={ref}\n      initial=\"hidden\"\n      animate={controls}\n      variants={variants}\n      className={className}\n    >\n      {children}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAuBO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,SAAS,EACT,YAAY,MAAM,EAClB,YAAY,IAAI,EAChB,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,YAAY,GAAG,EACf,OAAO,IAAI,EACX,WAAW,EAAE,EACb,kBAAkB,KAAK,EACvB,eAAe,GAAG,EAClB,aAAa,KAAK,EAClB,WAAW,KAAK,EACK;;IACrB,MAAM,WAAW,CAAA,GAAA,4LAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb;QACA;IACF;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,kCAAkC;IAClC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,aAAa;YACb,YAAY,OAAO,UAAU,GAAG;YAEhC,MAAM;0DAAe;oBACnB,YAAY,OAAO,UAAU,GAAG;gBAClC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;oCAAG,EAAE;IAEL,qEAAqE;IACrE,MAAM,sBAAsB,YAAa,YAAY,SAAS,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC;IAEjG,yDAAyD;IACzD,MAAM,cAAc;QAClB,MAAM,eAAyB;YAC7B,QAAQ,CAAC;YACT,SAAS;gBACP,YAAY;oBACV;oBACA;oBACA,MAAM;oBACN,iBAAiB,kBAAkB,eAAe;gBACpD;YACF;QACF;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,GAAG,YAAY;oBACf,QAAQ;wBAAE,SAAS;oBAAE;oBACrB,SAAS;wBACP,SAAS;wBACT,YAAY;4BAAE,GAAG,aAAa,OAAO,CAAC,UAAU;wBAAC;oBACnD;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,YAAY;oBACf,QAAQ;wBACN,SAAS;wBACT,GAAG,cAAc,SAAS,WAAW,cAAc,UAAU,CAAC,WAAW;wBACzE,GAAG,cAAc,OAAO,WAAW,cAAc,SAAS,CAAC,WAAW;oBACxE;oBACA,SAAS;wBACP,SAAS;wBACT,GAAG;wBACH,GAAG;wBACH,YAAY;4BAAE,GAAG,aAAa,OAAO,CAAC,UAAU;wBAAC;oBACnD;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,YAAY;oBACf,QAAQ;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBACjC,SAAS;wBACP,SAAS;wBACT,OAAO;wBACP,YAAY;4BAAE,GAAG,aAAa,OAAO,CAAC,UAAU;wBAAC;oBACnD;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,YAAY;oBACf,QAAQ;wBACN,SAAS;wBACT,QAAQ,cAAc,SAAS,CAAC,KAAK;wBACrC,OAAO;oBACT;oBACA,SAAS;wBACP,SAAS;wBACT,QAAQ;wBACR,OAAO;wBACP,YAAY;4BAAE,GAAG,aAAa,OAAO,CAAC,UAAU;wBAAC;oBACnD;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,YAAY;oBACf,QAAQ;wBACN,SAAS;wBACT,SAAS,cAAc,QAAQ,cAAc,SAAS,KAAK;wBAC3D,SAAS,cAAc,UAAU,cAAc,UAAU,KAAK;oBAChE;oBACA,SAAS;wBACP,SAAS;wBACT,SAAS;wBACT,SAAS;wBACT,YAAY;4BAAE,GAAG,aAAa,OAAO,CAAC,UAAU;wBAAC;oBACnD;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,YAAY;oBACf,QAAQ;wBACN,SAAS;wBACT,GAAG,cAAc,OAAO,KAAK,CAAC;oBAChC;oBACA,SAAS;wBACP,SAAS;wBACT,GAAG;wBACH,YAAY;4BACV,GAAG,aAAa,OAAO,CAAC,UAAU;4BAClC,MAAM;4BACN,WAAW;4BACX,SAAS;wBACX;oBACF;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,GAAG,YAAY;oBACf,QAAQ;wBACN,UAAU,cAAc,OAAO,sBACrB,cAAc,SAAS,sBACvB,cAAc,SAAS,sBACvB;wBACV,SAAS;oBACX;oBACA,SAAS;wBACP,UAAU;wBACV,SAAS;wBACT,YAAY;4BACV,GAAG,aAAa,OAAO,CAAC,UAAU;4BAClC,UAAU,WAAW;wBACvB;oBACF;gBACF;YACF;gBACE,OAAO;QACX;IACF;IAEA,MAAM,WAAW;IAEjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,QAAQ;gBACV,SAAS,KAAK,CAAC;YACjB,OAAO,IAAI,CAAC,MAAM;gBAChB,SAAS,KAAK,CAAC;YACjB;QACF;oCAAG;QAAC;QAAU;QAAQ;KAAK;IAE3B,qEAAqE;IACrE,IAAI,uBAAuB,CAAC,WAAW;QACrC,qBAAO,6LAAC;YAAI,WAAW;sBAAY;;;;;;IACrC;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,SAAQ;QACR,SAAS;QACT,UAAU;QACV,WAAW;kBAEV;;;;;;AAGP;GA5LgB;;QAeG,4LAAA,CAAA,eAAY;QACP,sKAAA,CAAA,YAAS;;;KAhBjB", "debugId": null}}, {"offset": {"line": 1456, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/ui/animations/ScrollStagger.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useEffect, useState, ReactNode, Children, cloneElement, isValidElement } from 'react';\nimport { motion, useAnimation, Variants } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { cn } from '../../../lib/utils';\n\ninterface ScrollStaggerProps {\n  children: ReactNode;\n  className?: string;\n  animation?: 'fade' | 'slide' | 'scale' | 'rotate' | 'flip' | 'bounce';\n  direction?: 'up' | 'down' | 'left' | 'right';\n  delay?: number;\n  duration?: number;\n  staggerDelay?: number;\n  threshold?: number;\n  once?: boolean;\n  distance?: number;\n  rootMargin?: string;\n  disabled?: boolean;\n  childClassName?: string;\n}\n\nexport function ScrollStagger({\n  children,\n  className,\n  animation = 'fade',\n  direction = 'up',\n  delay = 0,\n  duration = 0.5,\n  staggerDelay = 0.1,\n  threshold = 0.1,\n  once = true,\n  distance = 50,\n  rootMargin = '0px',\n  disabled = false,\n  childClassName,\n}: ScrollStaggerProps) {\n  const controls = useAnimation();\n  const [ref, inView] = useInView({\n    triggerOnce: once,\n    threshold,\n    rootMargin,\n  });\n  const [isMounted, setIsMounted] = useState(false);\n\n  // تحقق مما إذا كان الجهاز محمولاً\n  const [isMobile, setIsMobile] = useState(false);\n  \n  useEffect(() => {\n    setIsMounted(true);\n    setIsMobile(window.innerWidth < 768);\n    \n    const handleResize = () => {\n      setIsMobile(window.innerWidth < 768);\n    };\n    \n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // تعطيل الرسوم المتحركة على الأجهزة المحمولة إذا كان disabled = true\n  const isAnimationDisabled = disabled || (isMobile && document.documentElement.classList.contains('mobile-device'));\n\n  // تحديد متغيرات الرسوم المتحركة للحاوية\n  const containerVariants: Variants = {\n    hidden: {},\n    visible: {\n      transition: {\n        staggerChildren: staggerDelay,\n        delayChildren: delay,\n      },\n    },\n  };\n\n  // تحديد متغيرات الرسوم المتحركة للعناصر الفرعية\n  const getItemVariants = (): Variants => {\n    switch (animation) {\n      case 'fade':\n        return {\n          hidden: { opacity: 0 },\n          visible: { \n            opacity: 1,\n            transition: { duration }\n          },\n        };\n      case 'slide':\n        return {\n          hidden: { \n            opacity: 0,\n            x: direction === 'left' ? distance : direction === 'right' ? -distance : 0,\n            y: direction === 'up' ? distance : direction === 'down' ? -distance : 0,\n          },\n          visible: { \n            opacity: 1,\n            x: 0,\n            y: 0,\n            transition: { duration }\n          },\n        };\n      case 'scale':\n        return {\n          hidden: { opacity: 0, scale: 0.8 },\n          visible: { \n            opacity: 1,\n            scale: 1,\n            transition: { duration }\n          },\n        };\n      case 'rotate':\n        return {\n          hidden: { \n            opacity: 0,\n            rotate: direction === 'left' ? -90 : 90,\n            scale: 0.8,\n          },\n          visible: { \n            opacity: 1,\n            rotate: 0,\n            scale: 1,\n            transition: { duration }\n          },\n        };\n      case 'flip':\n        return {\n          hidden: { \n            opacity: 0,\n            rotateX: direction === 'up' || direction === 'down' ? 90 : 0,\n            rotateY: direction === 'left' || direction === 'right' ? 90 : 0,\n          },\n          visible: { \n            opacity: 1,\n            rotateX: 0,\n            rotateY: 0,\n            transition: { duration }\n          },\n        };\n      case 'bounce':\n        return {\n          hidden: { \n            opacity: 0,\n            y: direction === 'up' ? 50 : -50,\n          },\n          visible: { \n            opacity: 1,\n            y: 0,\n            transition: { \n              duration,\n              type: 'spring',\n              stiffness: 300,\n              damping: 15,\n            }\n          },\n        };\n      default:\n        return {\n          hidden: { opacity: 0 },\n          visible: { opacity: 1, transition: { duration } },\n        };\n    }\n  };\n\n  const itemVariants = getItemVariants();\n\n  useEffect(() => {\n    if (inView) {\n      controls.start('visible');\n    } else if (!once) {\n      controls.start('hidden');\n    }\n  }, [controls, inView, once]);\n\n  // إذا كانت الرسوم المتحركة معطلة، قم بإرجاع المحتوى بدون رسوم متحركة\n  if (isAnimationDisabled || !isMounted) {\n    return <div className={className}>{children}</div>;\n  }\n\n  return (\n    <motion.div\n      ref={ref}\n      className={className}\n      initial=\"hidden\"\n      animate={controls}\n      variants={containerVariants}\n    >\n      {Children.map(children, (child, index) => {\n        if (isValidElement(child)) {\n          return (\n            <motion.div\n              key={index}\n              variants={itemVariants}\n              className={childClassName}\n            >\n              {child}\n            </motion.div>\n          );\n        }\n        return child;\n      })}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAuBO,SAAS,cAAc,EAC5B,QAAQ,EACR,SAAS,EACT,YAAY,MAAM,EAClB,YAAY,IAAI,EAChB,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,eAAe,GAAG,EAClB,YAAY,GAAG,EACf,OAAO,IAAI,EACX,WAAW,EAAE,EACb,aAAa,KAAK,EAClB,WAAW,KAAK,EAChB,cAAc,EACK;;IACnB,MAAM,WAAW,CAAA,GAAA,4LAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb;QACA;IACF;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,kCAAkC;IAClC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,aAAa;YACb,YAAY,OAAO,UAAU,GAAG;YAEhC,MAAM;wDAAe;oBACnB,YAAY,OAAO,UAAU,GAAG;gBAClC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;2CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;kCAAG,EAAE;IAEL,qEAAqE;IACrE,MAAM,sBAAsB,YAAa,YAAY,SAAS,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC;IAEjG,wCAAwC;IACxC,MAAM,oBAA8B;QAClC,QAAQ,CAAC;QACT,SAAS;YACP,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,gDAAgD;IAChD,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,QAAQ;wBAAE,SAAS;oBAAE;oBACrB,SAAS;wBACP,SAAS;wBACT,YAAY;4BAAE;wBAAS;oBACzB;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,QAAQ;wBACN,SAAS;wBACT,GAAG,cAAc,SAAS,WAAW,cAAc,UAAU,CAAC,WAAW;wBACzE,GAAG,cAAc,OAAO,WAAW,cAAc,SAAS,CAAC,WAAW;oBACxE;oBACA,SAAS;wBACP,SAAS;wBACT,GAAG;wBACH,GAAG;wBACH,YAAY;4BAAE;wBAAS;oBACzB;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,QAAQ;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBACjC,SAAS;wBACP,SAAS;wBACT,OAAO;wBACP,YAAY;4BAAE;wBAAS;oBACzB;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,QAAQ;wBACN,SAAS;wBACT,QAAQ,cAAc,SAAS,CAAC,KAAK;wBACrC,OAAO;oBACT;oBACA,SAAS;wBACP,SAAS;wBACT,QAAQ;wBACR,OAAO;wBACP,YAAY;4BAAE;wBAAS;oBACzB;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,QAAQ;wBACN,SAAS;wBACT,SAAS,cAAc,QAAQ,cAAc,SAAS,KAAK;wBAC3D,SAAS,cAAc,UAAU,cAAc,UAAU,KAAK;oBAChE;oBACA,SAAS;wBACP,SAAS;wBACT,SAAS;wBACT,SAAS;wBACT,YAAY;4BAAE;wBAAS;oBACzB;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,QAAQ;wBACN,SAAS;wBACT,GAAG,cAAc,OAAO,KAAK,CAAC;oBAChC;oBACA,SAAS;wBACP,SAAS;wBACT,GAAG;wBACH,YAAY;4BACV;4BACA,MAAM;4BACN,WAAW;4BACX,SAAS;wBACX;oBACF;gBACF;YACF;gBACE,OAAO;oBACL,QAAQ;wBAAE,SAAS;oBAAE;oBACrB,SAAS;wBAAE,SAAS;wBAAG,YAAY;4BAAE;wBAAS;oBAAE;gBAClD;QACJ;IACF;IAEA,MAAM,eAAe;IAErB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,QAAQ;gBACV,SAAS,KAAK,CAAC;YACjB,OAAO,IAAI,CAAC,MAAM;gBAChB,SAAS,KAAK,CAAC;YACjB;QACF;kCAAG;QAAC;QAAU;QAAQ;KAAK;IAE3B,qEAAqE;IACrE,IAAI,uBAAuB,CAAC,WAAW;QACrC,qBAAO,6LAAC;YAAI,WAAW;sBAAY;;;;;;IACrC;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW;QACX,SAAQ;QACR,SAAS;QACT,UAAU;kBAET,6JAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO;YAC9B,kBAAI,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;gBACzB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,UAAU;oBACV,WAAW;8BAEV;mBAJI;;;;;YAOX;YACA,OAAO;QACT;;;;;;AAGN;GAlLgB;;QAeG,4LAAA,CAAA,eAAY;QACP,sKAAA,CAAA,YAAS;;;KAhBjB", "debugId": null}}, {"offset": {"line": 1685, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/ui/animations/HoverAnimation.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode, useState, useEffect } from 'react';\nimport { motion, Variants } from 'framer-motion';\nimport { cn } from '../../../lib/utils';\n\ninterface HoverAnimationProps {\n  children: ReactNode;\n  className?: string;\n  animation?: 'scale' | 'lift' | 'glow' | 'rotate' | 'pulse' | 'bounce' | 'tilt' | 'shine' | 'border' | 'shadow' | 'fade';\n  scale?: number;\n  duration?: number;\n  disabled?: boolean;\n  as?: React.ElementType;\n  onClick?: () => void;\n}\n\nexport function HoverAnimation({\n  children,\n  className,\n  animation = 'scale',\n  scale = 1.05,\n  duration = 0.3,\n  disabled = false,\n  as = 'div',\n  onClick,\n  ...props\n}: HoverAnimationProps & React.HTMLAttributes<HTMLElement>) {\n  const [isMounted, setIsMounted] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n\n  useEffect(() => {\n    setIsMounted(true);\n    setIsMobile(window.innerWidth < 768);\n\n    const handleResize = () => {\n      setIsMobile(window.innerWidth < 768);\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // تعطيل الرسوم المتحركة على الأجهزة المحمولة إذا كان disabled = true\n  const isAnimationDisabled = disabled || (isMobile && typeof document !== 'undefined' && document.documentElement.classList.contains('mobile-device'));\n\n  // تحسين الأداء عن طريق استخدام will-change للعناصر التي تحتاج إلى تحسين أداء الرسوم المتحركة\n  useEffect(() => {\n    if (isMounted && !isAnimationDisabled) {\n      const needsHardwareAcceleration = ['lift', 'rotate', 'tilt', 'pulse', 'bounce'].includes(animation);\n      if (needsHardwareAcceleration) {\n        const element = document.querySelector(`.animation-${animation}`);\n        if (element) {\n          element.classList.add('will-change-transform');\n        }\n      }\n    }\n  }, [isMounted, isAnimationDisabled, animation]);\n\n  // تحديد متغيرات الرسوم المتحركة بناءً على النوع\n  const getVariants = (): Variants => {\n    const baseTransition = {\n      type: 'tween',\n      duration,\n    };\n\n    switch (animation) {\n      case 'scale':\n        return {\n          initial: {},\n          hover: {\n            scale,\n            transition: baseTransition\n          },\n          tap: {\n            scale: scale * 0.95,\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      case 'lift':\n        return {\n          initial: {},\n          hover: {\n            y: -8,\n            transition: baseTransition\n          },\n          tap: {\n            y: -4,\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      case 'glow':\n        return {\n          initial: {},\n          hover: {\n            boxShadow: '0 0 15px rgba(var(--color-primary-500), 0.5)',\n            transition: baseTransition\n          },\n          tap: {\n            boxShadow: '0 0 8px rgba(var(--color-primary-500), 0.3)',\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      case 'rotate':\n        return {\n          initial: {},\n          hover: {\n            rotate: 5,\n            transition: baseTransition\n          },\n          tap: {\n            rotate: 2,\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      case 'pulse':\n        return {\n          initial: {},\n          hover: {\n            scale: [1, scale, 1, scale, 1],\n            transition: {\n              duration: duration * 2,\n              repeat: Infinity,\n              repeatType: 'loop'\n            }\n          },\n          tap: {\n            scale: scale * 0.95,\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      case 'bounce':\n        return {\n          initial: {},\n          hover: {\n            y: [0, -10, 0],\n            transition: {\n              duration: duration * 1.5,\n              repeat: Infinity,\n              repeatType: 'loop'\n            }\n          },\n          tap: {\n            y: -5,\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      case 'tilt':\n        return {\n          initial: {},\n          hover: {\n            rotateX: -10,\n            rotateY: 10,\n            transition: baseTransition\n          },\n          tap: {\n            rotateX: -5,\n            rotateY: 5,\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      case 'shine':\n        return {\n          initial: {},\n          hover: {},\n          tap: {\n            scale: 0.98,\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      case 'border':\n        return {\n          initial: { borderColor: 'rgba(var(--color-primary-500), 0)' },\n          hover: {\n            borderColor: 'rgba(var(--color-primary-500), 1)',\n            borderWidth: '2px',\n            transition: baseTransition\n          },\n          tap: {\n            borderColor: 'rgba(var(--color-primary-600), 1)',\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      case 'shadow':\n        return {\n          initial: { boxShadow: '0 0 0 rgba(0, 0, 0, 0)' },\n          hover: {\n            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)',\n            y: -2,\n            transition: baseTransition\n          },\n          tap: {\n            boxShadow: '0 5px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',\n            y: -1,\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      case 'fade':\n        return {\n          initial: { opacity: 1 },\n          hover: {\n            opacity: 0.8,\n            transition: baseTransition\n          },\n          tap: {\n            opacity: 0.9,\n            transition: { ...baseTransition, duration: duration / 2 }\n          }\n        };\n      default:\n        return {\n          initial: {},\n          hover: {},\n          tap: {}\n        };\n    }\n  };\n\n  const variants = getVariants();\n  const Component = motion[as as keyof typeof motion] || motion.div;\n\n  // إذا كانت الرسوم المتحركة معطلة، قم بإرجاع المحتوى بدون رسوم متحركة\n  if (isAnimationDisabled || !isMounted) {\n    const ElementType = as;\n    return (\n      <ElementType className={className} onClick={onClick} {...props}>\n        {children}\n      </ElementType>\n    );\n  }\n\n  return (\n    <Component\n      className={cn(\n        className,\n        `animation-${animation}`,\n        animation === 'shine' && 'group overflow-hidden relative',\n        animation === 'border' && 'border border-transparent',\n        animation === 'shadow' && 'transition-shadow'\n      )}\n      initial=\"initial\"\n      whileHover=\"hover\"\n      whileTap=\"tap\"\n      variants={variants}\n      onClick={onClick}\n      {...props}\n    >\n      {children}\n      {animation === 'shine' && (\n        <motion.div\n          className=\"absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent group-hover:translate-x-full\"\n          transition={{ duration: duration * 2, ease: 'linear' }}\n        />\n      )}\n      {animation === 'glow' && (\n        <motion.div\n          className=\"absolute inset-0 rounded-inherit opacity-0 group-hover:opacity-100\"\n          initial={{ opacity: 0, boxShadow: '0 0 0 rgba(var(--color-primary-500), 0)' }}\n          whileHover={{\n            opacity: 1,\n            boxShadow: '0 0 15px rgba(var(--color-primary-500), 0.5)',\n            transition: { duration }\n          }}\n        />\n      )}\n    </Component>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAiBO,SAAS,eAAe,EAC7B,QAAQ,EACR,SAAS,EACT,YAAY,OAAO,EACnB,QAAQ,IAAI,EACZ,WAAW,GAAG,EACd,WAAW,KAAK,EAChB,KAAK,KAAK,EACV,OAAO,EACP,GAAG,OACqD;;IACxD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,aAAa;YACb,YAAY,OAAO,UAAU,GAAG;YAEhC,MAAM;yDAAe;oBACnB,YAAY,OAAO,UAAU,GAAG;gBAClC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;4CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;mCAAG,EAAE;IAEL,qEAAqE;IACrE,MAAM,sBAAsB,YAAa,YAAY,OAAO,aAAa,eAAe,SAAS,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC;IAEpI,6FAA6F;IAC7F,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,aAAa,CAAC,qBAAqB;gBACrC,MAAM,4BAA4B;oBAAC;oBAAQ;oBAAU;oBAAQ;oBAAS;iBAAS,CAAC,QAAQ,CAAC;gBACzF,IAAI,2BAA2B;oBAC7B,MAAM,UAAU,SAAS,aAAa,CAAC,CAAC,WAAW,EAAE,WAAW;oBAChE,IAAI,SAAS;wBACX,QAAQ,SAAS,CAAC,GAAG,CAAC;oBACxB;gBACF;YACF;QACF;mCAAG;QAAC;QAAW;QAAqB;KAAU;IAE9C,gDAAgD;IAChD,MAAM,cAAc;QAClB,MAAM,iBAAiB;YACrB,MAAM;YACN;QACF;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,SAAS,CAAC;oBACV,OAAO;wBACL;wBACA,YAAY;oBACd;oBACA,KAAK;wBACH,OAAO,QAAQ;wBACf,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS,CAAC;oBACV,OAAO;wBACL,GAAG,CAAC;wBACJ,YAAY;oBACd;oBACA,KAAK;wBACH,GAAG,CAAC;wBACJ,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS,CAAC;oBACV,OAAO;wBACL,WAAW;wBACX,YAAY;oBACd;oBACA,KAAK;wBACH,WAAW;wBACX,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS,CAAC;oBACV,OAAO;wBACL,QAAQ;wBACR,YAAY;oBACd;oBACA,KAAK;wBACH,QAAQ;wBACR,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS,CAAC;oBACV,OAAO;wBACL,OAAO;4BAAC;4BAAG;4BAAO;4BAAG;4BAAO;yBAAE;wBAC9B,YAAY;4BACV,UAAU,WAAW;4BACrB,QAAQ;4BACR,YAAY;wBACd;oBACF;oBACA,KAAK;wBACH,OAAO,QAAQ;wBACf,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS,CAAC;oBACV,OAAO;wBACL,GAAG;4BAAC;4BAAG,CAAC;4BAAI;yBAAE;wBACd,YAAY;4BACV,UAAU,WAAW;4BACrB,QAAQ;4BACR,YAAY;wBACd;oBACF;oBACA,KAAK;wBACH,GAAG,CAAC;wBACJ,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS,CAAC;oBACV,OAAO;wBACL,SAAS,CAAC;wBACV,SAAS;wBACT,YAAY;oBACd;oBACA,KAAK;wBACH,SAAS,CAAC;wBACV,SAAS;wBACT,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS,CAAC;oBACV,OAAO,CAAC;oBACR,KAAK;wBACH,OAAO;wBACP,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS;wBAAE,aAAa;oBAAoC;oBAC5D,OAAO;wBACL,aAAa;wBACb,aAAa;wBACb,YAAY;oBACd;oBACA,KAAK;wBACH,aAAa;wBACb,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS;wBAAE,WAAW;oBAAyB;oBAC/C,OAAO;wBACL,WAAW;wBACX,GAAG,CAAC;wBACJ,YAAY;oBACd;oBACA,KAAK;wBACH,WAAW;wBACX,GAAG,CAAC;wBACJ,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF,KAAK;gBACH,OAAO;oBACL,SAAS;wBAAE,SAAS;oBAAE;oBACtB,OAAO;wBACL,SAAS;wBACT,YAAY;oBACd;oBACA,KAAK;wBACH,SAAS;wBACT,YAAY;4BAAE,GAAG,cAAc;4BAAE,UAAU,WAAW;wBAAE;oBAC1D;gBACF;YACF;gBACE,OAAO;oBACL,SAAS,CAAC;oBACV,OAAO,CAAC;oBACR,KAAK,CAAC;gBACR;QACJ;IACF;IAEA,MAAM,WAAW;IACjB,MAAM,YAAY,6LAAA,CAAA,SAAM,CAAC,GAA0B,IAAI,6LAAA,CAAA,SAAM,CAAC,GAAG;IAEjE,qEAAqE;IACrE,IAAI,uBAAuB,CAAC,WAAW;QACrC,MAAM,cAAc;QACpB,qBACE,6LAAC;YAAY,WAAW;YAAW,SAAS;YAAU,GAAG,KAAK;sBAC3D;;;;;;IAGP;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,WACA,CAAC,UAAU,EAAE,WAAW,EACxB,cAAc,WAAW,kCACzB,cAAc,YAAY,6BAC1B,cAAc,YAAY;QAE5B,SAAQ;QACR,YAAW;QACX,UAAS;QACT,UAAU;QACV,SAAS;QACR,GAAG,KAAK;;YAER;YACA,cAAc,yBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,YAAY;oBAAE,UAAU,WAAW;oBAAG,MAAM;gBAAS;;;;;;YAGxD,cAAc,wBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,WAAW;gBAA0C;gBAC5E,YAAY;oBACV,SAAS;oBACT,WAAW;oBACX,YAAY;wBAAE;oBAAS;gBACzB;;;;;;;;;;;;AAKV;GA1PgB;KAAA", "debugId": null}}, {"offset": {"line": 2024, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/ui/animations/index.ts"], "sourcesContent": ["export * from './FadeIn';\nexport * from './ScaleIn';\nexport * from './StaggerChildren';\nexport * from './SmoothTransition';\nexport * from './ScrollAnimation';\nexport * from './ScrollStagger';\nexport * from './HoverAnimation';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2063, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/shop/EnhancedProductFilters.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport {\n  Filter,\n  Search,\n  X,\n  ChevronDown,\n  ChevronUp,\n  Star,\n  Tag,\n  Sliders,\n  RefreshCw,\n  Check,\n  TrendingUp,\n  Zap,\n  Factory,\n  Bot,\n  Award,\n  Rocket,\n  Package\n} from 'lucide-react';\nimport { Button } from '../ui/Button';\nimport { Input } from '../ui/Input';\nimport { Slider } from '../ui/Slider';\nimport { Badge } from '../ui/Badge';\nimport { Tooltip } from '../ui/Tooltip';\nimport { HoverAnimation } from '../ui/animations';\nimport { formatCurrency, cn } from '../../lib/utils';\nimport { ProductCategory, ProductFiltersState } from '../../types/index';\nimport { useLanguageStore } from '../../stores/languageStore';\nimport { useTranslation } from '../../translations';\n\n// Category icons mapping\nconst categoryIcons = {\n  'trending-products': TrendingUp,\n  'innovative-tech': Zap,\n  'high-demand-industrial': Factory,\n  'smart-automation': Bot,\n  'market-leaders': Award,\n  'future-ready': Rocket,\n  'all': Package\n};\n\ninterface EnhancedProductFiltersProps {\n  filters: ProductFiltersState;\n  setFilters: React.Dispatch<React.SetStateAction<ProductFiltersState>>;\n  resetFilters: () => void;\n  maxPrice: number;\n  productCategories: ProductCategory[];\n  showMobileFilters: boolean;\n  setShowMobileFilters: (show: boolean) => void;\n  activeFiltersCount: number;\n  tags?: string[];\n}\n\nexport function EnhancedProductFilters({\n  filters,\n  setFilters,\n  resetFilters,\n  maxPrice,\n  productCategories,\n  showMobileFilters,\n  setShowMobileFilters,\n  activeFiltersCount,\n  tags = []\n}: EnhancedProductFiltersProps) {\n  const { language } = useLanguageStore();\n  const { t, locale } = useTranslation();\n  const [expandedSections, setExpandedSections] = useState({\n    categories: true,\n    price: true,\n    availability: true,\n    rating: true,\n    tags: true\n  });\n  const [selectedTags, setSelectedTags] = useState<string[]>([]);\n  const [selectedRating, setSelectedRating] = useState<number | null>(null);\n  const [priceRange, setPriceRange] = useState([filters.priceRange.min, filters.priceRange.max]);\n\n  // استخدام اللغة من المسار أو من المتجر\n  const currentLanguage = (locale as 'ar' | 'en') || language;\n  const isRTL = currentLanguage === 'ar';\n\n  // تحديث الفلاتر عند تغيير التصنيفات المحددة\n  useEffect(() => {\n    if (selectedTags.length > 0) {\n      setFilters(prev => ({ ...prev, tags: selectedTags }));\n    } else {\n      setFilters(prev => {\n        const newFilters = { ...prev };\n        delete newFilters.tags;\n        return newFilters;\n      });\n    }\n  }, [selectedTags, setFilters]);\n\n  // تحديث الفلاتر عند تغيير التقييم المحدد\n  useEffect(() => {\n    if (selectedRating !== null) {\n      setFilters(prev => ({ ...prev, rating: selectedRating }));\n    } else {\n      setFilters(prev => {\n        const newFilters = { ...prev };\n        delete newFilters.rating;\n        return newFilters;\n      });\n    }\n  }, [selectedRating, setFilters]);\n\n  // تحديث الفلاتر عند تغيير نطاق السعر\n  useEffect(() => {\n    setFilters(prev => ({\n      ...prev,\n      priceRange: { min: priceRange[0], max: priceRange[1] }\n    }));\n  }, [priceRange, setFilters]);\n\n  // تبديل حالة توسيع القسم\n  const toggleSection = (section: keyof typeof expandedSections) => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [section]: !prev[section]\n    }));\n  };\n\n  // إضافة أو إزالة وسم من الوسوم المحددة\n  const toggleTag = (tag: string) => {\n    setSelectedTags(prev =>\n      prev.includes(tag)\n        ? prev.filter(t => t !== tag)\n        : [...prev, tag]\n    );\n  };\n\n  // تحديد التقييم\n  const handleRatingSelect = (rating: number) => {\n    setSelectedRating(prev => prev === rating ? null : rating);\n  };\n\n  // إعادة تعيين جميع الفلاتر\n  const handleResetFilters = () => {\n    setSelectedTags([]);\n    setSelectedRating(null);\n    setPriceRange([0, maxPrice]);\n    resetFilters();\n  };\n\n  return (\n    <div className=\"mb-8\">\n      {/* رأس الفلاتر */}\n      <div className=\"flex justify-between items-center mb-4\">\n        <div className=\"flex items-center gap-2\">\n          <h2 className=\"text-xl font-semibold text-slate-900 dark:text-white\">\n            {currentLanguage === 'ar' ? 'تصفية المنتجات' : 'Filter Products'}\n          </h2>\n          {activeFiltersCount > 0 && (\n            <Badge variant=\"primary\" className=\"text-xs\">\n              {activeFiltersCount}\n            </Badge>\n          )}\n        </div>\n        <div className=\"flex items-center gap-2\">\n          {activeFiltersCount > 0 && (\n            <Tooltip content={currentLanguage === 'ar' ? 'إعادة تعيين الفلاتر' : 'Reset Filters'}>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={handleResetFilters}\n                className=\"text-slate-600 dark:text-slate-400 hover:text-primary-600 dark:hover:text-primary-400\"\n              >\n                <RefreshCw className=\"h-4 w-4 mr-1\" />\n                <span className=\"hidden sm:inline\">\n                  {currentLanguage === 'ar' ? 'إعادة تعيين' : 'Reset'}\n                </span>\n              </Button>\n            </Tooltip>\n          )}\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={() => setShowMobileFilters(!showMobileFilters)}\n            className=\"md:hidden\"\n          >\n            <Filter className=\"h-4 w-4 mr-1\" />\n            <span>\n              {showMobileFilters\n                ? (currentLanguage === 'ar' ? 'إخفاء الفلاتر' : 'Hide Filters')\n                : (currentLanguage === 'ar' ? 'عرض الفلاتر' : 'Show Filters')}\n            </span>\n          </Button>\n        </div>\n      </div>\n\n      {/* محتوى الفلاتر */}\n      <div className={cn(\n        \"bg-white dark:bg-slate-800 rounded-xl shadow-lg overflow-hidden transition-all duration-300 border border-slate-200 dark:border-slate-700\",\n        showMobileFilters ? \"max-h-[2000px] opacity-100\" : \"max-h-0 opacity-0 md:max-h-[2000px] md:opacity-100\"\n      )}>\n        <div className=\"p-6 space-y-6\">\n          {/* فلاتر نشطة */}\n          {activeFiltersCount > 0 && (\n            <div className=\"bg-slate-50 dark:bg-slate-700/50 rounded-lg p-4\">\n              <div className=\"flex flex-wrap items-center gap-2\">\n                <span className=\"text-sm font-medium text-slate-700 dark:text-slate-300\">\n                  {currentLanguage === 'ar' ? 'الفلاتر النشطة:' : 'Active Filters:'}\n                </span>\n                {filters.category !== 'all' && (\n                  <Badge variant=\"secondary\" className=\"flex items-center gap-1 px-3 py-1\">\n                    <span>\n                      {currentLanguage === 'ar' ? 'الفئة: ' : 'Category: '}\n                      {productCategories.find(c => c.id === filters.category)?.name[currentLanguage] || filters.category}\n                    </span>\n                    <X\n                      className=\"h-3 w-3 ml-1 cursor-pointer\"\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        setFilters(prev => ({ ...prev, category: 'all' }));\n                      }}\n                    />\n                  </Badge>\n                )}\n                {filters.searchQuery && (\n                  <Badge variant=\"secondary\" className=\"flex items-center gap-1 px-3 py-1\">\n                    <span>\n                      {currentLanguage === 'ar' ? 'بحث: ' : 'Search: '}\n                      {filters.searchQuery}\n                    </span>\n                    <X\n                      className=\"h-3 w-3 ml-1 cursor-pointer\"\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        setFilters(prev => ({ ...prev, searchQuery: '' }));\n                      }}\n                    />\n                  </Badge>\n                )}\n                {(filters.priceRange.min > 0 || filters.priceRange.max < maxPrice) && (\n                  <Badge variant=\"secondary\" className=\"flex items-center gap-1 px-3 py-1\">\n                    <span>\n                      {currentLanguage === 'ar' ? 'السعر: ' : 'Price: '}\n                      {formatCurrency(filters.priceRange.min)} - {formatCurrency(filters.priceRange.max)}\n                    </span>\n                    <X\n                      className=\"h-3 w-3 ml-1 cursor-pointer\"\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        setPriceRange([0, maxPrice]);\n                        setFilters(prev => ({ ...prev, priceRange: { min: 0, max: maxPrice } }));\n                      }}\n                    />\n                  </Badge>\n                )}\n                {filters.inStock && (\n                  <Badge variant=\"secondary\" className=\"flex items-center gap-1 px-3 py-1\">\n                    <span>{currentLanguage === 'ar' ? 'متوفر في المخزون' : 'In Stock'}</span>\n                    <X\n                      className=\"h-3 w-3 ml-1 cursor-pointer\"\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        setFilters(prev => ({ ...prev, inStock: false }));\n                      }}\n                    />\n                  </Badge>\n                )}\n                {filters.onSale && (\n                  <Badge variant=\"secondary\" className=\"flex items-center gap-1 px-3 py-1\">\n                    <span>{currentLanguage === 'ar' ? 'العروض' : 'On Sale'}</span>\n                    <X\n                      className=\"h-3 w-3 ml-1 cursor-pointer\"\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        setFilters(prev => ({ ...prev, onSale: false }));\n                      }}\n                    />\n                  </Badge>\n                )}\n                {filters.featured && (\n                  <Badge variant=\"secondary\" className=\"flex items-center gap-1 px-3 py-1\">\n                    <span>{currentLanguage === 'ar' ? 'المنتجات المميزة' : 'Featured'}</span>\n                    <X\n                      className=\"h-3 w-3 ml-1 cursor-pointer\"\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        setFilters(prev => ({ ...prev, featured: false }));\n                      }}\n                    />\n                  </Badge>\n                )}\n                {filters.newArrivals && (\n                  <Badge variant=\"secondary\" className=\"flex items-center gap-1 px-3 py-1\">\n                    <span>{currentLanguage === 'ar' ? 'وصل حديثاً' : 'New Arrivals'}</span>\n                    <X\n                      className=\"h-3 w-3 ml-1 cursor-pointer\"\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        setFilters(prev => ({ ...prev, newArrivals: false }));\n                      }}\n                    />\n                  </Badge>\n                )}\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={handleResetFilters}\n                  className=\"text-slate-600 dark:text-slate-400 hover:text-primary-600 dark:hover:text-primary-400 ml-2\"\n                >\n                  <RefreshCw className=\"h-3.5 w-3.5 mr-1\" />\n                  <span>{currentLanguage === 'ar' ? 'مسح الكل' : 'Clear All'}</span>\n                </Button>\n              </div>\n            </div>\n          )}\n          {/* البحث */}\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center\">\n              <Search className=\"h-4 w-4 mr-2 text-primary-500\" />\n              <span className=\"font-medium text-slate-900 dark:text-white\">\n                {currentLanguage === 'ar' ? 'البحث في المنتجات' : 'Search Products'}\n              </span>\n            </div>\n            <div className=\"relative\">\n              <Input\n                type=\"text\"\n                placeholder={currentLanguage === 'ar' ? 'ابحث عن منتجات...' : 'Search for products...'}\n                value={filters.searchQuery}\n                onChange={(e) => setFilters({ ...filters, searchQuery: e.target.value })}\n                className={cn(\n                  \"pl-10 pr-10 py-3 bg-slate-50 dark:bg-slate-700 border-slate-200 dark:border-slate-600 rounded-lg\",\n                  \"focus:border-primary-500 focus:ring-2 focus:ring-primary-200 dark:focus:ring-primary-800\",\n                  \"transition-all duration-200\"\n                )}\n              />\n              <Search className={cn(\n                \"absolute top-1/2 transform -translate-y-1/2 text-slate-400 dark:text-slate-500 h-4 w-4\",\n                isRTL ? \"right-3\" : \"left-3\"\n              )} />\n              {filters.searchQuery && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  onClick={() => setFilters({ ...filters, searchQuery: '' })}\n                  className={cn(\n                    \"absolute top-1/2 transform -translate-y-1/2 h-8 w-8 p-1.5 text-slate-400 hover:text-slate-600\",\n                    isRTL ? \"left-3\" : \"right-3\"\n                  )}\n                  aria-label={currentLanguage === 'ar' ? 'مسح البحث' : 'Clear search'}\n                >\n                  <X className=\"h-4 w-4\" />\n                </Button>\n              )}\n            </div>\n          </div>\n\n          {/* الفئات */}\n          <div className=\"space-y-2\">\n            <div className=\"border-b border-slate-200 dark:border-slate-700 pb-2 mb-3\">\n              <button\n                onClick={() => toggleSection('categories')}\n                className=\"flex items-center justify-between w-full text-left font-medium text-slate-900 dark:text-white\"\n              >\n                <div className=\"flex items-center\">\n                  <Tag className=\"h-4 w-4 mr-2 text-primary-500\" />\n                  <span>{t('shop.filters.category')}</span>\n                </div>\n                {expandedSections.categories ? (\n                  <ChevronUp className=\"h-4 w-4 text-slate-500\" />\n                ) : (\n                  <ChevronDown className=\"h-4 w-4 text-slate-500\" />\n                )}\n              </button>\n            </div>\n            {expandedSections.categories && (\n              <div className=\"space-y-1 mt-3 max-h-60 overflow-y-auto pr-2\">\n                <HoverAnimation animation=\"lift\">\n                  <div\n                    className={cn(\n                      \"flex items-center px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200\",\n                      \"border border-transparent hover:border-slate-200 dark:hover:border-slate-600\",\n                      filters.category === 'all'\n                        ? \"bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300 border-primary-200 dark:border-primary-700\"\n                        : \"hover:bg-slate-50 dark:hover:bg-slate-700/50\"\n                    )}\n                    onClick={() => setFilters({ ...filters, category: 'all' })}\n                  >\n                    <div className={cn(\n                      \"flex items-center justify-center w-8 h-8 rounded-lg mr-3\",\n                      filters.category === 'all'\n                        ? \"bg-primary-100 dark:bg-primary-800/50\"\n                        : \"bg-slate-100 dark:bg-slate-700\"\n                    )}>\n                      <Package className={cn(\n                        \"h-4 w-4\",\n                        filters.category === 'all'\n                          ? \"text-primary-600 dark:text-primary-400\"\n                          : \"text-slate-500 dark:text-slate-400\"\n                      )} />\n                    </div>\n                    <div className=\"flex-1\">\n                      <span className=\"font-medium text-sm\">\n                        {t('shop.filters.allCategories')}\n                      </span>\n                    </div>\n                    <Check className={cn(\n                      \"h-4 w-4 transition-opacity\",\n                      filters.category === 'all' ? \"opacity-100\" : \"opacity-0\"\n                    )} />\n                  </div>\n                </HoverAnimation>\n                {productCategories.map(category => {\n                  const IconComponent = categoryIcons[category.id as keyof typeof categoryIcons] || Package;\n                  return (\n                    <HoverAnimation key={category.id} animation=\"lift\">\n                      <div\n                        className={cn(\n                          \"flex items-center px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200\",\n                          \"border border-transparent hover:border-slate-200 dark:hover:border-slate-600\",\n                          filters.category === category.id\n                            ? \"bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300 border-primary-200 dark:border-primary-700\"\n                            : \"hover:bg-slate-50 dark:hover:bg-slate-700/50\"\n                        )}\n                        onClick={() => setFilters({ ...filters, category: category.id })}\n                      >\n                        <div className={cn(\n                          \"flex items-center justify-center w-8 h-8 rounded-lg mr-3\",\n                          filters.category === category.id\n                            ? \"bg-primary-100 dark:bg-primary-800/50\"\n                            : \"bg-slate-100 dark:bg-slate-700\"\n                        )}>\n                          <IconComponent className={cn(\n                            \"h-4 w-4\",\n                            filters.category === category.id\n                              ? \"text-primary-600 dark:text-primary-400\"\n                              : \"text-slate-500 dark:text-slate-400\"\n                          )} />\n                        </div>\n                        <div className=\"flex-1\">\n                          <span className=\"font-medium text-sm\">\n                            {category.name[currentLanguage]}\n                          </span>\n                          <p className=\"text-xs text-slate-500 dark:text-slate-400 mt-0.5\">\n                            {category.description[currentLanguage]}\n                          </p>\n                        </div>\n                        <Check className={cn(\n                          \"h-4 w-4 transition-opacity\",\n                          filters.category === category.id ? \"opacity-100\" : \"opacity-0\"\n                        )} />\n                      </div>\n                    </HoverAnimation>\n                  );\n                })}\n              </div>\n            )}\n          </div>\n\n          {/* نطاق السعر */}\n          <div className=\"space-y-2\">\n            <div className=\"border-b border-slate-200 dark:border-slate-700 pb-2 mb-3\">\n              <button\n                onClick={() => toggleSection('price')}\n                className=\"flex items-center justify-between w-full text-left font-medium text-slate-900 dark:text-white\"\n              >\n                <div className=\"flex items-center\">\n                  <Star className=\"h-4 w-4 mr-2 text-primary-500\" />\n                  <span>{t('shop.filters.priceRange')}</span>\n                </div>\n                {expandedSections.price ? (\n                  <ChevronUp className=\"h-4 w-4 text-slate-500\" />\n                ) : (\n                  <ChevronDown className=\"h-4 w-4 text-slate-500\" />\n                )}\n              </button>\n            </div>\n            {expandedSections.price && (\n              <div className=\"mt-4 px-2\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <span className=\"text-sm text-slate-600 dark:text-slate-400\">\n                    {formatCurrency(priceRange[0])}\n                  </span>\n                  <span className=\"text-sm text-slate-600 dark:text-slate-400\">\n                    {formatCurrency(priceRange[1])}\n                  </span>\n                </div>\n                <Slider\n                  min={0}\n                  max={maxPrice}\n                  step={1}\n                  value={priceRange}\n                  onValueChange={setPriceRange}\n                  className=\"my-4\"\n                />\n                <div className=\"flex items-center justify-between gap-4 mt-4\">\n                  <Input\n                    type=\"number\"\n                    min={0}\n                    max={priceRange[1]}\n                    value={priceRange[0]}\n                    onChange={(e) => setPriceRange([parseInt(e.target.value) || 0, priceRange[1]])}\n                    className=\"w-full text-sm\"\n                  />\n                  <span className=\"text-slate-400\">-</span>\n                  <Input\n                    type=\"number\"\n                    min={priceRange[0]}\n                    max={maxPrice}\n                    value={priceRange[1]}\n                    onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value) || maxPrice])}\n                    className=\"w-full text-sm\"\n                  />\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* التوفر والميزات */}\n          <div className=\"space-y-2\">\n            <div className=\"border-b border-slate-200 dark:border-slate-700 pb-2 mb-3\">\n              <button\n                onClick={() => toggleSection('availability')}\n                className=\"flex items-center justify-between w-full text-left font-medium text-slate-900 dark:text-white\"\n              >\n                <div className=\"flex items-center\">\n                  <Filter className=\"h-4 w-4 mr-2 text-primary-500\" />\n                  <span>{currentLanguage === 'ar' ? 'التوفر والميزات' : 'Availability & Features'}</span>\n                </div>\n                {expandedSections.availability ? (\n                  <ChevronUp className=\"h-4 w-4 text-slate-500\" />\n                ) : (\n                  <ChevronDown className=\"h-4 w-4 text-slate-500\" />\n                )}\n              </button>\n            </div>\n            {expandedSections.availability && (\n              <div className=\"space-y-3 mt-3 px-2\">\n                <div className=\"flex items-center\">\n                  <input\n                    id=\"inStock\"\n                    type=\"checkbox\"\n                    checked={filters.inStock}\n                    onChange={(e) => setFilters({ ...filters, inStock: e.target.checked })}\n                    className=\"h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 rounded focus:ring-primary-500\"\n                  />\n                  <label htmlFor=\"inStock\" className=\"ml-2 text-sm text-slate-700 dark:text-slate-300\">\n                    {t('shop.filters.inStock')}\n                  </label>\n                </div>\n                <div className=\"flex items-center\">\n                  <input\n                    id=\"onSale\"\n                    type=\"checkbox\"\n                    checked={filters.onSale}\n                    onChange={(e) => setFilters({ ...filters, onSale: e.target.checked })}\n                    className=\"h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 rounded focus:ring-primary-500\"\n                  />\n                  <label htmlFor=\"onSale\" className=\"ml-2 text-sm text-slate-700 dark:text-slate-300\">\n                    {t('shop.filters.onSale')}\n                  </label>\n                </div>\n                <div className=\"flex items-center\">\n                  <input\n                    id=\"featured\"\n                    type=\"checkbox\"\n                    checked={filters.featured}\n                    onChange={(e) => setFilters({ ...filters, featured: e.target.checked })}\n                    className=\"h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 rounded focus:ring-primary-500\"\n                  />\n                  <label htmlFor=\"featured\" className=\"ml-2 text-sm text-slate-700 dark:text-slate-300\">\n                    {t('shop.filters.featured')}\n                  </label>\n                </div>\n                <div className=\"flex items-center\">\n                  <input\n                    id=\"newArrivals\"\n                    type=\"checkbox\"\n                    checked={filters.newArrivals}\n                    onChange={(e) => setFilters({ ...filters, newArrivals: e.target.checked })}\n                    className=\"h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 rounded focus:ring-primary-500\"\n                  />\n                  <label htmlFor=\"newArrivals\" className=\"ml-2 text-sm text-slate-700 dark:text-slate-300\">\n                    {t('shop.filters.newArrivals')}\n                  </label>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;;;AA/BA;;;;;;;;;;;;AAiCA,yBAAyB;AACzB,MAAM,gBAAgB;IACpB,qBAAqB,qNAAA,CAAA,aAAU;IAC/B,mBAAmB,mMAAA,CAAA,MAAG;IACtB,0BAA0B,2MAAA,CAAA,UAAO;IACjC,oBAAoB,mMAAA,CAAA,MAAG;IACvB,kBAAkB,uMAAA,CAAA,QAAK;IACvB,gBAAgB,yMAAA,CAAA,SAAM;IACtB,OAAO,2MAAA,CAAA,UAAO;AAChB;AAcO,SAAS,uBAAuB,EACrC,OAAO,EACP,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,iBAAiB,EACjB,iBAAiB,EACjB,oBAAoB,EACpB,kBAAkB,EAClB,OAAO,EAAE,EACmB;;IAC5B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD;IACpC,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IACnC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvD,YAAY;QACZ,OAAO;QACP,cAAc;QACd,QAAQ;QACR,MAAM;IACR;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAC,QAAQ,UAAU,CAAC,GAAG;QAAE,QAAQ,UAAU,CAAC,GAAG;KAAC;IAE7F,uCAAuC;IACvC,MAAM,kBAAkB,AAAC,UAA0B;IACnD,MAAM,QAAQ,oBAAoB;IAElC,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,aAAa,MAAM,GAAG,GAAG;gBAC3B;wDAAW,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,MAAM;wBAAa,CAAC;;YACrD,OAAO;gBACL;wDAAW,CAAA;wBACT,MAAM,aAAa;4BAAE,GAAG,IAAI;wBAAC;wBAC7B,OAAO,WAAW,IAAI;wBACtB,OAAO;oBACT;;YACF;QACF;2CAAG;QAAC;QAAc;KAAW;IAE7B,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,mBAAmB,MAAM;gBAC3B;wDAAW,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,QAAQ;wBAAe,CAAC;;YACzD,OAAO;gBACL;wDAAW,CAAA;wBACT,MAAM,aAAa;4BAAE,GAAG,IAAI;wBAAC;wBAC7B,OAAO,WAAW,MAAM;wBACxB,OAAO;oBACT;;YACF;QACF;2CAAG;QAAC;QAAgB;KAAW;IAE/B,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR;oDAAW,CAAA,OAAQ,CAAC;wBAClB,GAAG,IAAI;wBACP,YAAY;4BAAE,KAAK,UAAU,CAAC,EAAE;4BAAE,KAAK,UAAU,CAAC,EAAE;wBAAC;oBACvD,CAAC;;QACH;2CAAG;QAAC;QAAY;KAAW;IAE3B,yBAAyB;IACzB,MAAM,gBAAgB,CAAC;QACrB,oBAAoB,CAAA,OAAQ,CAAC;gBAC3B,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ;YAC3B,CAAC;IACH;IAEA,uCAAuC;IACvC,MAAM,YAAY,CAAC;QACjB,gBAAgB,CAAA,OACd,KAAK,QAAQ,CAAC,OACV,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM,OACvB;mBAAI;gBAAM;aAAI;IAEtB;IAEA,gBAAgB;IAChB,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB,CAAA,OAAQ,SAAS,SAAS,OAAO;IACrD;IAEA,2BAA2B;IAC3B,MAAM,qBAAqB;QACzB,gBAAgB,EAAE;QAClB,kBAAkB;QAClB,cAAc;YAAC;YAAG;SAAS;QAC3B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,oBAAoB,OAAO,mBAAmB;;;;;;4BAEhD,qBAAqB,mBACpB,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;0CAChC;;;;;;;;;;;;kCAIP,6LAAC;wBAAI,WAAU;;4BACZ,qBAAqB,mBACpB,6LAAC,sIAAA,CAAA,UAAO;gCAAC,SAAS,oBAAoB,OAAO,wBAAwB;0CACnE,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;;sDAEV,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAK,WAAU;sDACb,oBAAoB,OAAO,gBAAgB;;;;;;;;;;;;;;;;;0CAKpD,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,qBAAqB,CAAC;gCACrC,WAAU;;kDAEV,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDACE,oBACI,oBAAoB,OAAO,kBAAkB,iBAC7C,oBAAoB,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;0BAOxD,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,6IACA,oBAAoB,+BAA+B;0BAEnD,cAAA,6LAAC;oBAAI,WAAU;;wBAEZ,qBAAqB,mBACpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDACb,oBAAoB,OAAO,oBAAoB;;;;;;oCAEjD,QAAQ,QAAQ,KAAK,uBACpB,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;0DACnC,6LAAC;;oDACE,oBAAoB,OAAO,YAAY;oDACvC,kBAAkB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,QAAQ,GAAG,IAAI,CAAC,gBAAgB,IAAI,QAAQ,QAAQ;;;;;;;0DAEpG,6LAAC,+LAAA,CAAA,IAAC;gDACA,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,UAAU;wDAAM,CAAC;gDAClD;;;;;;;;;;;;oCAIL,QAAQ,WAAW,kBAClB,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;0DACnC,6LAAC;;oDACE,oBAAoB,OAAO,UAAU;oDACrC,QAAQ,WAAW;;;;;;;0DAEtB,6LAAC,+LAAA,CAAA,IAAC;gDACA,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,aAAa;wDAAG,CAAC;gDAClD;;;;;;;;;;;;oCAIL,CAAC,QAAQ,UAAU,CAAC,GAAG,GAAG,KAAK,QAAQ,UAAU,CAAC,GAAG,GAAG,QAAQ,mBAC/D,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;0DACnC,6LAAC;;oDACE,oBAAoB,OAAO,YAAY;oDACvC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,UAAU,CAAC,GAAG;oDAAE;oDAAI,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,UAAU,CAAC,GAAG;;;;;;;0DAEnF,6LAAC,+LAAA,CAAA,IAAC;gDACA,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,cAAc;wDAAC;wDAAG;qDAAS;oDAC3B,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,YAAY;gEAAE,KAAK;gEAAG,KAAK;4DAAS;wDAAE,CAAC;gDACxE;;;;;;;;;;;;oCAIL,QAAQ,OAAO,kBACd,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;0DACnC,6LAAC;0DAAM,oBAAoB,OAAO,qBAAqB;;;;;;0DACvD,6LAAC,+LAAA,CAAA,IAAC;gDACA,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,SAAS;wDAAM,CAAC;gDACjD;;;;;;;;;;;;oCAIL,QAAQ,MAAM,kBACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;0DACnC,6LAAC;0DAAM,oBAAoB,OAAO,WAAW;;;;;;0DAC7C,6LAAC,+LAAA,CAAA,IAAC;gDACA,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,QAAQ;wDAAM,CAAC;gDAChD;;;;;;;;;;;;oCAIL,QAAQ,QAAQ,kBACf,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;0DACnC,6LAAC;0DAAM,oBAAoB,OAAO,qBAAqB;;;;;;0DACvD,6LAAC,+LAAA,CAAA,IAAC;gDACA,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,UAAU;wDAAM,CAAC;gDAClD;;;;;;;;;;;;oCAIL,QAAQ,WAAW,kBAClB,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;0DACnC,6LAAC;0DAAM,oBAAoB,OAAO,eAAe;;;;;;0DACjD,6LAAC,+LAAA,CAAA,IAAC;gDACA,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,aAAa;wDAAM,CAAC;gDACrD;;;;;;;;;;;;kDAIN,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;0DAAM,oBAAoB,OAAO,aAAa;;;;;;;;;;;;;;;;;;;;;;;sCAMvD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAK,WAAU;sDACb,oBAAoB,OAAO,sBAAsB;;;;;;;;;;;;8CAGtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,aAAa,oBAAoB,OAAO,sBAAsB;4CAC9D,OAAO,QAAQ,WAAW;4CAC1B,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACtE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oGACA,4FACA;;;;;;sDAGJ,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAClB,0FACA,QAAQ,YAAY;;;;;;wCAErB,QAAQ,WAAW,kBAClB,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,aAAa;gDAAG;4CACxD,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iGACA,QAAQ,WAAW;4CAErB,cAAY,oBAAoB,OAAO,cAAc;sDAErD,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAOrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,6LAAC;kEAAM,EAAE;;;;;;;;;;;;4CAEV,iBAAiB,UAAU,iBAC1B,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;qEAErB,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;gCAI5B,iBAAiB,UAAU,kBAC1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2JAAA,CAAA,iBAAc;4CAAC,WAAU;sDACxB,cAAA,6LAAC;gDACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uFACA,gFACA,QAAQ,QAAQ,KAAK,QACjB,2HACA;gDAEN,SAAS,IAAM,WAAW;wDAAE,GAAG,OAAO;wDAAE,UAAU;oDAAM;;kEAExD,6LAAC;wDAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,4DACA,QAAQ,QAAQ,KAAK,QACjB,0CACA;kEAEJ,cAAA,6LAAC,2MAAA,CAAA,UAAO;4DAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACnB,WACA,QAAQ,QAAQ,KAAK,QACjB,2CACA;;;;;;;;;;;kEAGR,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEACb,EAAE;;;;;;;;;;;kEAGP,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACjB,8BACA,QAAQ,QAAQ,KAAK,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;wCAIlD,kBAAkB,GAAG,CAAC,CAAA;4CACrB,MAAM,gBAAgB,aAAa,CAAC,SAAS,EAAE,CAA+B,IAAI,2MAAA,CAAA,UAAO;4CACzF,qBACE,6LAAC,2JAAA,CAAA,iBAAc;gDAAmB,WAAU;0DAC1C,cAAA,6LAAC;oDACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uFACA,gFACA,QAAQ,QAAQ,KAAK,SAAS,EAAE,GAC5B,2HACA;oDAEN,SAAS,IAAM,WAAW;4DAAE,GAAG,OAAO;4DAAE,UAAU,SAAS,EAAE;wDAAC;;sEAE9D,6LAAC;4DAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,4DACA,QAAQ,QAAQ,KAAK,SAAS,EAAE,GAC5B,0CACA;sEAEJ,cAAA,6LAAC;gEAAc,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACzB,WACA,QAAQ,QAAQ,KAAK,SAAS,EAAE,GAC5B,2CACA;;;;;;;;;;;sEAGR,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EACb,SAAS,IAAI,CAAC,gBAAgB;;;;;;8EAEjC,6LAAC;oEAAE,WAAU;8EACV,SAAS,WAAW,CAAC,gBAAgB;;;;;;;;;;;;sEAG1C,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACjB,8BACA,QAAQ,QAAQ,KAAK,SAAS,EAAE,GAAG,gBAAgB;;;;;;;;;;;;+CAlCpC,SAAS,EAAE;;;;;wCAuCpC;;;;;;;;;;;;;sCAMN,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;kEAAM,EAAE;;;;;;;;;;;;4CAEV,iBAAiB,KAAK,iBACrB,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;qEAErB,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;gCAI5B,iBAAiB,KAAK,kBACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,CAAC,EAAE;;;;;;8DAE/B,6LAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,CAAC,EAAE;;;;;;;;;;;;sDAGjC,6LAAC,qIAAA,CAAA,SAAM;4CACL,KAAK;4CACL,KAAK;4CACL,MAAM;4CACN,OAAO;4CACP,eAAe;4CACf,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,KAAK;oDACL,KAAK,UAAU,CAAC,EAAE;oDAClB,OAAO,UAAU,CAAC,EAAE;oDACpB,UAAU,CAAC,IAAM,cAAc;4DAAC,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4DAAG,UAAU,CAAC,EAAE;yDAAC;oDAC7E,WAAU;;;;;;8DAEZ,6LAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,KAAK,UAAU,CAAC,EAAE;oDAClB,KAAK;oDACL,OAAO,UAAU,CAAC,EAAE;oDACpB,UAAU,CAAC,IAAM,cAAc;4DAAC,UAAU,CAAC,EAAE;4DAAE,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;yDAAS;oDACpF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAQpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;kEAAM,oBAAoB,OAAO,oBAAoB;;;;;;;;;;;;4CAEvD,iBAAiB,YAAY,iBAC5B,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;qEAErB,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;gCAI5B,iBAAiB,YAAY,kBAC5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,SAAS,QAAQ,OAAO;oDACxB,UAAU,CAAC,IAAM,WAAW;4DAAE,GAAG,OAAO;4DAAE,SAAS,EAAE,MAAM,CAAC,OAAO;wDAAC;oDACpE,WAAU;;;;;;8DAEZ,6LAAC;oDAAM,SAAQ;oDAAU,WAAU;8DAChC,EAAE;;;;;;;;;;;;sDAGP,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,SAAS,QAAQ,MAAM;oDACvB,UAAU,CAAC,IAAM,WAAW;4DAAE,GAAG,OAAO;4DAAE,QAAQ,EAAE,MAAM,CAAC,OAAO;wDAAC;oDACnE,WAAU;;;;;;8DAEZ,6LAAC;oDAAM,SAAQ;oDAAS,WAAU;8DAC/B,EAAE;;;;;;;;;;;;sDAGP,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,SAAS,QAAQ,QAAQ;oDACzB,UAAU,CAAC,IAAM,WAAW;4DAAE,GAAG,OAAO;4DAAE,UAAU,EAAE,MAAM,CAAC,OAAO;wDAAC;oDACrE,WAAU;;;;;;8DAEZ,6LAAC;oDAAM,SAAQ;oDAAW,WAAU;8DACjC,EAAE;;;;;;;;;;;;sDAGP,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,SAAS,QAAQ,WAAW;oDAC5B,UAAU,CAAC,IAAM,WAAW;4DAAE,GAAG,OAAO;4DAAE,aAAa,EAAE,MAAM,CAAC,OAAO;wDAAC;oDACxE,WAAU;;;;;;8DAEZ,6LAAC;oDAAM,SAAQ;oDAAc,WAAU;8DACpC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvB;GAthBgB;;QAWO,iIAAA,CAAA,mBAAgB;QACf,+HAAA,CAAA,iBAAc;;;KAZtB", "debugId": null}}, {"offset": {"line": 3302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/shop/ShopFooter.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Mail, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';\nimport { Button } from '../ui/Button';\nimport { Input } from '../ui/Input';\nimport { useLanguageStore } from '../../stores/languageStore';\nimport { useTranslation } from '../../translations';\nimport { useThemeStore } from '../../stores/themeStore';\nimport { cn } from '../../lib/utils';\nimport { ScrollAnimation } from '../ui/animations';\n\ninterface ShopFooterProps {\n  totalProducts: number;\n  currentPage: number;\n  itemsPerPage: number;\n  onPageChange: (page: number) => void;\n}\n\nexport function ShopFooter({\n  totalProducts,\n  currentPage,\n  itemsPerPage,\n  onPageChange\n}: ShopFooterProps) {\n  const { language } = useLanguageStore();\n  const { t, locale } = useTranslation();\n  const { isDarkMode } = useThemeStore();\n\n\n  // استخدام اللغة من المسار أو من المتجر\n  const currentLanguage = (locale as 'ar' | 'en') || language;\n  const isRTL = currentLanguage === 'ar';\n\n  // حساب إجمالي عدد الصفحات\n  const totalPages = Math.ceil(totalProducts / itemsPerPage);\n  \n  // إنشاء مصفوفة أرقام الصفحات\n  const getPageNumbers = () => {\n    const pageNumbers = [];\n    const maxPagesToShow = 5;\n    \n    if (totalPages <= maxPagesToShow) {\n      // إذا كان إجمالي عدد الصفحات أقل من أو يساوي الحد الأقصى، عرض جميع الصفحات\n      for (let i = 1; i <= totalPages; i++) {\n        pageNumbers.push(i);\n      }\n    } else {\n      // إذا كان إجمالي عدد الصفحات أكبر من الحد الأقصى\n      let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));\n      let endPage = startPage + maxPagesToShow - 1;\n      \n      if (endPage > totalPages) {\n        endPage = totalPages;\n        startPage = Math.max(1, endPage - maxPagesToShow + 1);\n      }\n      \n      for (let i = startPage; i <= endPage; i++) {\n        pageNumbers.push(i);\n      }\n      \n      // إضافة \"...\" إذا لزم الأمر\n      if (startPage > 1) {\n        pageNumbers.unshift(-1); // استخدام -1 لتمثيل \"...\"\n        pageNumbers.unshift(1); // دائمًا إضافة الصفحة الأولى\n      }\n      \n      if (endPage < totalPages) {\n        pageNumbers.push(-2); // استخدام -2 لتمثيل \"...\" الثاني\n        pageNumbers.push(totalPages); // دائمًا إضافة الصفحة الأخيرة\n      }\n    }\n    \n    return pageNumbers;\n  };\n\n\n\n  return (\n    <div className=\"mt-12\">\n      {/* ترقيم الصفحات */}\n      {totalPages > 1 && (\n        <ScrollAnimation animation=\"fade\" delay={0.1}>\n          <div className=\"flex justify-center my-8\">\n            <div className={cn(\n              \"flex items-center gap-1 rounded-lg p-1\",\n              \"bg-white dark:bg-slate-800 shadow-md\"\n            )}>\n              {/* زر الصفحة الأولى */}\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => onPageChange(1)}\n                disabled={currentPage === 1}\n                className=\"h-9 w-9\"\n                aria-label={currentLanguage === 'ar' ? 'الصفحة الأولى' : 'First page'}\n              >\n                <ChevronsLeft className={cn(\"h-4 w-4\", isRTL && \"rotate-180\")} />\n              </Button>\n              \n              {/* زر الصفحة السابقة */}\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => onPageChange(currentPage - 1)}\n                disabled={currentPage === 1}\n                className=\"h-9 w-9\"\n                aria-label={currentLanguage === 'ar' ? 'الصفحة السابقة' : 'Previous page'}\n              >\n                <ChevronLeft className={cn(\"h-4 w-4\", isRTL && \"rotate-180\")} />\n              </Button>\n              \n              {/* أرقام الصفحات */}\n              {getPageNumbers().map((pageNumber, index) => (\n                pageNumber < 0 ? (\n                  // عرض \"...\" للصفحات المحذوفة\n                  <span key={`ellipsis-${index}`} className=\"px-2 text-slate-500 dark:text-slate-400\">\n                    ...\n                  </span>\n                ) : (\n                  // زر رقم الصفحة\n                  <Button\n                    key={`page-${pageNumber}`}\n                    variant={currentPage === pageNumber ? \"default\" : \"ghost\"}\n                    onClick={() => onPageChange(pageNumber)}\n                    className={cn(\n                      \"h-9 w-9 rounded-md\",\n                      currentPage === pageNumber && \"bg-primary-500 text-white hover:bg-primary-600\"\n                    )}\n                  >\n                    {pageNumber}\n                  </Button>\n                )\n              ))}\n              \n              {/* زر الصفحة التالية */}\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => onPageChange(currentPage + 1)}\n                disabled={currentPage === totalPages}\n                className=\"h-9 w-9\"\n                aria-label={currentLanguage === 'ar' ? 'الصفحة التالية' : 'Next page'}\n              >\n                <ChevronRight className={cn(\"h-4 w-4\", isRTL && \"rotate-180\")} />\n              </Button>\n              \n              {/* زر الصفحة الأخيرة */}\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => onPageChange(totalPages)}\n                disabled={currentPage === totalPages}\n                className=\"h-9 w-9\"\n                aria-label={currentLanguage === 'ar' ? 'الصفحة الأخيرة' : 'Last page'}\n              >\n                <ChevronsRight className={cn(\"h-4 w-4\", isRTL && \"rotate-180\")} />\n              </Button>\n            </div>\n          </div>\n        </ScrollAnimation>\n      )}\n\n\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;;;AAVA;;;;;;;;AAmBO,SAAS,WAAW,EACzB,aAAa,EACb,WAAW,EACX,YAAY,EACZ,YAAY,EACI;;IAChB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD;IACpC,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IACnC,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD;IAGnC,uCAAuC;IACvC,MAAM,kBAAkB,AAAC,UAA0B;IACnD,MAAM,QAAQ,oBAAoB;IAElC,0BAA0B;IAC1B,MAAM,aAAa,KAAK,IAAI,CAAC,gBAAgB;IAE7C,6BAA6B;IAC7B,MAAM,iBAAiB;QACrB,MAAM,cAAc,EAAE;QACtB,MAAM,iBAAiB;QAEvB,IAAI,cAAc,gBAAgB;YAChC,2EAA2E;YAC3E,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;gBACpC,YAAY,IAAI,CAAC;YACnB;QACF,OAAO;YACL,iDAAiD;YACjD,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,iBAAiB;YACtE,IAAI,UAAU,YAAY,iBAAiB;YAE3C,IAAI,UAAU,YAAY;gBACxB,UAAU;gBACV,YAAY,KAAK,GAAG,CAAC,GAAG,UAAU,iBAAiB;YACrD;YAEA,IAAK,IAAI,IAAI,WAAW,KAAK,SAAS,IAAK;gBACzC,YAAY,IAAI,CAAC;YACnB;YAEA,4BAA4B;YAC5B,IAAI,YAAY,GAAG;gBACjB,YAAY,OAAO,CAAC,CAAC,IAAI,0BAA0B;gBACnD,YAAY,OAAO,CAAC,IAAI,6BAA6B;YACvD;YAEA,IAAI,UAAU,YAAY;gBACxB,YAAY,IAAI,CAAC,CAAC,IAAI,iCAAiC;gBACvD,YAAY,IAAI,CAAC,aAAa,8BAA8B;YAC9D;QACF;QAEA,OAAO;IACT;IAIA,qBACE,6LAAC;QAAI,WAAU;kBAEZ,aAAa,mBACZ,6LAAC,4JAAA,CAAA,kBAAe;YAAC,WAAU;YAAO,OAAO;sBACvC,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,0CACA;;sCAGA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,aAAa;4BAC5B,UAAU,gBAAgB;4BAC1B,WAAU;4BACV,cAAY,oBAAoB,OAAO,kBAAkB;sCAEzD,cAAA,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,SAAS;;;;;;;;;;;sCAIlD,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,aAAa,cAAc;4BAC1C,UAAU,gBAAgB;4BAC1B,WAAU;4BACV,cAAY,oBAAoB,OAAO,mBAAmB;sCAE1D,cAAA,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,SAAS;;;;;;;;;;;wBAIhD,iBAAiB,GAAG,CAAC,CAAC,YAAY,QACjC,aAAa,IACX,6BAA6B;0CAC7B,6LAAC;gCAA+B,WAAU;0CAA0C;+BAAzE,CAAC,SAAS,EAAE,OAAO;;;;uCAI9B,gBAAgB;0CAChB,6LAAC,qIAAA,CAAA,SAAM;gCAEL,SAAS,gBAAgB,aAAa,YAAY;gCAClD,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,cAAc;0CAG/B;+BARI,CAAC,KAAK,EAAE,YAAY;;;;;sCAc/B,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,aAAa,cAAc;4BAC1C,UAAU,gBAAgB;4BAC1B,WAAU;4BACV,cAAY,oBAAoB,OAAO,mBAAmB;sCAE1D,cAAA,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,SAAS;;;;;;;;;;;sCAIlD,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,aAAa;4BAC5B,UAAU,gBAAgB;4BAC1B,WAAU;4BACV,cAAY,oBAAoB,OAAO,mBAAmB;sCAE1D,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjE;GAnJgB;;QAMO,iIAAA,CAAA,mBAAgB;QACf,+HAAA,CAAA,iBAAc;QACb,8HAAA,CAAA,gBAAa;;;KARtB", "debugId": null}}, {"offset": {"line": 3515, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/product/ProductCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, memo } from 'react';\nimport Link from 'next/link';\nimport { Heart, ShoppingCart, Eye, Star, Award, Truck, Shield, Users } from 'lucide-react';\nimport { Button } from '../ui/Button';\nimport { Card } from '../ui/Card';\nimport { EnhancedImage } from '../ui/EnhancedImage';\nimport { useCartStore } from '../../stores/cartStore';\nimport { useWishlistStore } from '../../stores/wishlistStore';\n\nimport { formatCurrency, cn } from '../../lib/utils';\nimport { Product } from '../../types/index';\nimport { useTranslation } from '../../translations';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useTheme } from 'next-themes';\n\ninterface ProductCardProps {\n  product: Product;\n  index?: number;\n  className?: string;\n  showQuickView?: boolean;\n  showAddToCart?: boolean;\n  showWishlist?: boolean;\n  showCompare?: boolean;\n  showShare?: boolean;\n  showWholesale?: boolean;\n  variant?: 'default' | 'compact' | 'featured' | 'list';\n  priority?: boolean;\n  onQuickView?: (product: Product) => void;\n  onAddToCart?: (product: Product) => void;\n  onToggleWishlist?: (product: Product) => void;\n  onCompare?: (product: Product) => void;\n  onShare?: (product: Product) => void;\n  onRequestWholesale?: (product: Product) => void;\n}\n\nconst ProductCard = memo(function ProductCard({\n  product,\n  index = 0,\n  className = '',\n  showQuickView = true,\n  showAddToCart = true,\n  showWishlist = true,\n  showCompare = false,\n  showShare = false,\n  showWholesale = true,\n  variant = 'default',\n  priority = false,\n  onQuickView,\n  onAddToCart,\n  onToggleWishlist,\n  onCompare,\n  onShare,\n  onRequestWholesale,\n}: ProductCardProps) {\n  const { language } = useTranslation();\n  const { theme } = useTheme();\n  const cartStore = useCartStore();\n  const wishlistStore = useWishlistStore();\n  const [isHovered, setIsHovered] = useState(false);\n\n  const isDarkMode = theme === 'dark';\n  const currentLanguage = language;\n\n  const handleAddToCart = (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    // استخدام الدالة المخصصة إذا تم تمريرها، وإلا استخدام السلوك الافتراضي\n    if (onAddToCart) {\n      onAddToCart(product);\n    } else {\n      cartStore.addItem(product, 1);\n    }\n  };\n\n  const handleToggleWishlist = (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    // استخدام الدالة المخصصة إذا تم تمريرها، وإلا استخدام السلوك الافتراضي\n    if (onToggleWishlist) {\n      onToggleWishlist(product);\n    } else {\n      if (wishlistStore.isInWishlist(product.id.toString())) {\n        wishlistStore.removeItem(product.id.toString());\n      } else {\n        wishlistStore.addItem(product);\n      }\n    }\n  };\n\n  const handleQuickView = (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (onQuickView) {\n      onQuickView(product);\n    }\n  };\n\n  const handleCompare = (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (onCompare) {\n      onCompare(product);\n    }\n  };\n\n  const handleShare = (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (onShare) {\n      onShare(product);\n    }\n  };\n\n  // Fallback image if the product image fails to load\n  const fallbackImage = `/images/product-placeholder-${isDarkMode ? 'dark' : 'light'}.svg`;\n\n  // Use the first product image or fallback if there are no images\n  const productImage = !product.images || product.images.length === 0\n    ? fallbackImage\n    : product.images[0];\n\n  // تحديد ما إذا كان المنتج في المخزون\n  const isInStock = product.inventoryStatus === 'in-stock' || product.inventoryStatus === 'low-stock';\n\n  // حساب نسبة الخصم إذا كان هناك سعر مقارنة\n  const discountPercentage = product.compareAtPrice && product.compareAtPrice > product.price\n    ? Math.round((1 - product.price / product.compareAtPrice) * 100)\n    : product.discount || 0;\n\n  // تحديد أسلوب العرض حسب النوع\n  const getCardStyles = () => {\n    switch (variant) {\n      case 'compact':\n        return 'h-80';\n      case 'featured':\n        return 'h-96 ring-2 ring-primary-200 dark:ring-primary-800';\n      case 'list':\n        return 'flex-row h-40 min-h-[160px]';\n      default:\n        return 'h-full';\n    }\n  };\n\n  // تحديد أولوية التحميل\n  const shouldPrioritize = priority || index < 4;\n\n\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3, delay: index * 0.1 }}\n      onHoverStart={() => setIsHovered(true)}\n      onHoverEnd={() => setIsHovered(false)}\n      className={cn(\"group relative\", className)}\n    >\n      <Card\n        className={cn(\n          \"flex flex-col overflow-hidden rounded-2xl shadow-xl transition-all duration-500 hover:shadow-2xl dark:bg-slate-800 border border-slate-200/50 dark:border-slate-700/50\",\n          \"bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900\",\n          \"hover:scale-105 hover:-translate-y-2\",\n          getCardStyles(),\n          variant === 'featured' && \"ring-2 ring-primary-200 dark:ring-primary-800 shadow-primary-100 dark:shadow-primary-900/20\",\n          className\n        )}\n      >\n        <div className=\"relative overflow-hidden\">\n          <Link href={`/${currentLanguage}/shop/product/${product.slug}`}>\n            <div className={cn(\n              \"relative overflow-hidden bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-700 dark:to-slate-800\",\n              variant === 'list' ? \"w-32 h-32 rounded-xl\" : \"w-full h-52 rounded-t-2xl\"\n            )}>\n              <EnhancedImage\n                src={productImage}\n                alt={product.name}\n                fill={true}\n                objectFit=\"cover\"\n                progressive={true}\n                placeholder=\"blur\"\n                className=\"transition-all duration-500 group-hover:scale-110\"\n                sizes=\"(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw\"\n                priority={shouldPrioritize}\n              />\n\n              {/* Enhanced gradient overlay on hover */}\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: isHovered ? 1 : 0 }}\n                transition={{ duration: 0.3 }}\n                className=\"absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent\"\n              />\n\n\n            </div>\n          </Link>\n\n          {/* Enhanced Product badges */}\n          <div className=\"absolute top-4 left-4 flex flex-col gap-3 z-20\">\n            <AnimatePresence>\n              {product.featured && (\n                <motion.div\n                  initial={{ scale: 0, opacity: 0 }}\n                  animate={{ scale: 1, opacity: 1 }}\n                  exit={{ scale: 0, opacity: 0 }}\n                  className=\"px-4 py-2 bg-gradient-to-r from-amber-400 to-orange-500 text-white rounded-full shadow-xl backdrop-blur-md flex items-center gap-2 border border-white/20\"\n                >\n                  <Award className=\"w-4 h-4\" />\n                  <span className=\"text-sm font-bold\">\n                    {currentLanguage === 'ar' ? 'مميز' : 'Featured'}\n                  </span>\n                </motion.div>\n              )}\n              {product.isNew && (\n                <motion.div\n                  initial={{ scale: 0, opacity: 0 }}\n                  animate={{ scale: 1, opacity: 1 }}\n                  exit={{ scale: 0, opacity: 0 }}\n                  className=\"px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-full shadow-xl backdrop-blur-md border border-white/20\"\n                >\n                  <span className=\"text-sm font-bold\">\n                    {currentLanguage === 'ar' ? 'جديد' : 'New'}\n                  </span>\n                </motion.div>\n              )}\n              {discountPercentage > 0 && (\n                <motion.div\n                  initial={{ scale: 0, opacity: 0 }}\n                  animate={{ scale: 1, opacity: 1 }}\n                  exit={{ scale: 0, opacity: 0 }}\n                  className=\"px-4 py-2 bg-gradient-to-r from-red-500 to-pink-600 text-white rounded-full shadow-xl backdrop-blur-md border border-white/20\"\n                >\n                  <span className=\"text-sm font-bold\">\n                    {currentLanguage === 'ar' ? `${discountPercentage}% خصم` : `${discountPercentage}% OFF`}\n                  </span>\n                </motion.div>\n              )}\n              {!isInStock && (\n                <motion.div\n                  initial={{ scale: 0, opacity: 0 }}\n                  animate={{ scale: 1, opacity: 1 }}\n                  exit={{ scale: 0, opacity: 0 }}\n                  className=\"px-4 py-2 bg-gradient-to-r from-slate-500 to-slate-600 text-white rounded-full shadow-xl backdrop-blur-md border border-white/20\"\n                >\n                  <span className=\"text-sm font-bold\">\n                    {currentLanguage === 'ar' ? 'نفذ المخزون' : 'Out of Stock'}\n                  </span>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n\n          {/* Enhanced Wishlist Button */}\n          <div className=\"absolute top-4 right-4 z-20\">\n            {showWishlist && (\n              <motion.div\n                initial={{ opacity: 0, scale: 0 }}\n                animate={{ opacity: 1, scale: 1 }}\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.95 }}\n                transition={{ duration: 0.3 }}\n              >\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className={cn(\n                    \"p-4 rounded-full shadow-xl backdrop-blur-md transition-all duration-300 border\",\n                    wishlistStore.isInWishlist(product.id.toString())\n                      ? \"bg-red-500 border-red-400 text-white hover:bg-red-600 shadow-red-500/30\"\n                      : \"bg-white/90 dark:bg-slate-900/90 border-white/20 text-slate-700 dark:text-slate-300 hover:bg-red-500 hover:text-white hover:border-red-400\"\n                  )}\n                  onClick={handleToggleWishlist}\n                  aria-label={currentLanguage === 'ar' ? 'إضافة إلى المفضلة' : 'Add to wishlist'}\n                >\n                  <Heart\n                    className={cn(\n                      \"h-5 w-5 transition-all duration-300\",\n                      wishlistStore.isInWishlist(product.id.toString()) && \"fill-current\"\n                    )}\n                  />\n                </Button>\n              </motion.div>\n            )}\n          </div>\n\n        </div>\n\n        <div className={cn(\n          \"p-6 flex flex-col flex-grow\",\n          variant === 'list' && \"flex-row items-center p-4 gap-4\"\n        )}>\n          {variant === 'list' ? (\n            // List view layout\n            <>\n              <div className=\"flex-1 min-w-0\">\n                {/* Product name */}\n                <Link href={`/${currentLanguage}/shop/product/${product.slug}`} className=\"block group/title\">\n                  <h3 className=\"font-bold text-base text-slate-900 dark:text-white mb-1 group-hover/title:text-primary-600 dark:group-hover/title:text-primary-400 transition-colors duration-300 line-clamp-1\">\n                    {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}\n                  </h3>\n                </Link>\n\n                {/* Product description */}\n                <p className=\"text-sm text-slate-600 dark:text-slate-300 mb-2 line-clamp-2 leading-relaxed\">\n                  {currentLanguage === 'ar'\n                    ? (product.description_ar || product.description)\n                    : product.description}\n                </p>\n\n                {/* Category & Rating */}\n                <div className=\"flex items-center gap-4 mb-2\">\n                  {product.category && (\n                    <span className=\"text-xs px-2 py-1 bg-gradient-to-r from-primary-50 to-primary-100 text-primary-700 dark:from-primary-900/30 dark:to-primary-800/30 dark:text-primary-300 rounded-full font-medium\">\n                      {product.category}\n                    </span>\n                  )}\n\n                  <div className=\"flex items-center gap-1\">\n                    <div className=\"flex items-center\">\n                      {[...Array(5)].map((_, i) => (\n                        <Star\n                          key={i}\n                          className={cn(\n                            \"h-3 w-3 transition-colors duration-200\",\n                            i < Math.floor(product.rating || 0)\n                              ? \"text-yellow-400 fill-current\"\n                              : \"text-slate-300 dark:text-slate-600\"\n                          )}\n                        />\n                      ))}\n                    </div>\n                    <span className=\"text-xs text-slate-600 dark:text-slate-400\">\n                      {product.rating?.toFixed(1) ?? 'N/A'}\n                      {product.reviewCount ? ` (${product.reviewCount})` : ''}\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"flex items-center gap-4\">\n                {/* Price */}\n                <div className=\"text-right\">\n                  <div className=\"flex items-baseline gap-2\">\n                    <span className=\"font-bold text-base text-slate-900 dark:text-white\">\n                      {formatCurrency(product.price)}\n                    </span>\n                    {product.compareAtPrice && product.compareAtPrice > product.price && (\n                      <span className=\"text-sm text-slate-500 line-through\">\n                        {formatCurrency(product.compareAtPrice)}\n                      </span>\n                    )}\n                  </div>\n                  <div className={cn(\n                    \"text-xs font-medium px-2 py-1 rounded-full mt-1\",\n                    isInStock\n                      ? \"text-green-700 bg-green-100 dark:text-green-400 dark:bg-green-900/30\"\n                      : \"text-red-700 bg-red-100 dark:text-red-400 dark:bg-red-900/30\"\n                  )}>\n                    {isInStock ? (\n                      currentLanguage === 'ar' ? 'متوفر' : 'In Stock'\n                    ) : (\n                      currentLanguage === 'ar' ? 'نفذ المخزون' : 'Out of Stock'\n                    )}\n                  </div>\n                </div>\n\n                {/* Actions */}\n                <div className=\"flex gap-2\">\n                  {showAddToCart && (\n                    <Button\n                      variant=\"primary\"\n                      size=\"sm\"\n                      className={cn(\n                        \"rounded-xl font-medium shadow-lg transition-all duration-300\",\n                        \"bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700\",\n                        !isInStock && \"opacity-50 cursor-not-allowed\"\n                      )}\n                      onClick={handleAddToCart}\n                      disabled={!isInStock}\n                      aria-label={currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}\n                    >\n                      <ShoppingCart className=\"h-4 w-4 mr-2\" />\n                      {currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}\n                    </Button>\n                  )}\n\n                  {/* B2B button for list view */}\n                  {showWholesale && onRequestWholesale && (\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      className=\"rounded-xl border-2 hover:border-slate-300 dark:hover:border-slate-600 transition-all duration-300 bg-white dark:bg-slate-800 px-3\"\n                      onClick={(e) => {\n                        e.preventDefault();\n                        e.stopPropagation();\n                        try {\n                          onRequestWholesale(product);\n                        } catch (error) {\n                          console.error('Error calling onRequestWholesale:', error);\n                        }\n                      }}\n                      aria-label={currentLanguage === 'ar' ? 'طلب جملة' : 'B2B Request'}\n                    >\n                      <Users className=\"h-4 w-4 mr-1\" />\n                      <span className=\"text-sm font-bold\">B2B</span>\n                    </Button>\n                  )}\n                </div>\n              </div>\n            </>\n          ) : (\n            // Grid view layout\n            <>\n              {/* Category & Rating */}\n              <div className=\"flex items-center justify-between mb-3\">\n                {product.category && (\n                  <motion.span\n                    whileHover={{ scale: 1.05 }}\n                    className=\"text-xs px-3 py-1 bg-gradient-to-r from-primary-50 to-primary-100 text-primary-700 dark:from-primary-900/30 dark:to-primary-800/30 dark:text-primary-300 rounded-full font-medium border border-primary-200/50 dark:border-primary-700/50\"\n                  >\n                    {product.category}\n                  </motion.span>\n                )}\n\n                <div className=\"flex items-center gap-1\">\n                  <div className=\"flex items-center\">\n                    {[...Array(5)].map((_, i) => (\n                      <Star\n                        key={i}\n                        className={cn(\n                          \"h-4 w-4 transition-colors duration-200\",\n                          i < Math.floor(product.rating || 0)\n                            ? \"text-yellow-400 fill-current\"\n                            : \"text-slate-300 dark:text-slate-600\"\n                        )}\n                      />\n                    ))}\n                  </div>\n                  <span className=\"text-sm text-slate-600 dark:text-slate-400 ml-1\">\n                    {product.rating?.toFixed(1) ?? 'N/A'}\n                    {product.reviewCount ? ` (${product.reviewCount})` : ''}\n                  </span>\n                </div>\n              </div>\n\n              {/* Product name */}\n              <Link href={`/${currentLanguage}/shop/product/${product.slug}`} className=\"block group/title\">\n                <h3 className={cn(\n                  \"font-bold text-slate-900 dark:text-white mb-2 group-hover/title:text-primary-600 dark:group-hover/title:text-primary-400 transition-colors duration-300 line-clamp-2\",\n                  variant === 'compact' ? \"text-sm\" : \"text-base\"\n                )}>\n                  {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}\n                </h3>\n              </Link>\n\n              {/* Product description */}\n              <p className={cn(\n                \"text-slate-600 dark:text-slate-300 mb-4 line-clamp-2 leading-relaxed\",\n                variant === 'compact' ? \"text-xs\" : \"text-sm\"\n              )}>\n                {currentLanguage === 'ar'\n                  ? (product.description_ar || product.description)\n                  : product.description}\n              </p>\n\n              {/* Product features/benefits */}\n              {variant === 'featured' && (\n                <div className=\"flex flex-wrap gap-2 mb-4\">\n                  <div className=\"flex items-center gap-1 text-xs text-green-600 dark:text-green-400\">\n                    <Truck className=\"w-3 h-3\" />\n                    <span>{currentLanguage === 'ar' ? 'شحن مجاني' : 'Free Shipping'}</span>\n                  </div>\n                  <div className=\"flex items-center gap-1 text-xs text-blue-600 dark:text-blue-400\">\n                    <Shield className=\"w-3 h-3\" />\n                    <span>{currentLanguage === 'ar' ? 'ضمان سنة' : '1 Year Warranty'}</span>\n                  </div>\n                </div>\n              )}\n\n              {/* Enhanced Price & Stock */}\n              <div className=\"flex items-center justify-between mb-4 mt-auto\">\n                <div className=\"flex flex-col\">\n                  <div className=\"flex items-baseline gap-2\">\n                    <span className={cn(\n                      \"font-bold text-slate-900 dark:text-white\",\n                      variant === 'compact' ? \"text-sm\" : \"text-base\"\n                    )}>\n                      {formatCurrency(product.price)}\n                    </span>\n                    {product.compareAtPrice && product.compareAtPrice > product.price && (\n                      <span className=\"text-sm text-slate-500 line-through\">\n                        {formatCurrency(product.compareAtPrice)}\n                      </span>\n                    )}\n                  </div>\n                  {discountPercentage > 0 && (\n                    <span className=\"text-xs text-green-600 dark:text-green-400 font-medium\">\n                      {currentLanguage === 'ar'\n                        ? `توفير ${formatCurrency(product.compareAtPrice! - product.price)}`\n                        : `Save ${formatCurrency(product.compareAtPrice! - product.price)}`\n                      }\n                    </span>\n                  )}\n                </div>\n\n                <div className=\"text-right\">\n                  <div className={cn(\n                    \"text-xs font-medium px-2 py-1 rounded-full\",\n                    isInStock\n                      ? \"text-green-700 bg-green-100 dark:text-green-400 dark:bg-green-900/30\"\n                      : \"text-red-700 bg-red-100 dark:text-red-400 dark:bg-red-900/30\"\n                  )}>\n                    {isInStock ? (\n                      currentLanguage === 'ar' ? 'متوفر' : 'In Stock'\n                    ) : (\n                      currentLanguage === 'ar' ? 'نفذ المخزون' : 'Out of Stock'\n                    )}\n                  </div>\n                  {isInStock && product.stock && product.stock <= 10 && (\n                    <div className=\"text-xs text-orange-600 dark:text-orange-400 mt-1\">\n                      {currentLanguage === 'ar'\n                        ? `${product.stock} متبقية فقط`\n                        : `Only ${product.stock} left`\n                      }\n                    </div>\n                  )}\n                </div>\n              </div>\n\n              {/* Enhanced Action buttons */}\n              <div className=\"flex gap-2\">\n                {/* Add to cart button */}\n                {showAddToCart && (\n                  <motion.div\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                    className=\"flex-1\"\n                  >\n                    <Button\n                      variant=\"primary\"\n                      size={variant === 'compact' ? 'sm' : 'md'}\n                      className={cn(\n                        \"w-full rounded-xl font-medium shadow-lg transition-all duration-300\",\n                        \"bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700\",\n                        \"hover:shadow-purple-500/30 dark:hover:shadow-purple-400/20\",\n                        !isInStock && \"opacity-50 cursor-not-allowed\"\n                      )}\n                      onClick={handleAddToCart}\n                      disabled={!isInStock}\n                      aria-label={currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}\n                    >\n                      <ShoppingCart className={`h-4 w-4 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}`} />\n                      <span className={variant === 'compact' ? 'hidden sm:inline' : ''}>\n                        {currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}\n                      </span>\n                    </Button>\n                  </motion.div>\n                )}\n\n                {/* B2B button */}\n                {showWholesale && onRequestWholesale && (\n                  <motion.div\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    <Button\n                      variant=\"outline\"\n                      size={variant === 'compact' ? 'sm' : 'md'}\n                      className=\"rounded-xl border-2 hover:border-slate-300 dark:hover:border-slate-600 transition-all duration-300 bg-white dark:bg-slate-800 px-4\"\n                      onClick={(e) => {\n                        e.preventDefault();\n                        e.stopPropagation();\n                        try {\n                          onRequestWholesale(product);\n                        } catch (error) {\n                          console.error('Error calling onRequestWholesale:', error);\n                        }\n                      }}\n                      aria-label={currentLanguage === 'ar' ? 'طلب جملة' : 'B2B Request'}\n                    >\n                      <Users className=\"h-4 w-4 mr-2\" />\n                      <span className=\"text-sm font-bold\">B2B</span>\n                    </Button>\n                  </motion.div>\n                )}\n              </div>\n            </>\n          )}\n        </div>\n      </Card>\n    </motion.div>\n  );\n});\n\nexport { ProductCard };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AAAA;AACA;;;AAfA;;;;;;;;;;;;;AAqCA,MAAM,4BAAc,GAAA,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,UAAE,SAAS,YAAY,EAC5C,OAAO,EACP,QAAQ,CAAC,EACT,YAAY,EAAE,EACd,gBAAgB,IAAI,EACpB,gBAAgB,IAAI,EACpB,eAAe,IAAI,EACnB,cAAc,KAAK,EACnB,YAAY,KAAK,EACjB,gBAAgB,IAAI,EACpB,UAAU,SAAS,EACnB,WAAW,KAAK,EAChB,WAAW,EACX,WAAW,EACX,gBAAgB,EAChB,SAAS,EACT,OAAO,EACP,kBAAkB,EACD;;IACjB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAClC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,YAAY,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAC7B,MAAM,gBAAgB,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,aAAa,UAAU;IAC7B,MAAM,kBAAkB;IAExB,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,uEAAuE;QACvE,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,UAAU,OAAO,CAAC,SAAS;QAC7B;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,uEAAuE;QACvE,IAAI,kBAAkB;YACpB,iBAAiB;QACnB,OAAO;YACL,IAAI,cAAc,YAAY,CAAC,QAAQ,EAAE,CAAC,QAAQ,KAAK;gBACrD,cAAc,UAAU,CAAC,QAAQ,EAAE,CAAC,QAAQ;YAC9C,OAAO;gBACL,cAAc,OAAO,CAAC;YACxB;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,aAAa;YACf,YAAY;QACd;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,WAAW;YACb,UAAU;QACZ;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,SAAS;YACX,QAAQ;QACV;IACF;IAEA,oDAAoD;IACpD,MAAM,gBAAgB,CAAC,4BAA4B,EAAE,aAAa,SAAS,QAAQ,IAAI,CAAC;IAExF,iEAAiE;IACjE,MAAM,eAAe,CAAC,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,KAAK,IAC9D,gBACA,QAAQ,MAAM,CAAC,EAAE;IAErB,qCAAqC;IACrC,MAAM,YAAY,QAAQ,eAAe,KAAK,cAAc,QAAQ,eAAe,KAAK;IAExF,0CAA0C;IAC1C,MAAM,qBAAqB,QAAQ,cAAc,IAAI,QAAQ,cAAc,GAAG,QAAQ,KAAK,GACvF,KAAK,KAAK,CAAC,CAAC,IAAI,QAAQ,KAAK,GAAG,QAAQ,cAAc,IAAI,OAC1D,QAAQ,QAAQ,IAAI;IAExB,8BAA8B;IAC9B,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,uBAAuB;IACvB,MAAM,mBAAmB,YAAY,QAAQ;IAI7C,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAI;QAChD,cAAc,IAAM,aAAa;QACjC,YAAY,IAAM,aAAa;QAC/B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBAEhC,cAAA,6LAAC,mIAAA,CAAA,OAAI;YACH,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0KACA,kFACA,wCACA,iBACA,YAAY,cAAc,+FAC1B;;8BAGF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,cAAc,EAAE,QAAQ,IAAI,EAAE;sCAC5D,cAAA,6LAAC;gCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,+GACA,YAAY,SAAS,yBAAyB;;kDAE9C,6LAAC,4IAAA,CAAA,gBAAa;wCACZ,KAAK;wCACL,KAAK,QAAQ,IAAI;wCACjB,MAAM;wCACN,WAAU;wCACV,aAAa;wCACb,aAAY;wCACZ,WAAU;wCACV,OAAM;wCACN,UAAU;;;;;;kDAIZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS,YAAY,IAAI;wCAAE;wCACtC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;;;;;;;;;;;;;;;;;sCAQhB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;;oCACb,QAAQ,QAAQ,kBACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,OAAO;4CAAG,SAAS;wCAAE;wCAChC,SAAS;4CAAE,OAAO;4CAAG,SAAS;wCAAE;wCAChC,MAAM;4CAAE,OAAO;4CAAG,SAAS;wCAAE;wCAC7B,WAAU;;0DAEV,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DACb,oBAAoB,OAAO,SAAS;;;;;;;;;;;;oCAI1C,QAAQ,KAAK,kBACZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,OAAO;4CAAG,SAAS;wCAAE;wCAChC,SAAS;4CAAE,OAAO;4CAAG,SAAS;wCAAE;wCAChC,MAAM;4CAAE,OAAO;4CAAG,SAAS;wCAAE;wCAC7B,WAAU;kDAEV,cAAA,6LAAC;4CAAK,WAAU;sDACb,oBAAoB,OAAO,SAAS;;;;;;;;;;;oCAI1C,qBAAqB,mBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,OAAO;4CAAG,SAAS;wCAAE;wCAChC,SAAS;4CAAE,OAAO;4CAAG,SAAS;wCAAE;wCAChC,MAAM;4CAAE,OAAO;4CAAG,SAAS;wCAAE;wCAC7B,WAAU;kDAEV,cAAA,6LAAC;4CAAK,WAAU;sDACb,oBAAoB,OAAO,GAAG,mBAAmB,KAAK,CAAC,GAAG,GAAG,mBAAmB,KAAK,CAAC;;;;;;;;;;;oCAI5F,CAAC,2BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,OAAO;4CAAG,SAAS;wCAAE;wCAChC,SAAS;4CAAE,OAAO;4CAAG,SAAS;wCAAE;wCAChC,MAAM;4CAAE,OAAO;4CAAG,SAAS;wCAAE;wCAC7B,WAAU;kDAEV,cAAA,6LAAC;4CAAK,WAAU;sDACb,oBAAoB,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;;;;;sCAQtD,6LAAC;4BAAI,WAAU;sCACZ,8BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAK;gCACxB,YAAY;oCAAE,UAAU;gCAAI;0CAE5B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kFACA,cAAc,YAAY,CAAC,QAAQ,EAAE,CAAC,QAAQ,MAC1C,4EACA;oCAEN,SAAS;oCACT,cAAY,oBAAoB,OAAO,sBAAsB;8CAE7D,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uCACA,cAAc,YAAY,CAAC,QAAQ,EAAE,CAAC,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUnE,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,+BACA,YAAY,UAAU;8BAErB,YAAY,SACX,mBAAmB;kCACnB;;0CACE,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,cAAc,EAAE,QAAQ,IAAI,EAAE;wCAAE,WAAU;kDACxE,cAAA,6LAAC;4CAAG,WAAU;sDACX,oBAAoB,OAAQ,QAAQ,OAAO,IAAI,QAAQ,IAAI,GAAI,QAAQ,IAAI;;;;;;;;;;;kDAKhF,6LAAC;wCAAE,WAAU;kDACV,oBAAoB,OAChB,QAAQ,cAAc,IAAI,QAAQ,WAAW,GAC9C,QAAQ,WAAW;;;;;;kDAIzB,6LAAC;wCAAI,WAAU;;4CACZ,QAAQ,QAAQ,kBACf,6LAAC;gDAAK,WAAU;0DACb,QAAQ,QAAQ;;;;;;0DAIrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ;+DAAI,MAAM;yDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;gEAEH,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0CACA,IAAI,KAAK,KAAK,CAAC,QAAQ,MAAM,IAAI,KAC7B,iCACA;+DALD;;;;;;;;;;kEAUX,6LAAC;wDAAK,WAAU;;4DACb,QAAQ,MAAM,EAAE,QAAQ,MAAM;4DAC9B,QAAQ,WAAW,GAAG,CAAC,EAAE,EAAE,QAAQ,WAAW,CAAC,CAAC,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;0CAM7D,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,KAAK;;;;;;oDAE9B,QAAQ,cAAc,IAAI,QAAQ,cAAc,GAAG,QAAQ,KAAK,kBAC/D,6LAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,cAAc;;;;;;;;;;;;0DAI5C,6LAAC;gDAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,mDACA,YACI,yEACA;0DAEH,YACC,oBAAoB,OAAO,UAAU,aAErC,oBAAoB,OAAO,gBAAgB;;;;;;;;;;;;kDAMjD,6LAAC;wCAAI,WAAU;;4CACZ,+BACC,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gEACA,4FACA,CAAC,aAAa;gDAEhB,SAAS;gDACT,UAAU,CAAC;gDACX,cAAY,oBAAoB,OAAO,cAAc;;kEAErD,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;oDACvB,oBAAoB,OAAO,cAAc;;;;;;;4CAK7C,iBAAiB,oCAChB,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,EAAE,eAAe;oDACjB,IAAI;wDACF,mBAAmB;oDACrB,EAAE,OAAO,OAAO;wDACd,QAAQ,KAAK,CAAC,qCAAqC;oDACrD;gDACF;gDACA,cAAY,oBAAoB,OAAO,aAAa;;kEAEpD,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;wDAAK,WAAU;kEAAoB;;;;;;;;;;;;;;;;;;;;;;;;;uCAO9C,mBAAmB;kCACnB;;0CAEE,6LAAC;gCAAI,WAAU;;oCACZ,QAAQ,QAAQ,kBACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,WAAU;kDAET,QAAQ,QAAQ;;;;;;kDAIrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;wDAEH,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0CACA,IAAI,KAAK,KAAK,CAAC,QAAQ,MAAM,IAAI,KAC7B,iCACA;uDALD;;;;;;;;;;0DAUX,6LAAC;gDAAK,WAAU;;oDACb,QAAQ,MAAM,EAAE,QAAQ,MAAM;oDAC9B,QAAQ,WAAW,GAAG,CAAC,EAAE,EAAE,QAAQ,WAAW,CAAC,CAAC,CAAC,GAAG;;;;;;;;;;;;;;;;;;;0CAM3D,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,cAAc,EAAE,QAAQ,IAAI,EAAE;gCAAE,WAAU;0CACxE,cAAA,6LAAC;oCAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,wKACA,YAAY,YAAY,YAAY;8CAEnC,oBAAoB,OAAQ,QAAQ,OAAO,IAAI,QAAQ,IAAI,GAAI,QAAQ,IAAI;;;;;;;;;;;0CAKhF,6LAAC;gCAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACb,wEACA,YAAY,YAAY,YAAY;0CAEnC,oBAAoB,OAChB,QAAQ,cAAc,IAAI,QAAQ,WAAW,GAC9C,QAAQ,WAAW;;;;;;4BAIxB,YAAY,4BACX,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAM,oBAAoB,OAAO,cAAc;;;;;;;;;;;;kDAElD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;0DAAM,oBAAoB,OAAO,aAAa;;;;;;;;;;;;;;;;;;0CAMrD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,4CACA,YAAY,YAAY,YAAY;kEAEnC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,KAAK;;;;;;oDAE9B,QAAQ,cAAc,IAAI,QAAQ,cAAc,GAAG,QAAQ,KAAK,kBAC/D,6LAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,cAAc;;;;;;;;;;;;4CAI3C,qBAAqB,mBACpB,6LAAC;gDAAK,WAAU;0DACb,oBAAoB,OACjB,CAAC,MAAM,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,cAAc,GAAI,QAAQ,KAAK,GAAG,GAClE,CAAC,KAAK,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,cAAc,GAAI,QAAQ,KAAK,GAAG;;;;;;;;;;;;kDAM3E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,8CACA,YACI,yEACA;0DAEH,YACC,oBAAoB,OAAO,UAAU,aAErC,oBAAoB,OAAO,gBAAgB;;;;;;4CAG9C,aAAa,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,oBAC9C,6LAAC;gDAAI,WAAU;0DACZ,oBAAoB,OACjB,GAAG,QAAQ,KAAK,CAAC,WAAW,CAAC,GAC7B,CAAC,KAAK,EAAE,QAAQ,KAAK,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;0CAQxC,6LAAC;gCAAI,WAAU;;oCAEZ,+BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDAEV,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAM,YAAY,YAAY,OAAO;4CACrC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uEACA,4FACA,8DACA,CAAC,aAAa;4CAEhB,SAAS;4CACT,UAAU,CAAC;4CACX,cAAY,oBAAoB,OAAO,cAAc;;8DAErD,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAW,CAAC,QAAQ,EAAE,oBAAoB,OAAO,SAAS,QAAQ;;;;;;8DAChF,6LAAC;oDAAK,WAAW,YAAY,YAAY,qBAAqB;8DAC3D,oBAAoB,OAAO,cAAc;;;;;;;;;;;;;;;;;oCAOjD,iBAAiB,oCAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAM,YAAY,YAAY,OAAO;4CACrC,WAAU;4CACV,SAAS,CAAC;gDACR,EAAE,cAAc;gDAChB,EAAE,eAAe;gDACjB,IAAI;oDACF,mBAAmB;gDACrB,EAAE,OAAO,OAAO;oDACd,QAAQ,KAAK,CAAC,qCAAqC;gDACrD;4CACF;4CACA,cAAY,oBAAoB,OAAO,aAAa;;8DAEpD,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAK,WAAU;8DAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1D;;QA5hBuB,+HAAA,CAAA,iBAAc;QACjB,mJAAA,CAAA,WAAQ;QACR,6HAAA,CAAA,eAAY;QACR,iIAAA,CAAA,mBAAgB;;;;QAHjB,+HAAA,CAAA,iBAAc;QACjB,mJAAA,CAAA,WAAQ;QACR,6HAAA,CAAA,eAAY;QACR,iIAAA,CAAA,mBAAgB", "debugId": null}}, {"offset": {"line": 4455, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/product/ProductDetailModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  X, ShoppingCart, Heart, Share2, Package, Truck, Shield, Star,\n  Plus, Minus, ZoomIn, ChevronLeft, ChevronRight, Info, Award,\n  Clock, MapPin, CreditCard, RotateCcw, Building\n} from 'lucide-react';\nimport { Button } from '../ui/Button';\nimport { Card } from '../ui/Card';\nimport { EnhancedImage } from '../ui/EnhancedImage';\nimport { useCartStore } from '../../stores/cartStore';\nimport { useWishlistStore } from '../../stores/wishlistStore';\nimport { useTranslation } from '../../translations';\nimport { formatCurrency, cn } from '../../lib/utils';\nimport { Product } from '../../types/index';\nimport { WholesaleQuoteForm } from '../forms/WholesaleQuoteForm';\n\ninterface ProductDetailModalProps {\n  product: Product | null;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function ProductDetailModal({ product, isOpen, onClose }: ProductDetailModalProps) {\n  const { language } = useTranslation();\n  const cartStore = useCartStore();\n  const wishlistStore = useWishlistStore();\n  \n  const [selectedImageIndex, setSelectedImageIndex] = useState(0);\n  const [quantity, setQuantity] = useState(1);\n  const [activeTab, setActiveTab] = useState<'description' | 'specifications' | 'reviews' | 'shipping'>('description');\n  const [isImageZoomed, setIsImageZoomed] = useState(false);\n  const [selectedVariants, setSelectedVariants] = useState<{ [key: string]: string }>({});\n  const [showWholesaleForm, setShowWholesaleForm] = useState(false);\n\n  const currentLanguage = language;\n\n  // Reset state when modal opens/closes\n  useEffect(() => {\n    if (isOpen && product) {\n      setSelectedImageIndex(0);\n      setQuantity(1);\n      setActiveTab('description');\n      setIsImageZoomed(false);\n      setSelectedVariants({});\n    }\n  }, [isOpen, product]);\n\n  // Handle escape key\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape' && isOpen) {\n        onClose();\n      }\n    };\n\n    document.addEventListener('keydown', handleEscape);\n    return () => document.removeEventListener('keydown', handleEscape);\n  }, [isOpen, onClose]);\n\n  if (!product) return null;\n\n  const isInStock = product.inventoryStatus === 'in-stock' || product.inventoryStatus === 'low-stock';\n  const isWishlisted = wishlistStore.isInWishlist(product.id.toString());\n  const discountPercentage = product.compareAtPrice && product.compareAtPrice > product.price\n    ? Math.round((1 - product.price / product.compareAtPrice) * 100)\n    : product.discount || 0;\n\n  const handleQuantityChange = (change: number) => {\n    const newQuantity = quantity + change;\n    if (newQuantity >= 1 && newQuantity <= (product.stock || 999)) {\n      setQuantity(newQuantity);\n    }\n  };\n\n  const handleAddToCart = () => {\n    cartStore.addItem(product, quantity);\n    // Show success feedback (could add toast notification here)\n  };\n\n  const handleToggleWishlist = () => {\n    if (isWishlisted) {\n      wishlistStore.removeItem(product.id.toString());\n    } else {\n      wishlistStore.addItem(product);\n    }\n  };\n\n  const handleShare = async () => {\n    if (navigator.share) {\n      try {\n        await navigator.share({\n          title: currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name,\n          text: currentLanguage === 'ar' ? (product.description_ar || product.description) : product.description,\n          url: window.location.href,\n        });\n      } catch (error) {\n        console.log('Error sharing:', error);\n      }\n    } else {\n      // Fallback: copy to clipboard\n      navigator.clipboard.writeText(window.location.href);\n    }\n  };\n\n  const nextImage = () => {\n    setSelectedImageIndex((prev) => \n      prev === product.images.length - 1 ? 0 : prev + 1\n    );\n  };\n\n  const prevImage = () => {\n    setSelectedImageIndex((prev) => \n      prev === 0 ? product.images.length - 1 : prev - 1\n    );\n  };\n\n  const tabs = [\n    { id: 'description', label: currentLanguage === 'ar' ? 'الوصف' : 'Description', icon: Info },\n    { id: 'specifications', label: currentLanguage === 'ar' ? 'المواصفات' : 'Specifications', icon: Package },\n    { id: 'reviews', label: currentLanguage === 'ar' ? 'التقييمات' : 'Reviews', icon: Star },\n    { id: 'shipping', label: currentLanguage === 'ar' ? 'الشحن' : 'Shipping', icon: Truck },\n  ] as const;\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/60 backdrop-blur-sm\"\n          onClick={onClose}\n        >\n          <motion.div\n            initial={{ scale: 0.9, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            exit={{ scale: 0.9, opacity: 0 }}\n            transition={{ type: \"spring\", duration: 0.5 }}\n            className=\"relative w-full max-w-6xl max-h-[90vh] overflow-hidden bg-white dark:bg-slate-900 rounded-2xl shadow-2xl\"\n            onClick={(e) => e.stopPropagation()}\n          >\n            {/* Close Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"absolute top-4 right-4 z-10 p-2 rounded-full bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm hover:bg-white dark:hover:bg-slate-800\"\n              onClick={onClose}\n            >\n              <X className=\"h-5 w-5\" />\n            </Button>\n\n            <div className=\"flex flex-col lg:flex-row h-full max-h-[90vh]\">\n              {/* Image Gallery Section */}\n              <div className=\"lg:w-1/2 relative bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-900\">\n                {/* Main Image */}\n                <div className=\"relative h-64 lg:h-96 overflow-hidden\">\n                  <EnhancedImage\n                    src={product.images[selectedImageIndex] || '/images/product-placeholder-light.svg'}\n                    alt={product.name}\n                    fill={true}\n                    objectFit=\"cover\"\n                    className=\"transition-transform duration-300 hover:scale-105\"\n                  />\n                  \n                  {/* Image Navigation */}\n                  {product.images.length > 1 && (\n                    <>\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"absolute left-2 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm\"\n                        onClick={prevImage}\n                      >\n                        <ChevronLeft className=\"h-4 w-4\" />\n                      </Button>\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm\"\n                        onClick={nextImage}\n                      >\n                        <ChevronRight className=\"h-4 w-4\" />\n                      </Button>\n                    </>\n                  )}\n\n                  {/* Zoom Button */}\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className=\"absolute bottom-2 right-2 p-2 rounded-full bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm\"\n                    onClick={() => setIsImageZoomed(true)}\n                  >\n                    <ZoomIn className=\"h-4 w-4\" />\n                  </Button>\n\n                  {/* Product Badges */}\n                  <div className=\"absolute top-4 left-4 flex flex-col gap-2\">\n                    {product.isNew && (\n                      <span className=\"px-3 py-1 text-xs font-bold bg-blue-500 text-white rounded-full\">\n                        {currentLanguage === 'ar' ? 'جديد' : 'New'}\n                      </span>\n                    )}\n                    {discountPercentage > 0 && (\n                      <span className=\"px-3 py-1 text-xs font-bold bg-red-500 text-white rounded-full\">\n                        {currentLanguage === 'ar' ? `${discountPercentage}% خصم` : `${discountPercentage}% OFF`}\n                      </span>\n                    )}\n                    {product.featured && (\n                      <span className=\"px-3 py-1 text-xs font-bold bg-amber-500 text-white rounded-full flex items-center gap-1\">\n                        <Award className=\"w-3 h-3\" />\n                        {currentLanguage === 'ar' ? 'مميز' : 'Featured'}\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                {/* Thumbnail Gallery */}\n                {product.images.length > 1 && (\n                  <div className=\"p-4 flex gap-2 overflow-x-auto\">\n                    {product.images.map((image, index) => (\n                      <button\n                        key={index}\n                        className={cn(\n                          \"flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-200\",\n                          selectedImageIndex === index\n                            ? \"border-primary-500 ring-2 ring-primary-200\"\n                            : \"border-slate-200 dark:border-slate-700 hover:border-primary-300\"\n                        )}\n                        onClick={() => setSelectedImageIndex(index)}\n                      >\n                        <EnhancedImage\n                          src={image}\n                          alt={`${product.name} ${index + 1}`}\n                          width={64}\n                          height={64}\n                          objectFit=\"cover\"\n                        />\n                      </button>\n                    ))}\n                  </div>\n                )}\n              </div>\n\n              {/* Product Information Section */}\n              <div className=\"lg:w-1/2 flex flex-col overflow-y-auto\">\n                <div className=\"p-6 space-y-6\">\n                  {/* Product Header */}\n                  <div>\n                    <div className=\"flex items-center gap-2 mb-2\">\n                      {product.category && (\n                        <span className=\"text-sm px-3 py-1 bg-primary-100 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300 rounded-full\">\n                          {product.category}\n                        </span>\n                      )}\n                      <div className=\"flex items-center gap-1\">\n                        {[...Array(5)].map((_, i) => (\n                          <Star\n                            key={i}\n                            className={cn(\n                              \"h-4 w-4\",\n                              i < Math.floor(product.rating || 0)\n                                ? \"text-yellow-400 fill-current\"\n                                : \"text-slate-300 dark:text-slate-600\"\n                            )}\n                          />\n                        ))}\n                        <span className=\"text-sm text-slate-600 dark:text-slate-400 ml-1\">\n                          {product.rating?.toFixed(1) || 'N/A'}\n                          {product.reviewCount ? ` (${product.reviewCount})` : ''}\n                        </span>\n                      </div>\n                    </div>\n                    \n                    <h1 className=\"text-2xl lg:text-3xl font-bold text-slate-900 dark:text-white mb-2\">\n                      {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}\n                    </h1>\n                    \n                    <p className=\"text-slate-600 dark:text-slate-300 leading-relaxed\">\n                      {currentLanguage === 'ar' \n                        ? (product.description_ar || product.description)\n                        : product.description}\n                    </p>\n                  </div>\n\n                  {/* Pricing */}\n                  <div className=\"flex items-baseline gap-3\">\n                    <span className=\"text-3xl font-bold text-slate-900 dark:text-white\">\n                      {formatCurrency(product.price)}\n                    </span>\n                    {product.compareAtPrice && product.compareAtPrice > product.price && (\n                      <span className=\"text-xl text-slate-500 line-through\">\n                        {formatCurrency(product.compareAtPrice)}\n                      </span>\n                    )}\n                    {discountPercentage > 0 && (\n                      <span className=\"text-sm text-green-600 dark:text-green-400 font-medium\">\n                        {currentLanguage === 'ar'\n                          ? `توفير ${formatCurrency(product.compareAtPrice! - product.price)}`\n                          : `Save ${formatCurrency(product.compareAtPrice! - product.price)}`\n                        }\n                      </span>\n                    )}\n                  </div>\n\n                  {/* Stock Status */}\n                  <div className=\"flex items-center gap-3\">\n                    <div className={cn(\n                      \"flex items-center gap-2 px-3 py-2 rounded-lg\",\n                      isInStock\n                        ? \"bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400\"\n                        : \"bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400\"\n                    )}>\n                      <Package className=\"w-4 h-4\" />\n                      <span className=\"font-medium\">\n                        {isInStock ? (\n                          currentLanguage === 'ar' ? 'متوفر' : 'In Stock'\n                        ) : (\n                          currentLanguage === 'ar' ? 'نفذ المخزون' : 'Out of Stock'\n                        )}\n                      </span>\n                    </div>\n                    \n                    {isInStock && product.stock && product.stock <= 10 && (\n                      <span className=\"text-sm text-orange-600 dark:text-orange-400\">\n                        {currentLanguage === 'ar'\n                          ? `${product.stock} متبقية فقط`\n                          : `Only ${product.stock} left`\n                        }\n                      </span>\n                    )}\n                  </div>\n\n                  {/* Quantity Selector */}\n                  <div className=\"flex items-center gap-4\">\n                    <span className=\"font-medium text-slate-700 dark:text-slate-300\">\n                      {currentLanguage === 'ar' ? 'الكمية:' : 'Quantity:'}\n                    </span>\n                    <div className=\"flex items-center border border-slate-300 dark:border-slate-600 rounded-lg\">\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"p-2 hover:bg-slate-100 dark:hover:bg-slate-700\"\n                        onClick={() => handleQuantityChange(-1)}\n                        disabled={quantity <= 1}\n                      >\n                        <Minus className=\"h-4 w-4\" />\n                      </Button>\n                      <span className=\"px-4 py-2 font-medium min-w-[3rem] text-center\">\n                        {quantity}\n                      </span>\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"p-2 hover:bg-slate-100 dark:hover:bg-slate-700\"\n                        onClick={() => handleQuantityChange(1)}\n                        disabled={quantity >= (product.stock || 999)}\n                      >\n                        <Plus className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                  </div>\n\n                  {/* Action Buttons */}\n                  <div className=\"space-y-3\">\n                    <div className=\"flex gap-3\">\n                      <Button\n                        variant=\"primary\"\n                        size=\"lg\"\n                        className=\"flex-1 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700\"\n                        onClick={handleAddToCart}\n                        disabled={!isInStock}\n                      >\n                        <ShoppingCart className=\"h-5 w-5 mr-2\" />\n                        {currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}\n                      </Button>\n\n                      <Button\n                        variant=\"outline\"\n                        size=\"lg\"\n                        className={cn(\n                          \"p-3\",\n                          isWishlisted && \"bg-red-50 border-red-200 text-red-600 dark:bg-red-900/20 dark:border-red-800 dark:text-red-400\"\n                        )}\n                        onClick={handleToggleWishlist}\n                      >\n                        <Heart className={cn(\"h-5 w-5\", isWishlisted && \"fill-current\")} />\n                      </Button>\n\n                      <Button\n                        variant=\"outline\"\n                        size=\"lg\"\n                        className=\"p-3\"\n                        onClick={handleShare}\n                      >\n                        <Share2 className=\"h-5 w-5\" />\n                      </Button>\n                    </div>\n\n                    {/* Wholesale Quote Button */}\n                    <Button\n                      variant=\"outline\"\n                      size=\"lg\"\n                      className=\"w-full border-orange-200 text-orange-700 hover:bg-orange-50 dark:border-orange-800 dark:text-orange-400 dark:hover:bg-orange-900/20\"\n                      onClick={() => setShowWholesaleForm(true)}\n                    >\n                      <Building className=\"h-5 w-5 mr-2\" />\n                      {currentLanguage === 'ar' ? 'طلب عرض أسعار الجملة' : 'Request Wholesale Quote'}\n                    </Button>\n                  </div>\n\n                  {/* Trust Indicators */}\n                  <div className=\"grid grid-cols-2 gap-4 pt-4 border-t border-slate-200 dark:border-slate-700\">\n                    <div className=\"flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400\">\n                      <Truck className=\"w-4 h-4 text-green-500\" />\n                      <span>{currentLanguage === 'ar' ? 'شحن مجاني' : 'Free Shipping'}</span>\n                    </div>\n                    <div className=\"flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400\">\n                      <Shield className=\"w-4 h-4 text-blue-500\" />\n                      <span>{currentLanguage === 'ar' ? 'ضمان سنة' : '1 Year Warranty'}</span>\n                    </div>\n                    <div className=\"flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400\">\n                      <RotateCcw className=\"w-4 h-4 text-purple-500\" />\n                      <span>{currentLanguage === 'ar' ? 'إرجاع مجاني' : 'Free Returns'}</span>\n                    </div>\n                    <div className=\"flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400\">\n                      <CreditCard className=\"w-4 h-4 text-orange-500\" />\n                      <span>{currentLanguage === 'ar' ? 'دفع آمن' : 'Secure Payment'}</span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Tabbed Content */}\n                <div className=\"border-t border-slate-200 dark:border-slate-700\">\n                  {/* Tab Navigation */}\n                  <div className=\"flex border-b border-slate-200 dark:border-slate-700\">\n                    {tabs.map((tab) => {\n                      const Icon = tab.icon;\n                      return (\n                        <button\n                          key={tab.id}\n                          className={cn(\n                            \"flex items-center gap-2 px-4 py-3 text-sm font-medium transition-colors duration-200\",\n                            activeTab === tab.id\n                              ? \"text-primary-600 border-b-2 border-primary-600 bg-primary-50 dark:bg-primary-900/20 dark:text-primary-400\"\n                              : \"text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-200\"\n                          )}\n                          onClick={() => setActiveTab(tab.id as any)}\n                        >\n                          <Icon className=\"w-4 h-4\" />\n                          {tab.label}\n                        </button>\n                      );\n                    })}\n                  </div>\n\n                  {/* Tab Content */}\n                  <div className=\"p-6 min-h-[200px]\">\n                    {activeTab === 'description' && (\n                      <div className=\"prose dark:prose-invert max-w-none\">\n                        <p className=\"text-slate-700 dark:text-slate-300 leading-relaxed\">\n                          {currentLanguage === 'ar' \n                            ? (product.description_ar || product.description)\n                            : product.description}\n                        </p>\n                      </div>\n                    )}\n\n                    {activeTab === 'specifications' && (\n                      <div className=\"space-y-3\">\n                        {product.specifications && Object.entries(product.specifications).map(([key, value]) => (\n                          <div key={key} className=\"flex justify-between py-2 border-b border-slate-100 dark:border-slate-800\">\n                            <span className=\"font-medium text-slate-700 dark:text-slate-300\">{key}</span>\n                            <span className=\"text-slate-600 dark:text-slate-400\">{value}</span>\n                          </div>\n                        ))}\n                        {(!product.specifications || Object.keys(product.specifications).length === 0) && (\n                          <p className=\"text-slate-500 dark:text-slate-400 text-center py-8\">\n                            {currentLanguage === 'ar' ? 'لا توجد مواصفات متاحة' : 'No specifications available'}\n                          </p>\n                        )}\n                      </div>\n                    )}\n\n                    {activeTab === 'reviews' && (\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center gap-4 mb-6\">\n                          <div className=\"text-center\">\n                            <div className=\"text-3xl font-bold text-slate-900 dark:text-white\">\n                              {product.rating?.toFixed(1) || 'N/A'}\n                            </div>\n                            <div className=\"flex items-center justify-center gap-1 mt-1\">\n                              {[...Array(5)].map((_, i) => (\n                                <Star\n                                  key={i}\n                                  className={cn(\n                                    \"h-4 w-4\",\n                                    i < Math.floor(product.rating || 0)\n                                      ? \"text-yellow-400 fill-current\"\n                                      : \"text-slate-300 dark:text-slate-600\"\n                                  )}\n                                />\n                              ))}\n                            </div>\n                            <div className=\"text-sm text-slate-600 dark:text-slate-400 mt-1\">\n                              {product.reviewCount || 0} {currentLanguage === 'ar' ? 'تقييم' : 'reviews'}\n                            </div>\n                          </div>\n                        </div>\n                        \n                        <p className=\"text-slate-500 dark:text-slate-400 text-center py-8\">\n                          {currentLanguage === 'ar' ? 'لا توجد تقييمات متاحة' : 'No reviews available'}\n                        </p>\n                      </div>\n                    )}\n\n                    {activeTab === 'shipping' && (\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n                          <Truck className=\"w-5 h-5 text-blue-500 mt-0.5\" />\n                          <div>\n                            <h4 className=\"font-medium text-slate-900 dark:text-white mb-1\">\n                              {currentLanguage === 'ar' ? 'الشحن المجاني' : 'Free Shipping'}\n                            </h4>\n                            <p className=\"text-sm text-slate-600 dark:text-slate-400\">\n                              {currentLanguage === 'ar' \n                                ? 'شحن مجاني للطلبات أكثر من 100 ريال'\n                                : 'Free shipping on orders over $100'}\n                            </p>\n                          </div>\n                        </div>\n                        \n                        <div className=\"flex items-start gap-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n                          <Clock className=\"w-5 h-5 text-green-500 mt-0.5\" />\n                          <div>\n                            <h4 className=\"font-medium text-slate-900 dark:text-white mb-1\">\n                              {currentLanguage === 'ar' ? 'التوصيل السريع' : 'Fast Delivery'}\n                            </h4>\n                            <p className=\"text-sm text-slate-600 dark:text-slate-400\">\n                              {currentLanguage === 'ar' \n                                ? 'التوصيل خلال 2-3 أيام عمل'\n                                : 'Delivery within 2-3 business days'}\n                            </p>\n                          </div>\n                        </div>\n                        \n                        <div className=\"flex items-start gap-3 p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg\">\n                          <MapPin className=\"w-5 h-5 text-purple-500 mt-0.5\" />\n                          <div>\n                            <h4 className=\"font-medium text-slate-900 dark:text-white mb-1\">\n                              {currentLanguage === 'ar' ? 'التوصيل المحلي' : 'Local Delivery'}\n                            </h4>\n                            <p className=\"text-sm text-slate-600 dark:text-slate-400\">\n                              {currentLanguage === 'ar' \n                                ? 'متوفر في جميع أنحاء المملكة'\n                                : 'Available nationwide'}\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Image Lightbox */}\n          <AnimatePresence>\n            {isImageZoomed && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className=\"fixed inset-0 z-60 bg-black/90 flex items-center justify-center p-4\"\n                onClick={() => setIsImageZoomed(false)}\n              >\n                <motion.div\n                  initial={{ scale: 0.8 }}\n                  animate={{ scale: 1 }}\n                  exit={{ scale: 0.8 }}\n                  className=\"relative max-w-4xl max-h-full\"\n                  onClick={(e) => e.stopPropagation()}\n                >\n                  <EnhancedImage\n                    src={product.images[selectedImageIndex]}\n                    alt={product.name}\n                    width={800}\n                    height={600}\n                    objectFit=\"contain\"\n                    className=\"rounded-lg\"\n                  />\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className=\"absolute top-4 right-4 p-2 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30\"\n                    onClick={() => setIsImageZoomed(false)}\n                  >\n                    <X className=\"h-5 w-5\" />\n                  </Button>\n                </motion.div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* Wholesale Quote Form Modal */}\n          <AnimatePresence>\n            {showWholesaleForm && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                className=\"fixed inset-0 z-60 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4\"\n                onClick={() => setShowWholesaleForm(false)}\n              >\n                <motion.div\n                  initial={{ scale: 0.9, opacity: 0 }}\n                  animate={{ scale: 1, opacity: 1 }}\n                  exit={{ scale: 0.9, opacity: 0 }}\n                  className=\"relative w-full max-w-2xl max-h-[90vh] overflow-y-auto\"\n                  onClick={(e) => e.stopPropagation()}\n                >\n                  <WholesaleQuoteForm\n                    product={product}\n                    initialQuantity={quantity}\n                    onClose={() => setShowWholesaleForm(false)}\n                  />\n                </motion.div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA;AAEA;AACA;AACA;AACA;AACA;AAEA;;;AAjBA;;;;;;;;;;;AAyBO,SAAS,mBAAmB,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAA2B;;IACtF,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAClC,MAAM,YAAY,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAC7B,MAAM,gBAAgB,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD;IAErC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6D;IACtG,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B,CAAC;IACrF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,kBAAkB;IAExB,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,UAAU,SAAS;gBACrB,sBAAsB;gBACtB,YAAY;gBACZ,aAAa;gBACb,iBAAiB;gBACjB,oBAAoB,CAAC;YACvB;QACF;uCAAG;QAAC;QAAQ;KAAQ;IAEpB,oBAAoB;IACpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM;6DAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,YAAY,QAAQ;wBAChC;oBACF;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;gDAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;uCAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,SAAS,OAAO;IAErB,MAAM,YAAY,QAAQ,eAAe,KAAK,cAAc,QAAQ,eAAe,KAAK;IACxF,MAAM,eAAe,cAAc,YAAY,CAAC,QAAQ,EAAE,CAAC,QAAQ;IACnE,MAAM,qBAAqB,QAAQ,cAAc,IAAI,QAAQ,cAAc,GAAG,QAAQ,KAAK,GACvF,KAAK,KAAK,CAAC,CAAC,IAAI,QAAQ,KAAK,GAAG,QAAQ,cAAc,IAAI,OAC1D,QAAQ,QAAQ,IAAI;IAExB,MAAM,uBAAuB,CAAC;QAC5B,MAAM,cAAc,WAAW;QAC/B,IAAI,eAAe,KAAK,eAAe,CAAC,QAAQ,KAAK,IAAI,GAAG,GAAG;YAC7D,YAAY;QACd;IACF;IAEA,MAAM,kBAAkB;QACtB,UAAU,OAAO,CAAC,SAAS;IAC3B,4DAA4D;IAC9D;IAEA,MAAM,uBAAuB;QAC3B,IAAI,cAAc;YAChB,cAAc,UAAU,CAAC,QAAQ,EAAE,CAAC,QAAQ;QAC9C,OAAO;YACL,cAAc,OAAO,CAAC;QACxB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,UAAU,KAAK,EAAE;YACnB,IAAI;gBACF,MAAM,UAAU,KAAK,CAAC;oBACpB,OAAO,oBAAoB,OAAQ,QAAQ,OAAO,IAAI,QAAQ,IAAI,GAAI,QAAQ,IAAI;oBAClF,MAAM,oBAAoB,OAAQ,QAAQ,cAAc,IAAI,QAAQ,WAAW,GAAI,QAAQ,WAAW;oBACtG,KAAK,OAAO,QAAQ,CAAC,IAAI;gBAC3B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,kBAAkB;YAChC;QACF,OAAO;YACL,8BAA8B;YAC9B,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,QAAQ,CAAC,IAAI;QACpD;IACF;IAEA,MAAM,YAAY;QAChB,sBAAsB,CAAC,OACrB,SAAS,QAAQ,MAAM,CAAC,MAAM,GAAG,IAAI,IAAI,OAAO;IAEpD;IAEA,MAAM,YAAY;QAChB,sBAAsB,CAAC,OACrB,SAAS,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,IAAI,OAAO;IAEpD;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAe,OAAO,oBAAoB,OAAO,UAAU;YAAe,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC3F;YAAE,IAAI;YAAkB,OAAO,oBAAoB,OAAO,cAAc;YAAkB,MAAM,2MAAA,CAAA,UAAO;QAAC;QACxG;YAAE,IAAI;YAAW,OAAO,oBAAoB,OAAO,cAAc;YAAW,MAAM,qMAAA,CAAA,OAAI;QAAC;QACvF;YAAE,IAAI;YAAY,OAAO,oBAAoB,OAAO,UAAU;YAAY,MAAM,uMAAA,CAAA,QAAK;QAAC;KACvF;IAED,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAU;YACV,SAAS;;8BAET,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,OAAO;wBAAK,SAAS;oBAAE;oBAClC,SAAS;wBAAE,OAAO;wBAAG,SAAS;oBAAE;oBAChC,MAAM;wBAAE,OAAO;wBAAK,SAAS;oBAAE;oBAC/B,YAAY;wBAAE,MAAM;wBAAU,UAAU;oBAAI;oBAC5C,WAAU;oBACV,SAAS,CAAC,IAAM,EAAE,eAAe;;sCAGjC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;sCAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;sCAGf,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,4IAAA,CAAA,gBAAa;oDACZ,KAAK,QAAQ,MAAM,CAAC,mBAAmB,IAAI;oDAC3C,KAAK,QAAQ,IAAI;oDACjB,MAAM;oDACN,WAAU;oDACV,WAAU;;;;;;gDAIX,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACvB;;sEACE,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS;sEAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;sEAEzB,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS;sEAET,cAAA,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;;8DAM9B,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,iBAAiB;8DAEhC,cAAA,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAIpB,6LAAC;oDAAI,WAAU;;wDACZ,QAAQ,KAAK,kBACZ,6LAAC;4DAAK,WAAU;sEACb,oBAAoB,OAAO,SAAS;;;;;;wDAGxC,qBAAqB,mBACpB,6LAAC;4DAAK,WAAU;sEACb,oBAAoB,OAAO,GAAG,mBAAmB,KAAK,CAAC,GAAG,GAAG,mBAAmB,KAAK,CAAC;;;;;;wDAG1F,QAAQ,QAAQ,kBACf,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB,oBAAoB,OAAO,SAAS;;;;;;;;;;;;;;;;;;;wCAO5C,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACvB,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC;oDAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2FACA,uBAAuB,QACnB,+CACA;oDAEN,SAAS,IAAM,sBAAsB;8DAErC,cAAA,6LAAC,4IAAA,CAAA,gBAAa;wDACZ,KAAK;wDACL,KAAK,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,GAAG;wDACnC,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;mDAdP;;;;;;;;;;;;;;;;8CAuBf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;;gEACZ,QAAQ,QAAQ,kBACf,6LAAC;oEAAK,WAAU;8EACb,QAAQ,QAAQ;;;;;;8EAGrB,6LAAC;oEAAI,WAAU;;wEACZ;+EAAI,MAAM;yEAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;gFAEH,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,WACA,IAAI,KAAK,KAAK,CAAC,QAAQ,MAAM,IAAI,KAC7B,iCACA;+EALD;;;;;sFAST,6LAAC;4EAAK,WAAU;;gFACb,QAAQ,MAAM,EAAE,QAAQ,MAAM;gFAC9B,QAAQ,WAAW,GAAG,CAAC,EAAE,EAAE,QAAQ,WAAW,CAAC,CAAC,CAAC,GAAG;;;;;;;;;;;;;;;;;;;sEAK3D,6LAAC;4DAAG,WAAU;sEACX,oBAAoB,OAAQ,QAAQ,OAAO,IAAI,QAAQ,IAAI,GAAI,QAAQ,IAAI;;;;;;sEAG9E,6LAAC;4DAAE,WAAU;sEACV,oBAAoB,OAChB,QAAQ,cAAc,IAAI,QAAQ,WAAW,GAC9C,QAAQ,WAAW;;;;;;;;;;;;8DAK3B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,KAAK;;;;;;wDAE9B,QAAQ,cAAc,IAAI,QAAQ,cAAc,GAAG,QAAQ,KAAK,kBAC/D,6LAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,cAAc;;;;;;wDAGzC,qBAAqB,mBACpB,6LAAC;4DAAK,WAAU;sEACb,oBAAoB,OACjB,CAAC,MAAM,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,cAAc,GAAI,QAAQ,KAAK,GAAG,GAClE,CAAC,KAAK,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,cAAc,GAAI,QAAQ,KAAK,GAAG;;;;;;;;;;;;8DAO3E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,gDACA,YACI,yEACA;;8EAEJ,6LAAC,2MAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;8EACnB,6LAAC;oEAAK,WAAU;8EACb,YACC,oBAAoB,OAAO,UAAU,aAErC,oBAAoB,OAAO,gBAAgB;;;;;;;;;;;;wDAKhD,aAAa,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,oBAC9C,6LAAC;4DAAK,WAAU;sEACb,oBAAoB,OACjB,GAAG,QAAQ,KAAK,CAAC,WAAW,CAAC,GAC7B,CAAC,KAAK,EAAE,QAAQ,KAAK,CAAC,KAAK,CAAC;;;;;;;;;;;;8DAOtC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,oBAAoB,OAAO,YAAY;;;;;;sEAE1C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,qBAAqB,CAAC;oEACrC,UAAU,YAAY;8EAEtB,cAAA,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;8EAEnB,6LAAC;oEAAK,WAAU;8EACb;;;;;;8EAEH,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,qBAAqB;oEACpC,UAAU,YAAY,CAAC,QAAQ,KAAK,IAAI,GAAG;8EAE3C,cAAA,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8DAMtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS;oEACT,UAAU,CAAC;;sFAEX,6LAAC,yNAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;wEACvB,oBAAoB,OAAO,cAAc;;;;;;;8EAG5C,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,gBAAgB;oEAElB,SAAS;8EAET,cAAA,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,gBAAgB;;;;;;;;;;;8EAGlD,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS;8EAET,cAAA,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAKtB,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,qBAAqB;;8EAEpC,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,oBAAoB,OAAO,yBAAyB;;;;;;;;;;;;;8DAKzD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;8EAAM,oBAAoB,OAAO,cAAc;;;;;;;;;;;;sEAElD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,6LAAC;8EAAM,oBAAoB,OAAO,aAAa;;;;;;;;;;;;sEAEjD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,mNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;8EACrB,6LAAC;8EAAM,oBAAoB,OAAO,gBAAgB;;;;;;;;;;;;sEAEpD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EACtB,6LAAC;8EAAM,oBAAoB,OAAO,YAAY;;;;;;;;;;;;;;;;;;;;;;;;sDAMpD,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;8DACZ,KAAK,GAAG,CAAC,CAAC;wDACT,MAAM,OAAO,IAAI,IAAI;wDACrB,qBACE,6LAAC;4DAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wFACA,cAAc,IAAI,EAAE,GAChB,8GACA;4DAEN,SAAS,IAAM,aAAa,IAAI,EAAE;;8EAElC,6LAAC;oEAAK,WAAU;;;;;;gEACf,IAAI,KAAK;;2DAVL,IAAI,EAAE;;;;;oDAajB;;;;;;8DAIF,6LAAC;oDAAI,WAAU;;wDACZ,cAAc,+BACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAU;0EACV,oBAAoB,OAChB,QAAQ,cAAc,IAAI,QAAQ,WAAW,GAC9C,QAAQ,WAAW;;;;;;;;;;;wDAK5B,cAAc,kCACb,6LAAC;4DAAI,WAAU;;gEACZ,QAAQ,cAAc,IAAI,OAAO,OAAO,CAAC,QAAQ,cAAc,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACjF,6LAAC;wEAAc,WAAU;;0FACvB,6LAAC;gFAAK,WAAU;0FAAkD;;;;;;0FAClE,6LAAC;gFAAK,WAAU;0FAAsC;;;;;;;uEAF9C;;;;;gEAKX,CAAC,CAAC,QAAQ,cAAc,IAAI,OAAO,IAAI,CAAC,QAAQ,cAAc,EAAE,MAAM,KAAK,CAAC,mBAC3E,6LAAC;oEAAE,WAAU;8EACV,oBAAoB,OAAO,0BAA0B;;;;;;;;;;;;wDAM7D,cAAc,2BACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FACZ,QAAQ,MAAM,EAAE,QAAQ,MAAM;;;;;;0FAEjC,6LAAC;gFAAI,WAAU;0FACZ;uFAAI,MAAM;iFAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;wFAEH,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,WACA,IAAI,KAAK,KAAK,CAAC,QAAQ,MAAM,IAAI,KAC7B,iCACA;uFALD;;;;;;;;;;0FAUX,6LAAC;gFAAI,WAAU;;oFACZ,QAAQ,WAAW,IAAI;oFAAE;oFAAE,oBAAoB,OAAO,UAAU;;;;;;;;;;;;;;;;;;8EAKvE,6LAAC;oEAAE,WAAU;8EACV,oBAAoB,OAAO,0BAA0B;;;;;;;;;;;;wDAK3D,cAAc,4BACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,6LAAC;;8FACC,6LAAC;oFAAG,WAAU;8FACX,oBAAoB,OAAO,kBAAkB;;;;;;8FAEhD,6LAAC;oFAAE,WAAU;8FACV,oBAAoB,OACjB,uCACA;;;;;;;;;;;;;;;;;;8EAKV,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,6LAAC;;8FACC,6LAAC;oFAAG,WAAU;8FACX,oBAAoB,OAAO,mBAAmB;;;;;;8FAEjD,6LAAC;oFAAE,WAAU;8FACV,oBAAoB,OACjB,8BACA;;;;;;;;;;;;;;;;;;8EAKV,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,6MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;sFAClB,6LAAC;;8FACC,6LAAC;oFAAG,WAAU;8FACX,oBAAoB,OAAO,mBAAmB;;;;;;8FAEjD,6LAAC;oFAAE,WAAU;8FACV,oBAAoB,OACjB,gCACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAaxB,6LAAC,4LAAA,CAAA,kBAAe;8BACb,+BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,MAAM;4BAAE,SAAS;wBAAE;wBACnB,WAAU;wBACV,SAAS,IAAM,iBAAiB;kCAEhC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;4BAAI;4BACtB,SAAS;gCAAE,OAAO;4BAAE;4BACpB,MAAM;gCAAE,OAAO;4BAAI;4BACnB,WAAU;4BACV,SAAS,CAAC,IAAM,EAAE,eAAe;;8CAEjC,6LAAC,4IAAA,CAAA,gBAAa;oCACZ,KAAK,QAAQ,MAAM,CAAC,mBAAmB;oCACvC,KAAK,QAAQ,IAAI;oCACjB,OAAO;oCACP,QAAQ;oCACR,WAAU;oCACV,WAAU;;;;;;8CAEZ,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,iBAAiB;8CAEhC,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQvB,6LAAC,4LAAA,CAAA,kBAAe;8BACb,mCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,MAAM;4BAAE,SAAS;wBAAE;wBACnB,WAAU;wBACV,SAAS,IAAM,qBAAqB;kCAEpC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;gCAAK,SAAS;4BAAE;4BAClC,SAAS;gCAAE,OAAO;gCAAG,SAAS;4BAAE;4BAChC,MAAM;gCAAE,OAAO;gCAAK,SAAS;4BAAE;4BAC/B,WAAU;4BACV,SAAS,CAAC,IAAM,EAAE,eAAe;sCAEjC,cAAA,6LAAC,oJAAA,CAAA,qBAAkB;gCACjB,SAAS;gCACT,iBAAiB;gCACjB,SAAS,IAAM,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxD;GArmBgB;;QACO,+HAAA,CAAA,iBAAc;QACjB,6HAAA,CAAA,eAAY;QACR,iIAAA,CAAA,mBAAgB;;;KAHxB", "debugId": null}}, {"offset": {"line": 5712, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/augment-projects/ecommercepro/src/components/shop/ShopPageEnhanced.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useMemo, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport {\n  ShoppingCart, Heart, Star, ArrowRight, X, Search,\n  Grid, List, SlidersHorizontal, Tag, Filter, ChevronDown,\n  ArrowUpDown, ArrowDownUp, CheckCircle, Eye, Truck, Package,\n  Sparkles, TrendingUp, ShieldCheck, Clock, Zap,\n  Info, RefreshCw\n} from 'lucide-react';\nimport { Button } from '../ui/Button';\nimport { Card } from '../ui/Card';\nimport { Input } from '../ui/Input';\nimport { LazyImage } from '../ui/LazyImage';\nimport { EnhancedImage } from '../ui/EnhancedImage';\nimport { formatCurrency, cn } from '../../lib/utils';\nimport { useCartStore } from '../../stores/cartStore';\nimport { useWishlistStore } from '../../stores/wishlistStore';\nimport { useAuthStore } from '../../stores/authStore';\nimport { useTheme } from 'next-themes';\nimport { useLanguageStore } from '../../stores/languageStore';\nimport { useTranslation } from '../../translations';\nimport { AuthModal } from '../auth/AuthModal';\nimport { WholesaleQuoteForm } from '../forms/WholesaleQuoteForm';\nimport { products, productCategories } from '../../data/products';\nimport { ScrollAnimation, ScrollStagger, HoverAnimation } from '../ui/animations';\nimport { Product, ProductFiltersState, SortOption } from '../../types/index';\n\nimport { Badge } from '../ui/Badge';\nimport { Tooltip } from '../ui/Tooltip';\nimport { EnhancedProductFilters } from './EnhancedProductFilters';\nimport { ShopFooter } from './ShopFooter';\n\nimport { ProductCard } from '../product/ProductCard';\nimport { ProductDetailModal } from '../product/ProductDetailModal';\n\ninterface ShopPageEnhancedProps {\n  locale?: 'en' | 'ar';\n  initialFilters?: {\n    featured?: boolean;\n    newArrivals?: boolean;\n    category?: string;\n    searchQuery?: string;\n  };\n}\n\nexport const ShopPageEnhanced = ({ locale: propLocale, initialFilters }: ShopPageEnhancedProps) => {\n  const router = useRouter();\n  const [showAuthModal, setShowAuthModal] = useState(false);\n  const [showWholesaleForm, setShowWholesaleForm] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);\n  const [showMobileFilters, setShowMobileFilters] = useState(false);\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n  const [quickViewProduct, setQuickViewProduct] = useState<Product | null>(null);\n  const [sortOption, setSortOption] = useState<SortOption>('featured');\n  const [isLoading, setIsLoading] = useState(true);\n  const [showSortDropdown, setShowSortDropdown] = useState(false);\n  const [activeFiltersCount, setActiveFiltersCount] = useState(0);\n  const [showSuccessToast, setShowSuccessToast] = useState(false);\n  const [toastMessage, setToastMessage] = useState('');\n  const [toastType, setToastType] = useState<'success' | 'error' | 'info'>('success');\n\n  const maxPrice = useMemo(() => products.reduce((max, p) => p.price > max ? p.price : max, 0), [products]);\n\n  const [filters, setFilters] = useState<ProductFiltersState>({\n    category: initialFilters?.category || 'all',\n    priceRange: { min: 0, max: maxPrice || 50000 },\n    inStock: false,\n    onSale: false,\n    featured: initialFilters?.featured || false,\n    newArrivals: initialFilters?.newArrivals || false,\n    searchQuery: initialFilters?.searchQuery || ''\n  });\n\n  // تحديث الفلاتر عند تغير السعر الأقصى\n  useEffect(() => {\n    setFilters(prevFilters => ({\n      ...prevFilters,\n      priceRange: {\n        ...prevFilters.priceRange,\n        max: maxPrice || 50000\n      }\n    }));\n  }, [maxPrice]);\n\n  // محاكاة تحميل البيانات\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setIsLoading(false);\n    }, 600); // تقليل وقت التحميل لتحسين تجربة المستخدم\n    return () => clearTimeout(timer);\n  }, []);\n\n  // إغلاق قائمة الترتيب عند النقر خارجها\n  useEffect(() => {\n    const handleClickOutside = () => {\n      if (showSortDropdown) {\n        setShowSortDropdown(false);\n      }\n    };\n\n    document.addEventListener('click', handleClickOutside);\n    return () => {\n      document.removeEventListener('click', handleClickOutside);\n    };\n  }, [showSortDropdown]);\n\n  // حساب عدد الفلاتر النشطة\n  useEffect(() => {\n    let count = 0;\n    if (filters.category !== 'all') count++;\n    if (filters.inStock) count++;\n    if (filters.onSale) count++;\n    if (filters.featured) count++;\n    if (filters.newArrivals) count++;\n    if (filters.searchQuery) count++;\n    if (filters.priceRange.min > 0 || filters.priceRange.max < maxPrice) count++;\n    setActiveFiltersCount(count);\n  }, [filters, maxPrice]);\n\n  // إظهار رسالة نجاح\n  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'success') => {\n    setToastMessage(message);\n    setToastType(type);\n    setShowSuccessToast(true);\n\n    setTimeout(() => {\n      setShowSuccessToast(false);\n    }, 3000);\n  };\n\n  const cartStore = useCartStore();\n  const wishlistStore = useWishlistStore();\n  const { user } = useAuthStore();\n  const { theme, resolvedTheme } = useTheme();\n  const { language } = useLanguageStore();\n  const { t, locale } = useTranslation();\n\n  const currentLanguage = propLocale || (locale as 'ar' | 'en') || language;\n  const isRTL = currentLanguage === 'ar';\n\n  // تصفية المنتجات حسب الفلاتر\n  const filteredProducts = useMemo(() => {\n    return products.filter(product => {\n      // تصفية حسب الفئة\n      if (filters.category !== 'all' && product.category !== filters.category) return false;\n\n      // تصفية حسب المخزون\n      if (filters.inStock && product.stock <= 0) return false;\n\n      // تصفية حسب العروض\n      if (filters.onSale && (!product.compareAtPrice || product.compareAtPrice <= product.price)) return false;\n\n      // تصفية حسب المنتجات المميزة\n      if (filters.featured && !product.featured) return false;\n\n      // تصفية حسب المنتجات الجديدة (آخر 30 يوم)\n      if (filters.newArrivals) {\n        const thirtyDaysAgo = new Date();\n        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n        const productDate = new Date(product.createdAt);\n        if (productDate < thirtyDaysAgo) return false;\n      }\n\n      // تصفية حسب نطاق السعر\n      if (product.price < filters.priceRange.min || product.price > filters.priceRange.max) return false;\n\n      // تصفية حسب البحث\n      if (filters.searchQuery) {\n        const query = filters.searchQuery.toLowerCase();\n        const nameMatch = product.name.toLowerCase().includes(query);\n        const nameArMatch = product.name_ar ? product.name_ar.toLowerCase().includes(query) : false;\n        const descMatch = product.description.toLowerCase().includes(query);\n        const descArMatch = product.description_ar ? product.description_ar.toLowerCase().includes(query) : false;\n        const categoryMatch = product.category.toLowerCase().includes(query);\n        const tagsMatch = product.tags.some(tag => tag.toLowerCase().includes(query));\n\n        return nameMatch || nameArMatch || descMatch || descArMatch || categoryMatch || tagsMatch;\n      }\n\n      return true;\n    });\n  }, [filters]);\n\n  // ترتيب المنتجات حسب الخيار المحدد\n  const sortedProducts = useMemo(() => {\n    let sorted = [...filteredProducts];\n\n    switch (sortOption) {\n      case 'featured':\n        // ترتيب المنتجات المميزة أولاً، ثم حسب التقييم والشعبية\n        return sorted.sort((a, b) => {\n          if (a.featured && !b.featured) return -1;\n          if (!a.featured && b.featured) return 1;\n\n          // إذا كانت كلاهما مميزة أو غير مميزة، رتب حسب التقييم ثم المراجعات\n          const aRating = a.rating || 0;\n          const bRating = b.rating || 0;\n          if (aRating !== bRating) return bRating - aRating;\n\n          const aReviews = a.reviewCount || 0;\n          const bReviews = b.reviewCount || 0;\n          return bReviews - aReviews;\n        });\n\n      case 'newest':\n        // ترتيب حسب تاريخ الإضافة (الأحدث أولاً)\n        return sorted.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n\n      case 'price-asc':\n        // ترتيب حسب السعر (من الأقل إلى الأعلى)\n        return sorted.sort((a, b) => a.price - b.price);\n\n      case 'price-desc':\n        // ترتيب حسب السعر (من الأعلى إلى الأقل)\n        return sorted.sort((a, b) => b.price - a.price);\n\n      case 'popular':\n        // ترتيب حسب الشعبية (التقييم والمراجعات والمبيعات)\n        return sorted.sort((a, b) => {\n          const aRating = a.rating || 0;\n          const bRating = b.rating || 0;\n          const aReviews = a.reviewCount || 0;\n          const bReviews = b.reviewCount || 0;\n          const aSales = a.salesCount || 0;\n          const bSales = b.salesCount || 0;\n\n          // حساب نقاط الشعبية\n          const aPopularityScore = (aRating * 0.4) + (aReviews * 0.3) + (aSales * 0.3);\n          const bPopularityScore = (bRating * 0.4) + (bReviews * 0.3) + (bSales * 0.3);\n\n          return bPopularityScore - aPopularityScore;\n        });\n\n      case 'discount':\n        // ترتيب حسب نسبة الخصم (الأعلى أولاً)\n        return sorted.sort((a, b) => {\n          const aDiscount = a.compareAtPrice ? ((a.compareAtPrice - a.price) / a.compareAtPrice) * 100 : 0;\n          const bDiscount = b.compareAtPrice ? ((b.compareAtPrice - b.price) / b.compareAtPrice) * 100 : 0;\n          return bDiscount - aDiscount;\n        });\n\n      case 'rating':\n        // ترتيب حسب التقييم (الأعلى أولاً)\n        return sorted.sort((a, b) => {\n          const aRating = a.rating || 0;\n          const bRating = b.rating || 0;\n          if (aRating !== bRating) return bRating - aRating;\n\n          // إذا كان التقييم متساوي، رتب حسب عدد المراجعات\n          const aReviews = a.reviewCount || 0;\n          const bReviews = b.reviewCount || 0;\n          return bReviews - aReviews;\n        });\n\n      case 'name-asc':\n        // ترتيب حسب الاسم (أبجدياً تصاعدي)\n        return sorted.sort((a, b) => {\n          const aName = currentLanguage === 'ar' ? (a.name_ar || a.name) : a.name;\n          const bName = currentLanguage === 'ar' ? (b.name_ar || b.name) : b.name;\n          return aName.localeCompare(bName, currentLanguage === 'ar' ? 'ar' : 'en');\n        });\n\n      case 'name-desc':\n        // ترتيب حسب الاسم (أبجدياً تنازلي)\n        return sorted.sort((a, b) => {\n          const aName = currentLanguage === 'ar' ? (a.name_ar || a.name) : a.name;\n          const bName = currentLanguage === 'ar' ? (b.name_ar || b.name) : b.name;\n          return bName.localeCompare(aName, currentLanguage === 'ar' ? 'ar' : 'en');\n        });\n\n      case 'stock':\n        // ترتيب حسب المخزون (الأعلى أولاً)\n        return sorted.sort((a, b) => {\n          // المنتجات المتوفرة أولاً\n          if (a.stock > 0 && b.stock <= 0) return -1;\n          if (a.stock <= 0 && b.stock > 0) return 1;\n\n          // ثم ترتيب حسب كمية المخزون\n          return b.stock - a.stock;\n        });\n\n      default:\n        return sorted;\n    }\n  }, [filteredProducts, sortOption, currentLanguage]);\n\n  // إضافة المنتج إلى السلة - يمكن للمستخدم إضافة المنتج إلى السلة حتى لو لم يكن مسجل دخول\n  const handleAddToCart = (product: Product) => {\n    // التحقق من توفر المنتج في المخزون\n    if (!product.inStock && product.stock <= 0) {\n      const message = currentLanguage === 'ar'\n        ? `المنتج ${product.name} غير متوفر في المخزون`\n        : `${product.name} is out of stock`;\n      showToast(message, 'error');\n      return;\n    }\n\n    // تجهيز بيانات المنتج للإضافة إلى السلة\n    const cartItem = {\n      id: product.id,\n      name: product.name,\n      name_ar: product.name_ar || product.name,\n      price: product.price,\n      image: product.images && product.images.length > 0 ? product.images[0] : '/images/product-placeholder-light.svg',\n      quantity: 1,\n    };\n\n    // إضافة المنتج إلى السلة\n    cartStore.addItem(cartItem, 1);\n\n    // إظهار رسالة نجاح\n    const message = currentLanguage === 'ar'\n      ? `تمت إضافة ${product.name} إلى سلة التسوق`\n      : `${product.name} added to cart`;\n    showToast(message, 'success');\n  };\n\n  // فتح نموذج طلب عرض سعر للجملة - لا يتطلب تسجيل الدخول\n  const handleWholesaleInquiry = (product: Product) => {\n    setSelectedProduct(product);\n    setShowWholesaleForm(true);\n  };\n\n  // إضافة/إزالة المنتج من المفضلة - لا يتطلب تسجيل الدخول\n  const toggleWishlist = (product: Product) => {\n    if (wishlistStore.isInWishlist(product.id)) {\n      wishlistStore.removeItem(product.id);\n      const message = currentLanguage === 'ar'\n        ? `تمت إزالة ${product.name} من المفضلة`\n        : `${product.name} removed from wishlist`;\n      showToast(message, 'info');\n    } else {\n      wishlistStore.addItem(product);\n      const message = currentLanguage === 'ar'\n        ? `تمت إضافة ${product.name} إلى المفضلة`\n        : `${product.name} added to wishlist`;\n      showToast(message, 'success');\n    }\n  };\n\n  const handleQuickView = (product: Product) => {\n    setQuickViewProduct(product);\n  };\n\n  const resetFilters = () => {\n    setFilters({\n      category: 'all',\n      priceRange: { min: 0, max: maxPrice || 50000 },\n      inStock: false,\n      onSale: false,\n      featured: false,\n      newArrivals: false,\n      searchQuery: ''\n    });\n    setSortOption('featured');\n    setShowMobileFilters(false);\n\n    // إظهار رسالة إعادة تعيين الفلاتر\n    const message = currentLanguage === 'ar'\n      ? 'تم إعادة تعيين جميع الفلاتر'\n      : 'All filters have been reset';\n    showToast(message, 'info');\n  };\n\n  // تبديل وضع العرض (شبكة/قائمة)\n  const toggleViewMode = () => {\n    setViewMode(prev => prev === 'grid' ? 'list' : 'grid');\n  };\n\n\n\n\n\n\n\n  // تحديث URL مع الفلاتر النشطة\n  const updateUrlWithFilters = () => {\n    const params = new URLSearchParams();\n    if (filters.featured) params.set('featured', 'true');\n    if (filters.category !== 'all') params.set('category', filters.category);\n    if (filters.searchQuery) params.set('q', filters.searchQuery);\n    if (filters.onSale) params.set('sale', 'true');\n    if (filters.inStock) params.set('instock', 'true');\n    if (filters.newArrivals) params.set('new', 'true');\n    if (filters.priceRange.min > 0) params.set('min', filters.priceRange.min.toString());\n    if (filters.priceRange.max < maxPrice) params.set('max', filters.priceRange.max.toString());\n\n    const url = `/${currentLanguage}/shop${params.toString() ? `?${params.toString()}` : ''}`;\n    router.push(url, { scroll: false });\n  };\n\n  // تحديث URL عند تغيير الفلاتر\n  useEffect(() => {\n    updateUrlWithFilters();\n  }, [filters]);\n\n  return (\n    <div className=\"container-custom py-8\">\n      {/* محتوى المتجر الرئيسي */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n        {/* الشريط الجانبي */}\n        <div className=\"lg:col-span-1\">\n          {/* فلاتر المنتجات */}\n          <div className=\"sticky top-24\">\n            <EnhancedProductFilters\n              filters={filters}\n              setFilters={setFilters}\n              resetFilters={resetFilters}\n              maxPrice={maxPrice}\n              productCategories={productCategories}\n              showMobileFilters={showMobileFilters}\n              setShowMobileFilters={setShowMobileFilters}\n              activeFiltersCount={activeFiltersCount}\n              tags={Array.from(new Set(products.flatMap(p => p.tags)))}\n            />\n          </div>\n\n\n\n\n        </div>\n\n        {/* شبكة المنتجات */}\n        <div className=\"lg:col-span-3\">\n          {/* أدوات الترتيب وتبديل طريقة العرض */}\n          <div className=\"flex flex-wrap justify-between items-center mb-6 bg-white dark:bg-slate-800 p-4 rounded-lg shadow-sm\">\n            <div className=\"flex items-center mb-2 sm:mb-0\">\n              <span className=\"text-sm text-slate-600 dark:text-slate-400 mr-2\">\n                {currentLanguage === 'ar'\n                  ? `${sortedProducts.length} منتج`\n                  : `${sortedProducts.length} products`}\n              </span>\n\n              <div className=\"relative\">\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  className=\"flex items-center min-w-[160px] justify-between\"\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    setShowSortDropdown(!showSortDropdown);\n                  }}\n                >\n                  <div className=\"flex items-center\">\n                    <SlidersHorizontal className=\"h-4 w-4 mr-2\" />\n                    <span className=\"text-sm\">\n                      {sortOption === 'featured' ? (currentLanguage === 'ar' ? 'المميزة' : 'Featured') :\n                       sortOption === 'newest' ? (currentLanguage === 'ar' ? 'الأحدث' : 'Newest') :\n                       sortOption === 'popular' ? (currentLanguage === 'ar' ? 'الأكثر شعبية' : 'Popular') :\n                       sortOption === 'price-asc' ? (currentLanguage === 'ar' ? 'السعر ↑' : 'Price ↑') :\n                       sortOption === 'price-desc' ? (currentLanguage === 'ar' ? 'السعر ↓' : 'Price ↓') :\n                       sortOption === 'discount' ? (currentLanguage === 'ar' ? 'أعلى خصم' : 'Best Deals') :\n                       sortOption === 'rating' ? (currentLanguage === 'ar' ? 'أعلى تقييم' : 'Top Rated') :\n                       sortOption === 'name-asc' ? (currentLanguage === 'ar' ? 'الاسم ↑' : 'Name ↑') :\n                       sortOption === 'name-desc' ? (currentLanguage === 'ar' ? 'الاسم ↓' : 'Name ↓') :\n                       sortOption === 'stock' ? (currentLanguage === 'ar' ? 'المخزون' : 'Stock') :\n                       (currentLanguage === 'ar' ? 'ترتيب حسب' : 'Sort by')}\n                    </span>\n                  </div>\n                  <ChevronDown className={`h-4 w-4 transition-transform ${showSortDropdown ? 'rotate-180' : ''}`} />\n                </Button>\n\n                {showSortDropdown && (\n                  <div className=\"absolute z-10 mt-1 w-64 bg-white dark:bg-slate-800 rounded-lg shadow-xl border border-slate-200 dark:border-slate-700 overflow-hidden\">\n                    <div className=\"py-2\">\n                      {/* Popular Options */}\n                      <div className=\"px-3 py-1 text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider border-b border-slate-100 dark:border-slate-700\">\n                        {currentLanguage === 'ar' ? 'الأكثر استخداماً' : 'Most Used'}\n                      </div>\n                      <button\n                        className={`w-full text-left px-4 py-3 text-sm transition-colors ${sortOption === 'featured' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700'}`}\n                        onClick={() => {\n                          setSortOption('featured');\n                          setShowSortDropdown(false);\n                        }}\n                      >\n                        <div className=\"flex items-center\">\n                          <span className=\"font-medium\">{currentLanguage === 'ar' ? 'المميزة' : 'Featured'}</span>\n                          <span className=\"ml-2 text-xs text-slate-500 dark:text-slate-400\">\n                            {currentLanguage === 'ar' ? 'الأفضل أولاً' : 'Best first'}\n                          </span>\n                        </div>\n                      </button>\n                      <button\n                        className={`w-full text-left px-4 py-3 text-sm transition-colors ${sortOption === 'popular' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700'}`}\n                        onClick={() => {\n                          setSortOption('popular');\n                          setShowSortDropdown(false);\n                        }}\n                      >\n                        <div className=\"flex items-center\">\n                          <span className=\"font-medium\">{currentLanguage === 'ar' ? 'الأكثر شعبية' : 'Most Popular'}</span>\n                          <span className=\"ml-2 text-xs text-slate-500 dark:text-slate-400\">\n                            {currentLanguage === 'ar' ? 'حسب التقييم والمبيعات' : 'By rating & sales'}\n                          </span>\n                        </div>\n                      </button>\n                      <button\n                        className={`w-full text-left px-4 py-3 text-sm transition-colors ${sortOption === 'newest' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700'}`}\n                        onClick={() => {\n                          setSortOption('newest');\n                          setShowSortDropdown(false);\n                        }}\n                      >\n                        <div className=\"flex items-center\">\n                          <span className=\"font-medium\">{currentLanguage === 'ar' ? 'الأحدث' : 'Newest'}</span>\n                          <span className=\"ml-2 text-xs text-slate-500 dark:text-slate-400\">\n                            {currentLanguage === 'ar' ? 'وصل حديثاً' : 'Latest arrivals'}\n                          </span>\n                        </div>\n                      </button>\n\n                      {/* Price Options */}\n                      <div className=\"px-3 py-1 text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider border-b border-slate-100 dark:border-slate-700 mt-2\">\n                        {currentLanguage === 'ar' ? 'السعر' : 'Price'}\n                      </div>\n                      <button\n                        className={`w-full text-left px-4 py-3 text-sm transition-colors ${sortOption === 'price-asc' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700'}`}\n                        onClick={() => {\n                          setSortOption('price-asc');\n                          setShowSortDropdown(false);\n                        }}\n                      >\n                        <div className=\"flex items-center\">\n                          <span className=\"font-medium\">{currentLanguage === 'ar' ? 'السعر: من الأقل إلى الأعلى' : 'Price: Low to High'}</span>\n                        </div>\n                      </button>\n                      <button\n                        className={`w-full text-left px-4 py-3 text-sm transition-colors ${sortOption === 'price-desc' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700'}`}\n                        onClick={() => {\n                          setSortOption('price-desc');\n                          setShowSortDropdown(false);\n                        }}\n                      >\n                        <div className=\"flex items-center\">\n                          <span className=\"font-medium\">{currentLanguage === 'ar' ? 'السعر: من الأعلى إلى الأقل' : 'Price: High to Low'}</span>\n                        </div>\n                      </button>\n                      <button\n                        className={`w-full text-left px-4 py-3 text-sm transition-colors ${sortOption === 'discount' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700'}`}\n                        onClick={() => {\n                          setSortOption('discount');\n                          setShowSortDropdown(false);\n                        }}\n                      >\n                        <div className=\"flex items-center\">\n                          <span className=\"font-medium\">{currentLanguage === 'ar' ? 'أعلى خصم' : 'Biggest Discount'}</span>\n                          <span className=\"ml-2 text-xs text-slate-500 dark:text-slate-400\">\n                            {currentLanguage === 'ar' ? 'أفضل العروض' : 'Best deals'}\n                          </span>\n                        </div>\n                      </button>\n\n                      {/* Other Options */}\n                      <div className=\"px-3 py-1 text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider border-b border-slate-100 dark:border-slate-700 mt-2\">\n                        {currentLanguage === 'ar' ? 'خيارات أخرى' : 'Other Options'}\n                      </div>\n                      <button\n                        className={`w-full text-left px-4 py-3 text-sm transition-colors ${sortOption === 'rating' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700'}`}\n                        onClick={() => {\n                          setSortOption('rating');\n                          setShowSortDropdown(false);\n                        }}\n                      >\n                        <div className=\"flex items-center\">\n                          <span className=\"font-medium\">{currentLanguage === 'ar' ? 'أعلى تقييم' : 'Highest Rated'}</span>\n                          <span className=\"ml-2 text-xs text-slate-500 dark:text-slate-400\">\n                            {currentLanguage === 'ar' ? 'حسب النجوم' : 'By stars'}\n                          </span>\n                        </div>\n                      </button>\n                      <button\n                        className={`w-full text-left px-4 py-3 text-sm transition-colors ${sortOption === 'name-asc' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700'}`}\n                        onClick={() => {\n                          setSortOption('name-asc');\n                          setShowSortDropdown(false);\n                        }}\n                      >\n                        <div className=\"flex items-center\">\n                          <span className=\"font-medium\">{currentLanguage === 'ar' ? 'الاسم: أ - ي' : 'Name: A - Z'}</span>\n                          <span className=\"ml-2 text-xs text-slate-500 dark:text-slate-400\">\n                            {currentLanguage === 'ar' ? 'أبجدياً' : 'Alphabetical'}\n                          </span>\n                        </div>\n                      </button>\n                      <button\n                        className={`w-full text-left px-4 py-3 text-sm transition-colors ${sortOption === 'name-desc' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700'}`}\n                        onClick={() => {\n                          setSortOption('name-desc');\n                          setShowSortDropdown(false);\n                        }}\n                      >\n                        <div className=\"flex items-center\">\n                          <span className=\"font-medium\">{currentLanguage === 'ar' ? 'الاسم: ي - أ' : 'Name: Z - A'}</span>\n                          <span className=\"ml-2 text-xs text-slate-500 dark:text-slate-400\">\n                            {currentLanguage === 'ar' ? 'أبجدياً عكسي' : 'Reverse alphabetical'}\n                          </span>\n                        </div>\n                      </button>\n                      <button\n                        className={`w-full text-left px-4 py-3 text-sm transition-colors ${sortOption === 'stock' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700'}`}\n                        onClick={() => {\n                          setSortOption('stock');\n                          setShowSortDropdown(false);\n                        }}\n                      >\n                        <div className=\"flex items-center\">\n                          <span className=\"font-medium\">{currentLanguage === 'ar' ? 'حسب المخزون' : 'By Stock'}</span>\n                          <span className=\"ml-2 text-xs text-slate-500 dark:text-slate-400\">\n                            {currentLanguage === 'ar' ? 'المتوفر أولاً' : 'Available first'}\n                          </span>\n                        </div>\n                      </button>\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            <div className=\"flex items-center\">\n              {/* Enhanced View Mode Toggle */}\n              <div className=\"flex items-center gap-1 bg-slate-100 dark:bg-slate-800 rounded-lg p-1 mr-3 border border-slate-200 dark:border-slate-700\">\n                <Tooltip content={currentLanguage === 'ar' ? 'عرض شبكي' : 'Grid view'}>\n                  <Button\n                    variant={viewMode === 'grid' ? 'primary' : 'ghost'}\n                    size=\"sm\"\n                    onClick={() => setViewMode('grid')}\n                    className={cn(\n                      \"p-2 transition-all duration-300 rounded-md\",\n                      viewMode === 'grid'\n                        ? \"bg-primary-500 text-white shadow-md scale-105\"\n                        : \"text-slate-600 dark:text-slate-300 hover:bg-slate-200 dark:hover:bg-slate-700\"\n                    )}\n                    aria-label={currentLanguage === 'ar' ? 'عرض شبكي' : 'Grid view'}\n                  >\n                    <Grid className=\"h-4 w-4\" />\n                  </Button>\n                </Tooltip>\n                <Tooltip content={currentLanguage === 'ar' ? 'عرض قائمة' : 'List view'}>\n                  <Button\n                    variant={viewMode === 'list' ? 'primary' : 'ghost'}\n                    size=\"sm\"\n                    onClick={() => setViewMode('list')}\n                    className={cn(\n                      \"p-2 transition-all duration-300 rounded-md\",\n                      viewMode === 'list'\n                        ? \"bg-primary-500 text-white shadow-md scale-105\"\n                        : \"text-slate-600 dark:text-slate-300 hover:bg-slate-200 dark:hover:bg-slate-700\"\n                    )}\n                    aria-label={currentLanguage === 'ar' ? 'عرض قائمة' : 'List view'}\n                  >\n                    <List className=\"h-4 w-4\" />\n                  </Button>\n                </Tooltip>\n              </div>\n\n              <Button\n                variant={activeFiltersCount > 0 ? \"default\" : \"outline\"}\n                size=\"sm\"\n                onClick={() => setShowMobileFilters(!showMobileFilters)}\n                className=\"lg:hidden\"\n              >\n                <Filter className=\"h-4 w-4 mr-2\" />\n                {currentLanguage === 'ar' ? 'الفلاتر' : 'Filters'}\n                {activeFiltersCount > 0 && (\n                  <Badge variant=\"secondary\" className=\"ml-2 bg-white text-primary-700\">\n                    {activeFiltersCount}\n                  </Badge>\n                )}\n              </Button>\n            </div>\n          </div>\n\n          {/* شبكة المنتجات */}\n          {isLoading ? (\n            // حالة التحميل\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {Array.from({ length: 6 }).map((_, index) => (\n                <div key={index} className=\"bg-white dark:bg-slate-800 rounded-lg overflow-hidden animate-pulse border border-slate-200 dark:border-slate-700 shadow-sm\">\n                  <div className=\"aspect-square bg-slate-200 dark:bg-slate-700 relative\">\n                    {/* محاكاة شارات المنتج */}\n                    <div className=\"absolute top-2 left-2 h-5 w-16 bg-slate-300 dark:bg-slate-600 rounded-full\"></div>\n                    {/* محاكاة أزرار الإجراءات */}\n                    <div className=\"absolute bottom-2 right-2 flex gap-1\">\n                      <div className=\"h-8 w-8 bg-slate-300 dark:bg-slate-600 rounded-full\"></div>\n                      <div className=\"h-8 w-8 bg-slate-300 dark:bg-slate-600 rounded-full\"></div>\n                    </div>\n                  </div>\n                  <div className=\"p-4 space-y-3\">\n                    {/* محاكاة الفئة والتقييم */}\n                    <div className=\"flex justify-between\">\n                      <div className=\"h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/4\"></div>\n                      <div className=\"h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/4\"></div>\n                    </div>\n                    {/* محاكاة اسم المنتج */}\n                    <div className=\"h-5 bg-slate-200 dark:bg-slate-700 rounded w-3/4\"></div>\n                    {/* محاكاة وصف المنتج */}\n                    <div className=\"h-4 bg-slate-200 dark:bg-slate-700 rounded w-full\"></div>\n                    <div className=\"h-4 bg-slate-200 dark:bg-slate-700 rounded w-2/3\"></div>\n                    {/* محاكاة السعر والمخزون */}\n                    <div className=\"flex justify-between pt-2\">\n                      <div className=\"h-5 bg-slate-200 dark:bg-slate-700 rounded w-1/3\"></div>\n                      <div className=\"h-5 bg-slate-200 dark:bg-slate-700 rounded w-1/5\"></div>\n                    </div>\n                    {/* محاكاة زر الإضافة إلى السلة */}\n                    <div className=\"h-9 bg-slate-200 dark:bg-slate-700 rounded w-full mt-4\"></div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : sortedProducts.length === 0 ? (\n            // لا توجد منتجات\n            <div className=\"bg-white dark:bg-slate-800 rounded-lg p-8 text-center border border-slate-200 dark:border-slate-700 shadow-sm\">\n              <div className=\"flex justify-center mb-6\">\n                <div className=\"relative\">\n                  <div className=\"absolute -inset-4 rounded-full bg-slate-100 dark:bg-slate-700/50 animate-pulse\"></div>\n                  <Package className=\"h-16 w-16 text-slate-400 dark:text-slate-500 relative z-10\" />\n                </div>\n              </div>\n              <h3 className=\"text-2xl font-semibold text-slate-900 dark:text-white mb-3\">\n                {currentLanguage === 'ar' ? 'لا توجد منتجات' : 'No Products Found'}\n              </h3>\n              <p className=\"text-slate-600 dark:text-slate-400 mb-6 max-w-md mx-auto\">\n                {currentLanguage === 'ar'\n                  ? 'لم نتمكن من العثور على أي منتجات تطابق معايير البحث الخاصة بك. يرجى تجربة معايير بحث مختلفة أو إعادة تعيين الفلاتر.'\n                  : 'We couldn\\'t find any products that match your search criteria. Try different search terms or reset the filters.'}\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-3 justify-center\">\n                <Button variant=\"default\" onClick={resetFilters} className=\"flex items-center justify-center\">\n                  <RefreshCw className=\"h-4 w-4 mr-2\" />\n                  {currentLanguage === 'ar' ? 'إعادة تعيين الفلاتر' : 'Reset Filters'}\n                </Button>\n                <Button\n                  variant=\"outline\"\n                  onClick={() => {\n                    setFilters({\n                      category: 'all',\n                      priceRange: { min: 0, max: maxPrice || 50000 },\n                      inStock: false,\n                      onSale: false,\n                      featured: false,\n                      newArrivals: false,\n                      searchQuery: ''\n                    });\n                    setSortOption('featured');\n                  }}\n                  className=\"flex items-center justify-center\"\n                >\n                  <Tag className=\"h-4 w-4 mr-2\" />\n                  {currentLanguage === 'ar' ? 'تصفح جميع المنتجات' : 'Browse All Products'}\n                </Button>\n              </div>\n            </div>\n          ) : (\n            // عرض المنتجات\n            <div>\n\n              {/* عرض جميع المنتجات */}\n              <div className=\"flex items-center mb-6\">\n                <h3 className=\"text-lg font-semibold text-slate-900 dark:text-white\">\n                  {filters.searchQuery ? (\n                    currentLanguage === 'ar' ? `نتائج البحث: \"${filters.searchQuery}\"` : `Search Results: \"${filters.searchQuery}\"`\n                  ) : filters.featured ? (\n                    currentLanguage === 'ar' ? 'المنتجات المميزة' : 'Featured Products'\n                  ) : filters.onSale ? (\n                    currentLanguage === 'ar' ? 'المنتجات المخفضة' : 'Sale Products'\n                  ) : filters.newArrivals ? (\n                    currentLanguage === 'ar' ? 'وصل حديثاً' : 'New Arrivals'\n                  ) : sortOption === 'newest' ? (\n                    currentLanguage === 'ar' ? 'أحدث المنتجات' : 'Newest Products'\n                  ) : sortOption === 'popular' ? (\n                    currentLanguage === 'ar' ? 'المنتجات الأكثر شعبية' : 'Most Popular Products'\n                  ) : sortOption === 'price-asc' ? (\n                    currentLanguage === 'ar' ? 'المنتجات من الأقل إلى الأعلى سعرًا' : 'Products: Low to High Price'\n                  ) : sortOption === 'price-desc' ? (\n                    currentLanguage === 'ar' ? 'المنتجات من الأعلى إلى الأقل سعرًا' : 'Products: High to Low Price'\n                  ) : sortOption === 'discount' ? (\n                    currentLanguage === 'ar' ? 'أفضل العروض والخصومات' : 'Best Deals & Discounts'\n                  ) : sortOption === 'rating' ? (\n                    currentLanguage === 'ar' ? 'المنتجات الأعلى تقييماً' : 'Highest Rated Products'\n                  ) : sortOption === 'name-asc' ? (\n                    currentLanguage === 'ar' ? 'المنتجات مرتبة أبجدياً' : 'Products A-Z'\n                  ) : sortOption === 'name-desc' ? (\n                    currentLanguage === 'ar' ? 'المنتجات مرتبة أبجدياً عكسي' : 'Products Z-A'\n                  ) : sortOption === 'stock' ? (\n                    currentLanguage === 'ar' ? 'المنتجات حسب المخزون' : 'Products by Stock'\n                  ) : (\n                    currentLanguage === 'ar' ? 'جميع المنتجات' : 'All Products'\n                  )}\n                </h3>\n              </div>\n\n              <div\n                className={cn(\n                  \"transition-all duration-500 ease-in-out\",\n                  viewMode === 'grid'\n                    ? \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\"\n                    : \"flex flex-col gap-4\"\n                )}\n                style={{\n                  minHeight: viewMode === 'list' ? 'auto' : '600px'\n                }}\n              >\n                {sortedProducts.map((product, index) => (\n                  <div\n                    key={product.id}\n                    className={cn(\n                      \"transition-all duration-300 ease-in-out\",\n                      viewMode === 'grid' ? \"transform hover:scale-105\" : \"transform hover:scale-102\"\n                    )}\n                    style={{\n                      animationDelay: `${index * 50}ms`\n                    }}\n                  >\n                    <ProductCard\n                      product={product}\n                      index={index}\n                      showQuickView={true}\n                      showAddToCart={true}\n                      showWishlist={true}\n                      showWholesale={true}\n                      onQuickView={handleQuickView}\n                      onAddToCart={handleAddToCart}\n                      onToggleWishlist={toggleWishlist}\n                      onRequestWholesale={handleWholesaleInquiry}\n                      variant={viewMode === 'list' ? 'list' : 'default'}\n                      className={cn(\n                        \"h-full transition-all duration-300\",\n                        viewMode === 'list' ? \"shadow-sm hover:shadow-md\" : \"shadow-sm hover:shadow-lg\"\n                      )}\n                    />\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* ترقيم الصفحات وقسم النشرة الإخبارية */}\n          <ShopFooter\n            totalProducts={sortedProducts.length}\n            currentPage={1}\n            itemsPerPage={12}\n            onPageChange={(page) => console.log(`Navigate to page ${page}`)}\n          />\n        </div>\n      </div>\n\n      {/* نافذة تفاصيل المنتج */}\n      <ProductDetailModal\n        product={quickViewProduct}\n        isOpen={!!quickViewProduct}\n        onClose={() => setQuickViewProduct(null)}\n      />\n\n      {/* نافذة طلب عرض سعر للجملة */}\n      <WholesaleQuoteForm\n        isOpen={showWholesaleForm && !!selectedProduct}\n        product={selectedProduct}\n        onClose={() => {\n          setShowWholesaleForm(false);\n          setSelectedProduct(null);\n        }}\n      />\n\n      {/* نافذة تسجيل الدخول */}\n      {showAuthModal && (\n        <AuthModal\n          onClose={() => setShowAuthModal(false)}\n          defaultTab=\"login\"\n        />\n      )}\n\n      {/* رسالة النجاح */}\n      {showSuccessToast && (\n        <div className={cn(\n          \"fixed bottom-4 right-4 z-50 p-4 rounded-lg shadow-xl max-w-md\",\n          \"animate-bounce-in transition-all duration-300\",\n          \"backdrop-blur-md border\",\n          toastType === 'success' ? \"bg-green-500/90 text-white border-green-400\" :\n          toastType === 'error' ? \"bg-red-500/90 text-white border-red-400\" :\n          \"bg-blue-500/90 text-white border-blue-400\"\n        )}>\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <div className={cn(\n                \"flex items-center justify-center w-8 h-8 rounded-full mr-3\",\n                toastType === 'success' ? \"bg-green-600\" :\n                toastType === 'error' ? \"bg-red-600\" :\n                \"bg-blue-600\"\n              )}>\n                {toastType === 'success' && <CheckCircle className=\"h-5 w-5\" />}\n                {toastType === 'error' && <X className=\"h-5 w-5\" />}\n                {toastType === 'info' && <Info className=\"h-5 w-5\" />}\n              </div>\n              <div>\n                <h4 className=\"font-medium mb-1\">\n                  {toastType === 'success' ? (currentLanguage === 'ar' ? 'تم بنجاح' : 'Success') :\n                   toastType === 'error' ? (currentLanguage === 'ar' ? 'خطأ' : 'Error') :\n                   (currentLanguage === 'ar' ? 'معلومات' : 'Information')}\n                </h4>\n                <p className=\"text-sm text-white/90\">{toastMessage}</p>\n              </div>\n            </div>\n            <button\n              onClick={() => setShowSuccessToast(false)}\n              className=\"ml-4 p-1 rounded-full hover:bg-white/20 transition-colors\"\n              aria-label={currentLanguage === 'ar' ? 'إغلاق' : 'Close'}\n            >\n              <X className=\"h-4 w-4\" />\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AAEA;AACA;;;AApCA;;;;;;;;;;;;;;;;;;;;;AAgDO,MAAM,mBAAmB,CAAC,EAAE,QAAQ,UAAU,EAAE,cAAc,EAAyB;;IAC5F,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACzE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IAEzE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE,IAAM,0HAAA,CAAA,WAAQ,CAAC,MAAM;sDAAC,CAAC,KAAK,IAAM,EAAE,KAAK,GAAG,MAAM,EAAE,KAAK,GAAG;qDAAK;6CAAI;QAAC,0HAAA,CAAA,WAAQ;KAAC;IAExG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;QAC1D,UAAU,gBAAgB,YAAY;QACtC,YAAY;YAAE,KAAK;YAAG,KAAK,YAAY;QAAM;QAC7C,SAAS;QACT,QAAQ;QACR,UAAU,gBAAgB,YAAY;QACtC,aAAa,gBAAgB,eAAe;QAC5C,aAAa,gBAAgB,eAAe;IAC9C;IAEA,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;8CAAW,CAAA,cAAe,CAAC;wBACzB,GAAG,WAAW;wBACd,YAAY;4BACV,GAAG,YAAY,UAAU;4BACzB,KAAK,YAAY;wBACnB;oBACF,CAAC;;QACH;qCAAG;QAAC;KAAS;IAEb,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,QAAQ;oDAAW;oBACvB,aAAa;gBACf;mDAAG,MAAM,0CAA0C;YACnD;8CAAO,IAAM,aAAa;;QAC5B;qCAAG,EAAE;IAEL,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;iEAAqB;oBACzB,IAAI,kBAAkB;wBACpB,oBAAoB;oBACtB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,SAAS;YACnC;8CAAO;oBACL,SAAS,mBAAmB,CAAC,SAAS;gBACxC;;QACF;qCAAG;QAAC;KAAiB;IAErB,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,QAAQ;YACZ,IAAI,QAAQ,QAAQ,KAAK,OAAO;YAChC,IAAI,QAAQ,OAAO,EAAE;YACrB,IAAI,QAAQ,MAAM,EAAE;YACpB,IAAI,QAAQ,QAAQ,EAAE;YACtB,IAAI,QAAQ,WAAW,EAAE;YACzB,IAAI,QAAQ,WAAW,EAAE;YACzB,IAAI,QAAQ,UAAU,CAAC,GAAG,GAAG,KAAK,QAAQ,UAAU,CAAC,GAAG,GAAG,UAAU;YACrE,sBAAsB;QACxB;qCAAG;QAAC;QAAS;KAAS;IAEtB,mBAAmB;IACnB,MAAM,YAAY,CAAC,SAAiB,OAAqC,SAAS;QAChF,gBAAgB;QAChB,aAAa;QACb,oBAAoB;QAEpB,WAAW;YACT,oBAAoB;QACtB,GAAG;IACL;IAEA,MAAM,YAAY,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAC7B,MAAM,gBAAgB,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD;IACrC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACxC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD;IACpC,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAEnC,MAAM,kBAAkB,cAAe,UAA0B;IACjE,MAAM,QAAQ,oBAAoB;IAElC,6BAA6B;IAC7B,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sDAAE;YAC/B,OAAO,0HAAA,CAAA,WAAQ,CAAC,MAAM;8DAAC,CAAA;oBACrB,kBAAkB;oBAClB,IAAI,QAAQ,QAAQ,KAAK,SAAS,QAAQ,QAAQ,KAAK,QAAQ,QAAQ,EAAE,OAAO;oBAEhF,oBAAoB;oBACpB,IAAI,QAAQ,OAAO,IAAI,QAAQ,KAAK,IAAI,GAAG,OAAO;oBAElD,mBAAmB;oBACnB,IAAI,QAAQ,MAAM,IAAI,CAAC,CAAC,QAAQ,cAAc,IAAI,QAAQ,cAAc,IAAI,QAAQ,KAAK,GAAG,OAAO;oBAEnG,6BAA6B;oBAC7B,IAAI,QAAQ,QAAQ,IAAI,CAAC,QAAQ,QAAQ,EAAE,OAAO;oBAElD,0CAA0C;oBAC1C,IAAI,QAAQ,WAAW,EAAE;wBACvB,MAAM,gBAAgB,IAAI;wBAC1B,cAAc,OAAO,CAAC,cAAc,OAAO,KAAK;wBAChD,MAAM,cAAc,IAAI,KAAK,QAAQ,SAAS;wBAC9C,IAAI,cAAc,eAAe,OAAO;oBAC1C;oBAEA,uBAAuB;oBACvB,IAAI,QAAQ,KAAK,GAAG,QAAQ,UAAU,CAAC,GAAG,IAAI,QAAQ,KAAK,GAAG,QAAQ,UAAU,CAAC,GAAG,EAAE,OAAO;oBAE7F,kBAAkB;oBAClB,IAAI,QAAQ,WAAW,EAAE;wBACvB,MAAM,QAAQ,QAAQ,WAAW,CAAC,WAAW;wBAC7C,MAAM,YAAY,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;wBACtD,MAAM,cAAc,QAAQ,OAAO,GAAG,QAAQ,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS;wBACtF,MAAM,YAAY,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC;wBAC7D,MAAM,cAAc,QAAQ,cAAc,GAAG,QAAQ,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS;wBACpG,MAAM,gBAAgB,QAAQ,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC;wBAC9D,MAAM,YAAY,QAAQ,IAAI,CAAC,IAAI;oFAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC;;wBAEtE,OAAO,aAAa,eAAe,aAAa,eAAe,iBAAiB;oBAClF;oBAEA,OAAO;gBACT;;QACF;qDAAG;QAAC;KAAQ;IAEZ,mCAAmC;IACnC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oDAAE;YAC7B,IAAI,SAAS;mBAAI;aAAiB;YAElC,OAAQ;gBACN,KAAK;oBACH,wDAAwD;oBACxD,OAAO,OAAO,IAAI;oEAAC,CAAC,GAAG;4BACrB,IAAI,EAAE,QAAQ,IAAI,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC;4BACvC,IAAI,CAAC,EAAE,QAAQ,IAAI,EAAE,QAAQ,EAAE,OAAO;4BAEtC,mEAAmE;4BACnE,MAAM,UAAU,EAAE,MAAM,IAAI;4BAC5B,MAAM,UAAU,EAAE,MAAM,IAAI;4BAC5B,IAAI,YAAY,SAAS,OAAO,UAAU;4BAE1C,MAAM,WAAW,EAAE,WAAW,IAAI;4BAClC,MAAM,WAAW,EAAE,WAAW,IAAI;4BAClC,OAAO,WAAW;wBACpB;;gBAEF,KAAK;oBACH,yCAAyC;oBACzC,OAAO,OAAO,IAAI;oEAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;;gBAE9F,KAAK;oBACH,wCAAwC;oBACxC,OAAO,OAAO,IAAI;oEAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;;gBAEhD,KAAK;oBACH,wCAAwC;oBACxC,OAAO,OAAO,IAAI;oEAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;;gBAEhD,KAAK;oBACH,mDAAmD;oBACnD,OAAO,OAAO,IAAI;oEAAC,CAAC,GAAG;4BACrB,MAAM,UAAU,EAAE,MAAM,IAAI;4BAC5B,MAAM,UAAU,EAAE,MAAM,IAAI;4BAC5B,MAAM,WAAW,EAAE,WAAW,IAAI;4BAClC,MAAM,WAAW,EAAE,WAAW,IAAI;4BAClC,MAAM,SAAS,EAAE,UAAU,IAAI;4BAC/B,MAAM,SAAS,EAAE,UAAU,IAAI;4BAE/B,oBAAoB;4BACpB,MAAM,mBAAmB,AAAC,UAAU,MAAQ,WAAW,MAAQ,SAAS;4BACxE,MAAM,mBAAmB,AAAC,UAAU,MAAQ,WAAW,MAAQ,SAAS;4BAExE,OAAO,mBAAmB;wBAC5B;;gBAEF,KAAK;oBACH,sCAAsC;oBACtC,OAAO,OAAO,IAAI;oEAAC,CAAC,GAAG;4BACrB,MAAM,YAAY,EAAE,cAAc,GAAG,AAAC,CAAC,EAAE,cAAc,GAAG,EAAE,KAAK,IAAI,EAAE,cAAc,GAAI,MAAM;4BAC/F,MAAM,YAAY,EAAE,cAAc,GAAG,AAAC,CAAC,EAAE,cAAc,GAAG,EAAE,KAAK,IAAI,EAAE,cAAc,GAAI,MAAM;4BAC/F,OAAO,YAAY;wBACrB;;gBAEF,KAAK;oBACH,mCAAmC;oBACnC,OAAO,OAAO,IAAI;oEAAC,CAAC,GAAG;4BACrB,MAAM,UAAU,EAAE,MAAM,IAAI;4BAC5B,MAAM,UAAU,EAAE,MAAM,IAAI;4BAC5B,IAAI,YAAY,SAAS,OAAO,UAAU;4BAE1C,gDAAgD;4BAChD,MAAM,WAAW,EAAE,WAAW,IAAI;4BAClC,MAAM,WAAW,EAAE,WAAW,IAAI;4BAClC,OAAO,WAAW;wBACpB;;gBAEF,KAAK;oBACH,mCAAmC;oBACnC,OAAO,OAAO,IAAI;oEAAC,CAAC,GAAG;4BACrB,MAAM,QAAQ,oBAAoB,OAAQ,EAAE,OAAO,IAAI,EAAE,IAAI,GAAI,EAAE,IAAI;4BACvE,MAAM,QAAQ,oBAAoB,OAAQ,EAAE,OAAO,IAAI,EAAE,IAAI,GAAI,EAAE,IAAI;4BACvE,OAAO,MAAM,aAAa,CAAC,OAAO,oBAAoB,OAAO,OAAO;wBACtE;;gBAEF,KAAK;oBACH,mCAAmC;oBACnC,OAAO,OAAO,IAAI;oEAAC,CAAC,GAAG;4BACrB,MAAM,QAAQ,oBAAoB,OAAQ,EAAE,OAAO,IAAI,EAAE,IAAI,GAAI,EAAE,IAAI;4BACvE,MAAM,QAAQ,oBAAoB,OAAQ,EAAE,OAAO,IAAI,EAAE,IAAI,GAAI,EAAE,IAAI;4BACvE,OAAO,MAAM,aAAa,CAAC,OAAO,oBAAoB,OAAO,OAAO;wBACtE;;gBAEF,KAAK;oBACH,mCAAmC;oBACnC,OAAO,OAAO,IAAI;oEAAC,CAAC,GAAG;4BACrB,0BAA0B;4BAC1B,IAAI,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,IAAI,GAAG,OAAO,CAAC;4BACzC,IAAI,EAAE,KAAK,IAAI,KAAK,EAAE,KAAK,GAAG,GAAG,OAAO;4BAExC,4BAA4B;4BAC5B,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;wBAC1B;;gBAEF;oBACE,OAAO;YACX;QACF;mDAAG;QAAC;QAAkB;QAAY;KAAgB;IAElD,wFAAwF;IACxF,MAAM,kBAAkB,CAAC;QACvB,mCAAmC;QACnC,IAAI,CAAC,QAAQ,OAAO,IAAI,QAAQ,KAAK,IAAI,GAAG;YAC1C,MAAM,UAAU,oBAAoB,OAChC,CAAC,OAAO,EAAE,QAAQ,IAAI,CAAC,qBAAqB,CAAC,GAC7C,GAAG,QAAQ,IAAI,CAAC,gBAAgB,CAAC;YACrC,UAAU,SAAS;YACnB;QACF;QAEA,wCAAwC;QACxC,MAAM,WAAW;YACf,IAAI,QAAQ,EAAE;YACd,MAAM,QAAQ,IAAI;YAClB,SAAS,QAAQ,OAAO,IAAI,QAAQ,IAAI;YACxC,OAAO,QAAQ,KAAK;YACpB,OAAO,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,IAAI,QAAQ,MAAM,CAAC,EAAE,GAAG;YACzE,UAAU;QACZ;QAEA,yBAAyB;QACzB,UAAU,OAAO,CAAC,UAAU;QAE5B,mBAAmB;QACnB,MAAM,UAAU,oBAAoB,OAChC,CAAC,UAAU,EAAE,QAAQ,IAAI,CAAC,eAAe,CAAC,GAC1C,GAAG,QAAQ,IAAI,CAAC,cAAc,CAAC;QACnC,UAAU,SAAS;IACrB;IAEA,uDAAuD;IACvD,MAAM,yBAAyB,CAAC;QAC9B,mBAAmB;QACnB,qBAAqB;IACvB;IAEA,wDAAwD;IACxD,MAAM,iBAAiB,CAAC;QACtB,IAAI,cAAc,YAAY,CAAC,QAAQ,EAAE,GAAG;YAC1C,cAAc,UAAU,CAAC,QAAQ,EAAE;YACnC,MAAM,UAAU,oBAAoB,OAChC,CAAC,UAAU,EAAE,QAAQ,IAAI,CAAC,WAAW,CAAC,GACtC,GAAG,QAAQ,IAAI,CAAC,sBAAsB,CAAC;YAC3C,UAAU,SAAS;QACrB,OAAO;YACL,cAAc,OAAO,CAAC;YACtB,MAAM,UAAU,oBAAoB,OAChC,CAAC,UAAU,EAAE,QAAQ,IAAI,CAAC,YAAY,CAAC,GACvC,GAAG,QAAQ,IAAI,CAAC,kBAAkB,CAAC;YACvC,UAAU,SAAS;QACrB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,oBAAoB;IACtB;IAEA,MAAM,eAAe;QACnB,WAAW;YACT,UAAU;YACV,YAAY;gBAAE,KAAK;gBAAG,KAAK,YAAY;YAAM;YAC7C,SAAS;YACT,QAAQ;YACR,UAAU;YACV,aAAa;YACb,aAAa;QACf;QACA,cAAc;QACd,qBAAqB;QAErB,kCAAkC;QAClC,MAAM,UAAU,oBAAoB,OAChC,gCACA;QACJ,UAAU,SAAS;IACrB;IAEA,+BAA+B;IAC/B,MAAM,iBAAiB;QACrB,YAAY,CAAA,OAAQ,SAAS,SAAS,SAAS;IACjD;IAQA,8BAA8B;IAC9B,MAAM,uBAAuB;QAC3B,MAAM,SAAS,IAAI;QACnB,IAAI,QAAQ,QAAQ,EAAE,OAAO,GAAG,CAAC,YAAY;QAC7C,IAAI,QAAQ,QAAQ,KAAK,OAAO,OAAO,GAAG,CAAC,YAAY,QAAQ,QAAQ;QACvE,IAAI,QAAQ,WAAW,EAAE,OAAO,GAAG,CAAC,KAAK,QAAQ,WAAW;QAC5D,IAAI,QAAQ,MAAM,EAAE,OAAO,GAAG,CAAC,QAAQ;QACvC,IAAI,QAAQ,OAAO,EAAE,OAAO,GAAG,CAAC,WAAW;QAC3C,IAAI,QAAQ,WAAW,EAAE,OAAO,GAAG,CAAC,OAAO;QAC3C,IAAI,QAAQ,UAAU,CAAC,GAAG,GAAG,GAAG,OAAO,GAAG,CAAC,OAAO,QAAQ,UAAU,CAAC,GAAG,CAAC,QAAQ;QACjF,IAAI,QAAQ,UAAU,CAAC,GAAG,GAAG,UAAU,OAAO,GAAG,CAAC,OAAO,QAAQ,UAAU,CAAC,GAAG,CAAC,QAAQ;QAExF,MAAM,MAAM,CAAC,CAAC,EAAE,gBAAgB,KAAK,EAAE,OAAO,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG,IAAI;QACzF,OAAO,IAAI,CAAC,KAAK;YAAE,QAAQ;QAAM;IACnC;IAEA,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG;QAAC;KAAQ;IAEZ,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCAEb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,uJAAA,CAAA,yBAAsB;gCACrB,SAAS;gCACT,YAAY;gCACZ,cAAc;gCACd,UAAU;gCACV,mBAAmB,0HAAA,CAAA,oBAAiB;gCACpC,mBAAmB;gCACnB,sBAAsB;gCACtB,oBAAoB;gCACpB,MAAM,MAAM,IAAI,CAAC,IAAI,IAAI,0HAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,CAAA,IAAK,EAAE,IAAI;;;;;;;;;;;;;;;;kCAU3D,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DACb,oBAAoB,OACjB,GAAG,eAAe,MAAM,CAAC,KAAK,CAAC,GAC/B,GAAG,eAAe,MAAM,CAAC,SAAS,CAAC;;;;;;0DAGzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,oBAAoB,CAAC;wDACvB;;0EAEA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,mOAAA,CAAA,oBAAiB;wEAAC,WAAU;;;;;;kFAC7B,6LAAC;wEAAK,WAAU;kFACb,eAAe,aAAc,oBAAoB,OAAO,YAAY,aACpE,eAAe,WAAY,oBAAoB,OAAO,WAAW,WACjE,eAAe,YAAa,oBAAoB,OAAO,iBAAiB,YACxE,eAAe,cAAe,oBAAoB,OAAO,YAAY,YACrE,eAAe,eAAgB,oBAAoB,OAAO,YAAY,YACtE,eAAe,aAAc,oBAAoB,OAAO,aAAa,eACrE,eAAe,WAAY,oBAAoB,OAAO,eAAe,cACrE,eAAe,aAAc,oBAAoB,OAAO,YAAY,WACpE,eAAe,cAAe,oBAAoB,OAAO,YAAY,WACrE,eAAe,UAAW,oBAAoB,OAAO,YAAY,UAChE,oBAAoB,OAAO,cAAc;;;;;;;;;;;;0EAG/C,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAW,CAAC,6BAA6B,EAAE,mBAAmB,eAAe,IAAI;;;;;;;;;;;;oDAG/F,kCACC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EAEb,6LAAC;oEAAI,WAAU;8EACZ,oBAAoB,OAAO,qBAAqB;;;;;;8EAEnD,6LAAC;oEACC,WAAW,CAAC,qDAAqD,EAAE,eAAe,aAAa,gFAAgF,gFAAgF;oEAC/P,SAAS;wEACP,cAAc;wEACd,oBAAoB;oEACtB;8EAEA,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAAe,oBAAoB,OAAO,YAAY;;;;;;0FACtE,6LAAC;gFAAK,WAAU;0FACb,oBAAoB,OAAO,iBAAiB;;;;;;;;;;;;;;;;;8EAInD,6LAAC;oEACC,WAAW,CAAC,qDAAqD,EAAE,eAAe,YAAY,gFAAgF,gFAAgF;oEAC9P,SAAS;wEACP,cAAc;wEACd,oBAAoB;oEACtB;8EAEA,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAAe,oBAAoB,OAAO,iBAAiB;;;;;;0FAC3E,6LAAC;gFAAK,WAAU;0FACb,oBAAoB,OAAO,0BAA0B;;;;;;;;;;;;;;;;;8EAI5D,6LAAC;oEACC,WAAW,CAAC,qDAAqD,EAAE,eAAe,WAAW,gFAAgF,gFAAgF;oEAC7P,SAAS;wEACP,cAAc;wEACd,oBAAoB;oEACtB;8EAEA,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAAe,oBAAoB,OAAO,WAAW;;;;;;0FACrE,6LAAC;gFAAK,WAAU;0FACb,oBAAoB,OAAO,eAAe;;;;;;;;;;;;;;;;;8EAMjD,6LAAC;oEAAI,WAAU;8EACZ,oBAAoB,OAAO,UAAU;;;;;;8EAExC,6LAAC;oEACC,WAAW,CAAC,qDAAqD,EAAE,eAAe,cAAc,gFAAgF,gFAAgF;oEAChQ,SAAS;wEACP,cAAc;wEACd,oBAAoB;oEACtB;8EAEA,cAAA,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;sFAAe,oBAAoB,OAAO,+BAA+B;;;;;;;;;;;;;;;;8EAG7F,6LAAC;oEACC,WAAW,CAAC,qDAAqD,EAAE,eAAe,eAAe,gFAAgF,gFAAgF;oEACjQ,SAAS;wEACP,cAAc;wEACd,oBAAoB;oEACtB;8EAEA,cAAA,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;sFAAe,oBAAoB,OAAO,+BAA+B;;;;;;;;;;;;;;;;8EAG7F,6LAAC;oEACC,WAAW,CAAC,qDAAqD,EAAE,eAAe,aAAa,gFAAgF,gFAAgF;oEAC/P,SAAS;wEACP,cAAc;wEACd,oBAAoB;oEACtB;8EAEA,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAAe,oBAAoB,OAAO,aAAa;;;;;;0FACvE,6LAAC;gFAAK,WAAU;0FACb,oBAAoB,OAAO,gBAAgB;;;;;;;;;;;;;;;;;8EAMlD,6LAAC;oEAAI,WAAU;8EACZ,oBAAoB,OAAO,gBAAgB;;;;;;8EAE9C,6LAAC;oEACC,WAAW,CAAC,qDAAqD,EAAE,eAAe,WAAW,gFAAgF,gFAAgF;oEAC7P,SAAS;wEACP,cAAc;wEACd,oBAAoB;oEACtB;8EAEA,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAAe,oBAAoB,OAAO,eAAe;;;;;;0FACzE,6LAAC;gFAAK,WAAU;0FACb,oBAAoB,OAAO,eAAe;;;;;;;;;;;;;;;;;8EAIjD,6LAAC;oEACC,WAAW,CAAC,qDAAqD,EAAE,eAAe,aAAa,gFAAgF,gFAAgF;oEAC/P,SAAS;wEACP,cAAc;wEACd,oBAAoB;oEACtB;8EAEA,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAAe,oBAAoB,OAAO,iBAAiB;;;;;;0FAC3E,6LAAC;gFAAK,WAAU;0FACb,oBAAoB,OAAO,YAAY;;;;;;;;;;;;;;;;;8EAI9C,6LAAC;oEACC,WAAW,CAAC,qDAAqD,EAAE,eAAe,cAAc,gFAAgF,gFAAgF;oEAChQ,SAAS;wEACP,cAAc;wEACd,oBAAoB;oEACtB;8EAEA,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAAe,oBAAoB,OAAO,iBAAiB;;;;;;0FAC3E,6LAAC;gFAAK,WAAU;0FACb,oBAAoB,OAAO,iBAAiB;;;;;;;;;;;;;;;;;8EAInD,6LAAC;oEACC,WAAW,CAAC,qDAAqD,EAAE,eAAe,UAAU,gFAAgF,gFAAgF;oEAC5P,SAAS;wEACP,cAAc;wEACd,oBAAoB;oEACtB;8EAEA,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAAe,oBAAoB,OAAO,gBAAgB;;;;;;0FAC1E,6LAAC;gFAAK,WAAU;0FACb,oBAAoB,OAAO,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAU9D,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,sIAAA,CAAA,UAAO;wDAAC,SAAS,oBAAoB,OAAO,aAAa;kEACxD,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAS,aAAa,SAAS,YAAY;4DAC3C,MAAK;4DACL,SAAS,IAAM,YAAY;4DAC3B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8CACA,aAAa,SACT,kDACA;4DAEN,cAAY,oBAAoB,OAAO,aAAa;sEAEpD,cAAA,6LAAC,4MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAGpB,6LAAC,sIAAA,CAAA,UAAO;wDAAC,SAAS,oBAAoB,OAAO,cAAc;kEACzD,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAS,aAAa,SAAS,YAAY;4DAC3C,MAAK;4DACL,SAAS,IAAM,YAAY;4DAC3B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8CACA,aAAa,SACT,kDACA;4DAEN,cAAY,oBAAoB,OAAO,cAAc;sEAErD,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0DAKtB,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,qBAAqB,IAAI,YAAY;gDAC9C,MAAK;gDACL,SAAS,IAAM,qBAAqB,CAAC;gDACrC,WAAU;;kEAEV,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDACjB,oBAAoB,OAAO,YAAY;oDACvC,qBAAqB,mBACpB,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;kEAClC;;;;;;;;;;;;;;;;;;;;;;;;4BAQV,YACC,eAAe;0CACf,6LAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;;;;;;kEAEf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;0DAGnB,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;;;;;;;kEAGjB,6LAAC;wDAAI,WAAU;;;;;;kEAEf,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;;;;;kEAEf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;;;;;;;kEAGjB,6LAAC;wDAAI,WAAU;;;;;;;;;;;;;uCA3BT;;;;;;;;;uCAgCZ,eAAe,MAAM,KAAK,IAC5B,iBAAiB;0CACjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGvB,6LAAC;wCAAG,WAAU;kDACX,oBAAoB,OAAO,mBAAmB;;;;;;kDAEjD,6LAAC;wCAAE,WAAU;kDACV,oBAAoB,OACjB,wHACA;;;;;;kDAEN,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,SAAS;gDAAc,WAAU;;kEACzD,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDACpB,oBAAoB,OAAO,wBAAwB;;;;;;;0DAEtD,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS;oDACP,WAAW;wDACT,UAAU;wDACV,YAAY;4DAAE,KAAK;4DAAG,KAAK,YAAY;wDAAM;wDAC7C,SAAS;wDACT,QAAQ;wDACR,UAAU;wDACV,aAAa;wDACb,aAAa;oDACf;oDACA,cAAc;gDAChB;gDACA,WAAU;;kEAEV,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDACd,oBAAoB,OAAO,uBAAuB;;;;;;;;;;;;;;;;;;uCAKzD,eAAe;0CACf,6LAAC;;kDAGC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAG,WAAU;sDACX,QAAQ,WAAW,GAClB,oBAAoB,OAAO,CAAC,cAAc,EAAE,QAAQ,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,iBAAiB,EAAE,QAAQ,WAAW,CAAC,CAAC,CAAC,GAC7G,QAAQ,QAAQ,GAClB,oBAAoB,OAAO,qBAAqB,sBAC9C,QAAQ,MAAM,GAChB,oBAAoB,OAAO,qBAAqB,kBAC9C,QAAQ,WAAW,GACrB,oBAAoB,OAAO,eAAe,iBACxC,eAAe,WACjB,oBAAoB,OAAO,kBAAkB,oBAC3C,eAAe,YACjB,oBAAoB,OAAO,0BAA0B,0BACnD,eAAe,cACjB,oBAAoB,OAAO,uCAAuC,gCAChE,eAAe,eACjB,oBAAoB,OAAO,uCAAuC,gCAChE,eAAe,aACjB,oBAAoB,OAAO,0BAA0B,2BACnD,eAAe,WACjB,oBAAoB,OAAO,4BAA4B,2BACrD,eAAe,aACjB,oBAAoB,OAAO,2BAA2B,iBACpD,eAAe,cACjB,oBAAoB,OAAO,gCAAgC,iBACzD,eAAe,UACjB,oBAAoB,OAAO,yBAAyB,sBAEpD,oBAAoB,OAAO,kBAAkB;;;;;;;;;;;kDAKnD,6LAAC;wCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2CACA,aAAa,SACT,yDACA;wCAEN,OAAO;4CACL,WAAW,aAAa,SAAS,SAAS;wCAC5C;kDAEC,eAAe,GAAG,CAAC,CAAC,SAAS,sBAC5B,6LAAC;gDAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2CACA,aAAa,SAAS,8BAA8B;gDAEtD,OAAO;oDACL,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;gDACnC;0DAEA,cAAA,6LAAC,+IAAA,CAAA,cAAW;oDACV,SAAS;oDACT,OAAO;oDACP,eAAe;oDACf,eAAe;oDACf,cAAc;oDACd,eAAe;oDACf,aAAa;oDACb,aAAa;oDACb,kBAAkB;oDAClB,oBAAoB;oDACpB,SAAS,aAAa,SAAS,SAAS;oDACxC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sCACA,aAAa,SAAS,8BAA8B;;;;;;+CAvBnD,QAAQ,EAAE;;;;;;;;;;;;;;;;0CAiCzB,6LAAC,2IAAA,CAAA,aAAU;gCACT,eAAe,eAAe,MAAM;gCACpC,aAAa;gCACb,cAAc;gCACd,cAAc,CAAC,OAAS,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,MAAM;;;;;;;;;;;;;;;;;;0BAMpE,6LAAC,sJAAA,CAAA,qBAAkB;gBACjB,SAAS;gBACT,QAAQ,CAAC,CAAC;gBACV,SAAS,IAAM,oBAAoB;;;;;;0BAIrC,6LAAC,oJAAA,CAAA,qBAAkB;gBACjB,QAAQ,qBAAqB,CAAC,CAAC;gBAC/B,SAAS;gBACT,SAAS;oBACP,qBAAqB;oBACrB,mBAAmB;gBACrB;;;;;;YAID,+BACC,6LAAC,0IAAA,CAAA,YAAS;gBACR,SAAS,IAAM,iBAAiB;gBAChC,YAAW;;;;;;YAKd,kCACC,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,iEACA,iDACA,2BACA,cAAc,YAAY,gDAC1B,cAAc,UAAU,4CACxB;0BAEA,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,8DACA,cAAc,YAAY,iBAC1B,cAAc,UAAU,eACxB;;wCAEC,cAAc,2BAAa,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAClD,cAAc,yBAAW,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;wCACtC,cAAc,wBAAU,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;8CAE3C,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDACX,cAAc,YAAa,oBAAoB,OAAO,aAAa,YACnE,cAAc,UAAW,oBAAoB,OAAO,QAAQ,UAC3D,oBAAoB,OAAO,YAAY;;;;;;sDAE3C,6LAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;sCAG1C,6LAAC;4BACC,SAAS,IAAM,oBAAoB;4BACnC,WAAU;4BACV,cAAY,oBAAoB,OAAO,UAAU;sCAEjD,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B;GAt2Ba;;QACI,qIAAA,CAAA,YAAS;QAoFN,6HAAA,CAAA,eAAY;QACR,iIAAA,CAAA,mBAAgB;QACrB,6HAAA,CAAA,eAAY;QACI,mJAAA,CAAA,WAAQ;QACpB,iIAAA,CAAA,mBAAgB;QACf,+HAAA,CAAA,iBAAc;;;KA1FzB", "debugId": null}}]}